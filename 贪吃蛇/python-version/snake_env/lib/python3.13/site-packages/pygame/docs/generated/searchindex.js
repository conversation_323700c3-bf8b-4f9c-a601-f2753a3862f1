Search.setIndex({"alltitles": {"-New Input is new Event": [[70, "new-input-is-new-event"]], "-Setting for Animation": [[69, "setting-for-animation"]], "-\uc0c8\ub85c\uc6b4 \uc785\ub825\uc740 \uc0c8\ub85c\uc6b4 \uc774\ubca4\ud2b8": [[78, "id2"]], "-\ud654\uba74\uc774 \uc6c0\uc9c1\uc774\uae30 \uc704\ud55c \uc870\uac74": [[77, "id2"]], "1. Introduction": [[61, "introduction"]], "1.1. A note on coding styles": [[61, "a-note-on-coding-styles"]], "2. Revision: Pygame fundamentals": [[85, "makegames-2"]], "2.1. The basic Pygame game": [[85, "the-basic-pygame-game"]], "2.2. Basic Pygame objects": [[85, "basic-pygame-objects"]], "2.3. Blitting": [[85, "blitting"]], "2.4. The event loop": [[85, "the-event-loop"]], "2.5. Ta-da!": [[85, "ta-da"]], "3. Kicking things off": [[86, "makegames-3"]], "3.1. The first lines, and loading modules": [[86, "the-first-lines-and-loading-modules"]], "3.2. Resource handling functions": [[86, "resource-handling-functions"]], "4. Game object classes": [[87, "makegames-4"]], "4.1. A simple ball class": [[87, "a-simple-ball-class"]], "4.1.1. Diversion 1: Sprites": [[87, "diversion-1-sprites"]], "4.1.2. Diversion 2: Vector physics": [[87, "diversion-2-vector-physics"]], "5. User-controllable objects": [[88, "makegames-5"]], "5.1. A simple bat class": [[88, "a-simple-bat-class"]], "5.1.1. Diversion 3: Pygame events": [[88, "diversion-3-pygame-events"]], "6. Putting it all together": [[89, "makegames-6"]], "6.1. Let the ball hit sides": [[89, "let-the-ball-hit-sides"]], "6.2. Let the ball hit bats": [[89, "let-the-ball-hit-bats"]], "6.3. The Finished product": [[89, "the-finished-product"]], "A Newbie Guide to pygame": [[84, "a-newbie-guide-to-pygame"]], "API exported by pygame._freetype": [[6, null]], "API exported by pygame.display": [[4, null]], "API exported by pygame.event": [[5, null]], "API exported by pygame.mixer": [[7, null]], "API exported by pygame.rwobject": [[9, null]], "API exported by pygame.surflock": [[12, null]], "API exported by pygame.version": [[13, null]], "Adding the User Input": [[62, "adding-the-user-input"]], "Author: Youngwook Kim (Korean)": [[67, null], [68, null], [69, null], [70, null], [71, null], [72, null], [73, null], [74, null], [75, null], [76, null], [77, null], [78, null], [79, null], [80, null], [81, null], [82, null]], "Basic Computer Vision": [[57, "basic-computer-vision"]], "Basic INPUT": [[70, "basic-input"]], "Basic PROCESS": [[69, "basic-process"]], "Basic TEMPLATE and OUTPUT": [[68, "basic-template-and-output"]], "Be wary of outdated, obsolete, and optional advice.": [[84, "be-wary-of-outdated-obsolete-and-optional-advice"]], "CLOSING": [[63, "closing"]], "Camera Module Introduction": [[57, "camera-module-introduction"]], "Capturing a Live Stream": [[57, "capturing-a-live-stream"]], "Capturing a Single Image": [[57, "capturing-a-single-image"]], "Changing The Background": [[62, "changing-the-background"]], "Class BufferProxy API exported by pygame.bufferproxy": [[2, null]], "Class Color API exported by pygame.color": [[3, null]], "Class Rect API exported by pygame.rect": [[8, null]], "Class Surface API exported by pygame.surface": [[11, null]], "Collision Detection": [[64, "collision-detection"]], "Colorkey vs. Alpha.": [[84, "colorkey-vs-alpha"]], "Colorspaces": [[57, "colorspaces"]], "Common Controller Axis Mappings": [[32, "common-controller-axis-mappings"]], "Common Problems": [[64, "common-problems"]], "Contact: <EMAIL>": [[67, "contact-rumia0601-gmail-com"], [68, "contact-rumia0601-gmail-com"], [69, "contact-rumia0601-gmail-com"], [70, "contact-rumia0601-gmail-com"], [71, "contact-rumia0601-gmail-com"], [72, "contact-rumia0601-gmail-com"], [73, "contact-rumia0601-gmail-com"], [74, "contact-rumia0601-gmail-com"], [75, "contact-rumia0601-gmail-com"], [76, "contact-rumia0601-gmail-com"], [77, "contact-rumia0601-gmail-com"], [78, "contact-rumia0601-gmail-com"], [79, "contact-rumia0601-gmail-com"], [80, "contact-rumia0601-gmail-com"], [81, "contact-rumia0601-gmail-com"], [82, "contact-rumia0601-gmail-com"]], "Create The Background": [[58, "create-the-background"]], "Creating A Map": [[62, "creating-a-map"]], "Definition: \"blit\"": [[62, "definition-blit"]], "Display The Background While Setup Finishes": [[58, "display-the-background-while-setup-finishes"]], "Do things the pythony way.": [[84, "do-things-the-pythony-way"]], "Documents": [[15, "documents"]], "Don't bother with pixel-perfect collision detection.": [[84, "don-t-bother-with-pixel-perfect-collision-detection"]], "Don't get distracted by side issues.": [[84, "don-t-get-distracted-by-side-issues"]], "Draw The Entire Scene": [[58, "draw-the-entire-scene"]], "Epilog": [[74, "epilog"]], "Examples": [[59, "examples"], [65, "examples"]], "Extending Your Own Classes (Advanced)": [[64, "extending-your-own-classes-advanced"]], "File Path Function Arguments": [[14, null]], "First, The Mystery Functions": [[62, "first-the-mystery-functions"]], "Functions": [[59, "functions"]], "Game Object Classes": [[58, "game-object-classes"]], "Game Over": [[58, "game-over"]], "Game object classes": [[87, null]], "Get comfortable working in Python.": [[84, "get-comfortable-working-in-python"]], "Going From The List To The Screen": [[62, "going-from-the-list-to-the-screen"]], "Graduation": [[65, "graduation"]], "HISTORY": [[63, "history"]], "Handle All Input Events": [[58, "handle-all-input-events"]], "Handling Some Input": [[62, "handling-some-input"]], "Help! How Do I Move An Image?": [[62, "help-how-do-i-move-an-image"]], "High level API exported by pygame.base": [[1, null]], "History Lesson": [[64, "history-lesson"]], "How to Decide": [[59, "how-to-decide"]], "Import": [[60, "import"]], "Import Modules": [[58, "import-modules"]], "Import Surfarray": [[65, "import-surfarray"]], "Import and Init": [[57, "import-and-init"]], "Import and Initialize": [[60, "import-and-initialize"]], "Init": [[60, "init"]], "Initialize Everything": [[58, "initialize-everything"]], "Introduction": [[58, "introduction"], [59, "introduction"], [65, "introduction"]], "Just Pixels On The Screen": [[62, "just-pixels-on-the-screen"]], "Kicking things off": [[86, null]], "Know what a surface is.": [[84, "know-what-a-surface-is"]], "Legacy logos": [[16, "legacy-logos"]], "Let's Go Back A Step": [[62, "let-s-go-back-a-step"]], "Line By Line Chimp": [[58, "line-by-line-chimp"]], "Listing Connected Cameras": [[57, "listing-connected-cameras"]], "Loading Resources": [[58, "loading-resources"]], "Main Loop": [[58, "main-loop"]], "Making Games With Pygame": [[61, null], [61, "id1"]], "Making The Hero Move": [[62, "making-the-hero-move"]], "Making The Hero Move (Take 2)": [[62, "making-the-hero-move-take-2"]], "Managing the event subsystem.": [[84, "managing-the-event-subsystem"]], "Mixing Them Together": [[64, "mixing-them-together"]], "More Advanced NumPy": [[65, "more-advanced-numpy"]], "Moving Multiple Images": [[62, "moving-multiple-images"]], "Named Colors": [[21, null]], "Newbie Guide to Pygame": [[84, null]], "Nintendo Switch Left Joy-Con (pygame 2.x)": [[32, "nintendo-switch-left-joy-con-pygame-2-x"]], "Nintendo Switch Pro Controller (pygame 2.x)": [[32, "nintendo-switch-pro-controller-pygame-2-x"]], "Nintendo Switch Right Joy-Con (pygame 2.x)": [[32, "nintendo-switch-right-joy-con-pygame-2-x"]], "NumPy": [[65, "numpy"]], "Other Surfarray Functions": [[65, "other-surfarray-functions"]], "PYTHON AND GAMING": [[63, "python-and-gaming"]], "Playstation 4 Controller (pygame 1.x)": [[32, "playstation-4-controller-pygame-1-x"]], "Playstation 4 Controller (pygame 2.x)": [[32, "playstation-4-controller-pygame-2-x"]], "Playstation 5 Controller (pygame 2.x)": [[32, "playstation-5-controller-pygame-2-x"]], "Prepare Game Object": [[58, "prepare-game-object"]], "Preparing for Improved User Input": [[62, "preparing-for-improved-user-input"]], "Prolog? Why Pygame?": [[67, "prolog-why-pygame"]], "Put Text On The Background, Centered": [[58, "put-text-on-the-background-centered"]], "Putting It All Together": [[62, "putting-it-all-together"]], "Putting it all Together One More time": [[62, "putting-it-all-together-one-more-time"]], "Putting it all together": [[89, null]], "Pygame Front Page": [[15, null]], "Pygame Intro": [[63, null]], "Pygame Logos": [[16, "pygame-logos"]], "Pygame Logos Page": [[16, null]], "Pygame Modules Overview": [[63, "pygame-modules-overview"]], "Pygame Tutorials - Camera Module Introduction": [[57, null]], "Pygame Tutorials - Help! How Do I Move An Image?": [[62, null]], "Pygame Tutorials - Import and Initialize": [[60, null]], "Pygame Tutorials - Line By Line Chimp Example": [[58, null]], "Pygame Tutorials - Setting Display Modes": [[59, null]], "Pygame Tutorials - Sprite Module Introduction": [[64, null]], "Pygame Tutorials - Surfarray Introduction": [[65, null]], "Python Pygame Introduction": [[63, "python-pygame-introduction"]], "Quick start": [[15, "quick-start"]], "Quit": [[60, "quit"]], "Recognize which parts of pygame you really need.": [[84, "recognize-which-parts-of-pygame-you-really-need"]], "Rects are your friends.": [[84, "rects-are-your-friends"]], "Reference": [[15, "reference"]], "Revision: Pygame fundamentals": [[85, null]], "Screen Coordinates": [[62, "screen-coordinates"]], "Setting Basics": [[59, "setting-basics"]], "Setting Display Modes": [[59, "setting-display-modes"]], "Slots and c_api - Making functions and data available from other modules": [[10, null]], "Smooth Movement": [[62, "smooth-movement"]], "So, What Next?": [[62, "so-what-next"]], "Software architecture, design patterns, and games.": [[84, "software-architecture-design-patterns-and-games"]], "Sprite Module Introduction": [[64, "sprite-module-introduction"]], "Surface Locking": [[65, "surface-locking"]], "Surfarray Introduction": [[65, "surfarray-introduction"], [65, "id1"]], "TASTE": [[63, "taste"]], "Table of Contents": [[61, "table-of-contents"]], "The Classes": [[64, "the-classes"]], "The Group Class": [[64, "the-group-class"]], "The Many Group Types": [[64, "the-many-group-types"]], "The Rendering Groups": [[64, "the-rendering-groups"]], "The Sprite Class": [[64, "the-sprite-class"]], "There is NO rule six.": [[84, "there-is-no-rule-six"]], "Thresholding": [[57, "thresholding"]], "Transparency": [[65, "transparency"]], "Tutorials": [[15, "tutorials"]], "Update the Sprites": [[58, "update-the-sprites"]], "Use Surface.convert().": [[84, "use-surface-convert"]], "User-controllable objects": [[88, null]], "Using Camera Controls": [[57, "using-camera-controls"]], "Using the Mask Module": [[57, "using-the-mask-module"]], "XBox 360 Controller (pygame 1.x)": [[32, "xbox-360-controller-pygame-1-x"]], "XBox 360 Controller (pygame 2.x)": [[32, "xbox-360-controller-pygame-2-x"]], "You Are On Your Own From Here": [[62, "you-are-on-your-own-from-here"]], "and plus alpha": [[73, "and-plus-alpha"]], "pygame C API": [[0, null]], "pygame.BufferProxypygame object to export a surface buffer through an array protocol": [[17, null]], "pygame.Colorpygame object for color representations": [[20, null]], "pygame.Overlaypygame object for video overlay graphics": [[41, null]], "pygame.PixelArraypygame object for direct pixel access of surfaces": [[42, null]], "pygame.Rectpygame object for storing rectangular coordinates": [[45, null]], "pygame.Surfacepygame object for representing images": [[51, null]], "pygame._sdl2.controllerPygame module to work with controllers.": [[47, null]], "pygame._sdl2.touchpygame module to work with touch input": [[55, null]], "pygame.camerapygame module for camera use": [[18, null]], "pygame.cdrompygame module for audio cdrom control": [[19, null]], "pygame.cursorspygame module for cursor resources": [[22, null]], "pygame.displaypygame module to control the display window and screen": [[23, null]], "pygame.drawpygame module for drawing shapes": [[24, null]], "pygame.eventpygame module for interacting with events and queues": [[25, null]], "pygame.examplesmodule of example programs": [[26, null]], "pygame.fasteventpygame module for interacting with events and queues": [[27, null]], "pygame.fontpygame module for loading and rendering fonts": [[28, null]], "pygame.freetypeEnhanced pygame module for loading and rendering computer fonts": [[29, null]], "pygame.gfxdrawpygame module for drawing shapes": [[30, null]], "pygame.imagepygame module for image transfer": [[31, null]], "pygame.joystickPygame module for interacting with joysticks, gamepads, and trackballs.": [[32, null]], "pygame.keypygame module to work with the keyboard": [[33, null]], "pygame.localspygame constants": [[34, null]], "pygame.maskpygame module for image masks.": [[35, null]], "pygame.mathpygame module for vector classes": [[36, null]], "pygame.midipygame module for interacting with midi input and output.": [[37, null]], "pygame.mixer.musicpygame module for controlling streamed audio": [[40, null]], "pygame.mixerpygame module for loading and playing sounds": [[38, null]], "pygame.mousepygame module to work with the mouse": [[39, null]], "pygame.pixelcopypygame module for general pixel array copying": [[43, null]], "pygame.scrappygame module for clipboard support.": [[46, null]], "pygame.sdl2_video": [[48, null]], "pygame.sndarraypygame module for accessing sound sample data": [[49, null]], "pygame.spritepygame module with basic game object classes": [[50, null]], "pygame.surfarraypygame module for accessing surface pixel data using array interfaces": [[52, null]], "pygame.testsPygame unit test suite package": [[53, null]], "pygame.timepygame module for monitoring time": [[54, null]], "pygame.transformpygame module to transform surfaces": [[56, null]], "pygame.versionsmall module containing version information": [[44, "module-pygame.version"]], "pygame/examples/chimp.py": [[66, null]], "pygamethe top level pygame package": [[44, null]], "src_c/_freetype.c": [[6, "src-c-freetype-c"]], "src_c/base.c": [[1, "src-c-base-c"]], "src_c/bufferproxy.c": [[2, "src-c-bufferproxy-c"]], "src_c/color.c": [[3, "src-c-color-c"]], "src_c/display.c": [[4, "src-c-display-c"]], "src_c/event.c": [[5, "src-c-event-c"]], "src_c/mixer.c": [[7, "src-c-mixer-c"]], "src_c/rect.c": [[8, "src-c-rect-c"]], "src_c/rwobject.c": [[9, "src-c-rwobject-c"]], "src_c/surface.c": [[11, "src-c-surface-c"]], "src_c/surflock.c": [[12, "src-c-surflock-c"]], "src_py/version.py": [[13, "src-py-version-py"]], "with Advanced OUTPUT \u2013 Buttons": [[72, "with-advanced-output-buttons"]], "with Advanced PROCESS - Functionalization": [[71, "with-advanced-process-functionalization"]], "\u2013 \uc65c \ud558\ud544 \ud30c\uc774\uac8c\uc784?": [[75, "id2"]], "\u2013Into Event-driven and GUI": [[68, "into-event-driven-and-gui"]], "\u2013\uc774\ubca4\ud2b8 \uae30\ubc18\uacfc GUI \uae30\ubc18\uc73c\ub85c\uc758 \uc785\ubb38": [[76, "gui"]], "\uadf8\ub9ac\uace0 \uc2ec\ud654 \ucc98\ub9ac - \ud568\uc218\ud654": [[79, "id1"]], "\uadf8\ub9ac\uace0 \uc2ec\ud654 \ucd9c\ub825 \u2013 \ubc84\ud2bc": [[80, "id1"]], "\uadf8\ub9ac\uace0 \uc870\uae08 \ub354!": [[81, "id1"]], "\uae30\ubcf8 \ud615\uc2dd\uacfc \uae30\ucd08 \ucd9c\ub825": [[76, "id1"]], "\uae30\ucd08 \uc785\ub825": [[78, "id1"]], "\uae30\ucd08 \ucc98\ub9ac": [[77, "id1"]], "\uc5d0\ud544\ub85c\uadf8": [[82, "id1"]], "\ud504\ub864\ub85c\uadf8": [[75, "id1"]], "\ud55c\uad6d\uc5b4 \ud29c\ud1a0\ub9ac\uc5bc": [[83, null], [83, "id2"]]}, "docnames": ["c_api", "c_api/base", "c_api/bufferproxy", "c_api/color", "c_api/display", "c_api/event", "c_api/freetype", "c_api/mixer", "c_api/rect", "c_api/rwobject", "c_api/slots", "c_api/surface", "c_api/surflock", "c_api/version", "filepaths", "index", "logos", "ref/bufferproxy", "ref/camera", "ref/cdrom", "ref/color", "ref/color_list", "ref/cursors", "ref/display", "ref/draw", "ref/event", "ref/examples", "ref/fastevent", "ref/font", "ref/freetype", "ref/gfxdraw", "ref/image", "ref/joystick", "ref/key", "ref/locals", "ref/mask", "ref/math", "ref/midi", "ref/mixer", "ref/mouse", "ref/music", "ref/overlay", "ref/pixelarray", "ref/pixelcopy", "ref/pygame", "ref/rect", "ref/scrap", "ref/sdl2_controller", "ref/sdl2_video", "ref/sndarray", "ref/sprite", "ref/surface", "ref/surfarray", "ref/tests", "ref/time", "ref/touch", "ref/transform", "tut/CameraIntro", "tut/ChimpLineByLine", "tut/DisplayModes", "tut/ImportInit", "tut/MakeGames", "tut/MoveIt", "tut/PygameIntro", "tut/SpriteIntro", "tut/SurfarrayIntro", "tut/chimp.py", "tut/en/Red_or_Black/1.Prolog/introduction", "tut/en/Red_or_Black/2.Print_text/Basic TEMPLATE and OUTPUT", "tut/en/Red_or_Black/3.Move_text/Basic PROCESS", "tut/en/Red_or_Black/4.Control_text/Basic INPUT", "tut/en/Red_or_Black/5.HP_bar/Advanced OUTPUT with Advanced PROCESS", "tut/en/Red_or_Black/6.Buttons/Advanced INPUT with Advanced OUTPUT", "tut/en/Red_or_Black/7.Game_board/Advanced OUTPUT and plus alpha", "tut/en/Red_or_Black/8.Epilog/Epilog", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/1.\ud504\ub864\ub85c\uadf8/\uc18c\uac1c", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/2.\ud14d\uc2a4\ud2b8 \ucd9c\ub825/\uae30\ucd08 \ud15c\ud50c\ub9bf\uacfc \ucd9c\ub825", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/3.\ud14d\uc2a4\ud2b8 \uc774\ub3d9/\uae30\ucd08 \ucc98\ub9ac", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/4.\ud14d\uc2a4\ud2b8 \uc870\uc885/\uae30\ucd08 \uc785\ub825", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/5.HP\ubc14/\uc2ec\ud654 \ucd9c\ub825 \uadf8\ub9ac\uace0 \uc2ec\ud654 \ucc98\ub9ac", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/6.\ubc84\ud2bc\ub4e4/\uc2ec\ud654 \uc785\ub825 \uadf8\ub9ac\uace0 \uc2ec\ud654 \ucd9c\ub825", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/7.\uac8c\uc784\ud310/\uc2ec\ud654 \ucd9c\ub825 \uadf8\ub9ac\uace0 \uc870\uae08 \ub354", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/8.\uc5d0\ud544\ub85c\uadf8/\uc5d0\ud544\ub85c\uadf8", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/\uac1c\uc694", "tut/newbieguide", "tut/tom_games2", "tut/tom_games3", "tut/tom_games4", "tut/tom_games5", "tut/tom_games6"], "envversion": {"sphinx": 63, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2}, "filenames": ["c_api.rst", "c_api/base.rst", "c_api/bufferproxy.rst", "c_api/color.rst", "c_api/display.rst", "c_api/event.rst", "c_api/freetype.rst", "c_api/mixer.rst", "c_api/rect.rst", "c_api/rwobject.rst", "c_api/slots.rst", "c_api/surface.rst", "c_api/surflock.rst", "c_api/version.rst", "filepaths.rst", "index.rst", "logos.rst", "ref/bufferproxy.rst", "ref/camera.rst", "ref/cdrom.rst", "ref/color.rst", "ref/color_list.rst", "ref/cursors.rst", "ref/display.rst", "ref/draw.rst", "ref/event.rst", "ref/examples.rst", "ref/fastevent.rst", "ref/font.rst", "ref/freetype.rst", "ref/gfxdraw.rst", "ref/image.rst", "ref/joystick.rst", "ref/key.rst", "ref/locals.rst", "ref/mask.rst", "ref/math.rst", "ref/midi.rst", "ref/mixer.rst", "ref/mouse.rst", "ref/music.rst", "ref/overlay.rst", "ref/pixelarray.rst", "ref/pixelcopy.rst", "ref/pygame.rst", "ref/rect.rst", "ref/scrap.rst", "ref/sdl2_controller.rst", "ref/sdl2_video.rst", "ref/sndarray.rst", "ref/sprite.rst", "ref/surface.rst", "ref/surfarray.rst", "ref/tests.rst", "ref/time.rst", "ref/touch.rst", "ref/transform.rst", "tut/CameraIntro.rst", "tut/ChimpLineByLine.rst", "tut/DisplayModes.rst", "tut/ImportInit.rst", "tut/MakeGames.rst", "tut/MoveIt.rst", "tut/PygameIntro.rst", "tut/SpriteIntro.rst", "tut/SurfarrayIntro.rst", "tut/chimp.py.rst", "tut/en/Red_or_Black/1.Prolog/introduction.rst", "tut/en/Red_or_Black/2.Print_text/Basic TEMPLATE and OUTPUT.rst", "tut/en/Red_or_Black/3.Move_text/Basic PROCESS.rst", "tut/en/Red_or_Black/4.Control_text/Basic INPUT.rst", "tut/en/Red_or_Black/5.HP_bar/Advanced OUTPUT with Advanced PROCESS.rst", "tut/en/Red_or_Black/6.Buttons/Advanced INPUT with Advanced OUTPUT.rst", "tut/en/Red_or_Black/7.Game_board/Advanced OUTPUT and plus alpha.rst", "tut/en/Red_or_Black/8.Epilog/Epilog.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/1.\ud504\ub864\ub85c\uadf8/\uc18c\uac1c.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/2.\ud14d\uc2a4\ud2b8 \ucd9c\ub825/\uae30\ucd08 \ud15c\ud50c\ub9bf\uacfc \ucd9c\ub825.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/3.\ud14d\uc2a4\ud2b8 \uc774\ub3d9/\uae30\ucd08 \ucc98\ub9ac.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/4.\ud14d\uc2a4\ud2b8 \uc870\uc885/\uae30\ucd08 \uc785\ub825.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/5.HP\ubc14/\uc2ec\ud654 \ucd9c\ub825 \uadf8\ub9ac\uace0 \uc2ec\ud654 \ucc98\ub9ac.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/6.\ubc84\ud2bc\ub4e4/\uc2ec\ud654 \uc785\ub825 \uadf8\ub9ac\uace0 \uc2ec\ud654 \ucd9c\ub825.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/7.\uac8c\uc784\ud310/\uc2ec\ud654 \ucd9c\ub825 \uadf8\ub9ac\uace0 \uc870\uae08 \ub354.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/8.\uc5d0\ud544\ub85c\uadf8/\uc5d0\ud544\ub85c\uadf8.rst", "tut/ko/\ube68\uac04\ube14\ub85d \uac80\uc740\ube14\ub85d/\uac1c\uc694.rst", "tut/newbieguide.rst", "tut/tom_games2.rst", "tut/tom_games3.rst", "tut/tom_games4.rst", "tut/tom_games5.rst", "tut/tom_games6.rst"], "indexentries": {"__dict__ (pygame.event.event attribute)": [[25, "pygame.event.Event.__dict__", false]], "_pixels_address (pygame.surface attribute)": [[51, "pygame.Surface._pixels_address", false]], "a (pygame.color attribute)": [[20, "pygame.Color.a", false]], "aacircle() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.aacircle", false]], "aaellipse() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.aaellipse", false]], "aaline() (in module pygame.draw)": [[24, "pygame.draw.aaline", false]], "aalines() (in module pygame.draw)": [[24, "pygame.draw.aalines", false]], "aapolygon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.aapolygon", false]], "aatrigon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.aatrigon", false]], "abort() (pygame.midi.output method)": [[37, "pygame.midi.Output.abort", false]], "add() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.add", false]], "add() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.add", false]], "add() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.add", false]], "aliens.main() (in module pygame.examples)": [[26, "pygame.examples.aliens.main", false]], "alive() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.alive", false]], "alpha (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.alpha", false]], "alpha (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.alpha", false]], "angle (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.angle", false]], "angle() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.angle", false]], "angle_to() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.angle_to", false]], "angle_to() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.angle_to", false]], "antialiased (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.antialiased", false]], "arc() (in module pygame.draw)": [[24, "pygame.draw.arc", false]], "arc() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.arc", false]], "array() (in module pygame.sndarray)": [[49, "pygame.sndarray.array", false]], "array2d() (in module pygame.surfarray)": [[52, "pygame.surfarray.array2d", false]], "array3d() (in module pygame.surfarray)": [[52, "pygame.surfarray.array3d", false]], "array_alpha() (in module pygame.surfarray)": [[52, "pygame.surfarray.array_alpha", false]], "array_blue() (in module pygame.surfarray)": [[52, "pygame.surfarray.array_blue", false]], "array_colorkey() (in module pygame.surfarray)": [[52, "pygame.surfarray.array_colorkey", false]], "array_green() (in module pygame.surfarray)": [[52, "pygame.surfarray.array_green", false]], "array_red() (in module pygame.surfarray)": [[52, "pygame.surfarray.array_red", false]], "array_to_surface() (in module pygame.pixelcopy)": [[43, "pygame.pixelcopy.array_to_surface", false]], "arraydemo.main() (in module pygame.examples)": [[26, "pygame.examples.arraydemo.main", false]], "as_joystick() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.as_joystick", false]], "as_polar() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.as_polar", false]], "as_spherical() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.as_spherical", false]], "ascender (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.ascender", false]], "attached() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.attached", false]], "average_color() (in module pygame.transform)": [[56, "pygame.transform.average_color", false]], "average_surfaces() (in module pygame.transform)": [[56, "pygame.transform.average_surfaces", false]], "b (pygame.color attribute)": [[20, "pygame.Color.b", false]], "bezier() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.bezier", false]], "bgcolor (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.bgcolor", false]], "blend_fill.main() (in module pygame.examples)": [[26, "pygame.examples.blend_fill.main", false]], "blend_mode (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.blend_mode", false]], "blend_mode (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.blend_mode", false]], "blit() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.blit", false]], "blit() (pygame.surface method)": [[51, "pygame.Surface.blit", false]], "blit_array() (in module pygame.surfarray)": [[52, "pygame.surfarray.blit_array", false]], "blit_blends.main() (in module pygame.examples)": [[26, "pygame.examples.blit_blends.main", false]], "blits() (pygame.surface method)": [[51, "pygame.Surface.blits", false]], "bold (pygame.font.font attribute)": [[28, "pygame.font.Font.bold", false]], "borderless (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.borderless", false]], "box() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.box", false]], "bufferproxy (class in pygame)": [[17, "pygame.BufferProxy", false]], "camera (class in pygame.camera)": [[18, "pygame.camera.Camera", false]], "camera.main() (in module pygame.examples)": [[26, "pygame.examples.camera.main", false]], "cd (class in pygame.cdrom)": [[19, "pygame.cdrom.CD", false]], "centroid() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.centroid", false]], "change_layer() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.change_layer", false]], "change_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.change_layer", false]], "channel (class in pygame.mixer)": [[38, "pygame.mixer.Channel", false]], "chimp.main() (in module pygame.examples)": [[26, "pygame.examples.chimp.main", false]], "chop() (in module pygame.transform)": [[56, "pygame.transform.chop", false]], "circle() (in module pygame.draw)": [[24, "pygame.draw.circle", false]], "circle() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.circle", false]], "clamp() (in module pygame.math)": [[36, "pygame.math.clamp", false]], "clamp() (pygame.rect method)": [[45, "pygame.Rect.clamp", false]], "clamp_ip() (pygame.rect method)": [[45, "pygame.Rect.clamp_ip", false]], "clamp_magnitude() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.clamp_magnitude", false]], "clamp_magnitude() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.clamp_magnitude", false]], "clamp_magnitude_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.clamp_magnitude_ip", false]], "clamp_magnitude_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.clamp_magnitude_ip", false]], "clear() (in module pygame.event)": [[25, "pygame.event.clear", false]], "clear() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.clear", false]], "clear() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.clear", false]], "clear() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.clear", false]], "clear() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.clear", false]], "clip() (pygame.rect method)": [[45, "pygame.Rect.clip", false]], "clipline() (pygame.rect method)": [[45, "pygame.Rect.clipline", false]], "clock (class in pygame.time)": [[54, "pygame.time.Clock", false]], "close() (pygame.midi.input method)": [[37, "pygame.midi.Input.close", false]], "close() (pygame.midi.output method)": [[37, "pygame.midi.Output.close", false]], "close() (pygame.pixelarray method)": [[42, "pygame.PixelArray.close", false]], "cmy (pygame.color attribute)": [[20, "pygame.Color.cmy", false]], "collide_circle() (in module pygame.sprite)": [[50, "pygame.sprite.collide_circle", false]], "collide_circle_ratio() (in module pygame.sprite)": [[50, "pygame.sprite.collide_circle_ratio", false]], "collide_mask() (in module pygame.sprite)": [[50, "pygame.sprite.collide_mask", false]], "collide_rect() (in module pygame.sprite)": [[50, "pygame.sprite.collide_rect", false]], "collide_rect_ratio() (in module pygame.sprite)": [[50, "pygame.sprite.collide_rect_ratio", false]], "collidedict() (pygame.rect method)": [[45, "pygame.Rect.collidedict", false]], "collidedictall() (pygame.rect method)": [[45, "pygame.Rect.collidedictall", false]], "collidelist() (pygame.rect method)": [[45, "pygame.Rect.collidelist", false]], "collidelistall() (pygame.rect method)": [[45, "pygame.Rect.collidelistall", false]], "collideobjects() (pygame.rect method)": [[45, "pygame.Rect.collideobjects", false]], "collideobjectsall() (pygame.rect method)": [[45, "pygame.Rect.collideobjectsall", false]], "collidepoint() (pygame.rect method)": [[45, "pygame.Rect.collidepoint", false]], "colliderect() (pygame.rect method)": [[45, "pygame.Rect.colliderect", false]], "color (class in pygame)": [[20, "pygame.Color", false]], "color (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.color", false]], "color (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.color", false]], "colorspace() (in module pygame.camera)": [[18, "pygame.camera.colorspace", false]], "compare() (pygame.pixelarray method)": [[42, "pygame.PixelArray.compare", false]], "compile() (in module pygame.cursors)": [[22, "pygame.cursors.compile", false]], "connected_component() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.connected_component", false]], "connected_components() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.connected_components", false]], "contains() (in module pygame.scrap)": [[46, "pygame.scrap.contains", false]], "contains() (pygame.rect method)": [[45, "pygame.Rect.contains", false]], "controller (class in pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.Controller", false]], "convert() (pygame.surface method)": [[51, "pygame.Surface.convert", false]], "convert_alpha() (pygame.surface method)": [[51, "pygame.Surface.convert_alpha", false]], "convolve() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.convolve", false]], "copy() (pygame.cursors.cursor method)": [[22, "pygame.cursors.Cursor.copy", false]], "copy() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.copy", false]], "copy() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.copy", false]], "copy() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.copy", false]], "copy() (pygame.rect method)": [[45, "pygame.Rect.copy", false]], "copy() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.copy", false]], "copy() (pygame.surface method)": [[51, "pygame.Surface.copy", false]], "correct_gamma() (pygame.color method)": [[20, "pygame.Color.correct_gamma", false]], "count() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.count", false]], "cross() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.cross", false]], "cross() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.cross", false]], "cursor (class in pygame.cursors)": [[22, "pygame.cursors.Cursor", false]], "cursors.main() (in module pygame.examples)": [[26, "pygame.examples.cursors.main", false]], "custom_type() (in module pygame.event)": [[25, "pygame.event.custom_type", false]], "data (pygame.cursors.cursor attribute)": [[22, "pygame.cursors.Cursor.data", false]], "delay() (in module pygame.time)": [[54, "pygame.time.delay", false]], "descender (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.descender", false]], "destroy() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.destroy", false]], "dirtysprite (class in pygame.sprite)": [[50, "pygame.sprite.DirtySprite", false]], "display() (pygame.overlay method)": [[41, "pygame.Overlay.display", false]], "display_index (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.display_index", false]], "distance_squared_to() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.distance_squared_to", false]], "distance_squared_to() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.distance_squared_to", false]], "distance_to() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.distance_to", false]], "distance_to() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.distance_to", false]], "dot() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.dot", false]], "dot() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.dot", false]], "draw() (pygame._sdl2.video.image method)": [[48, "pygame._sdl2.video.Image.draw", false]], "draw() (pygame._sdl2.video.texture method)": [[48, "pygame._sdl2.video.Texture.draw", false]], "draw() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.draw", false]], "draw() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.draw", false]], "draw() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.draw", false]], "draw() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.draw", false]], "draw() (pygame.sprite.renderupdates method)": [[50, "pygame.sprite.RenderUpdates.draw", false]], "draw_blend_mode (pygame._sdl2.video.renderer attribute)": [[48, "pygame._sdl2.video.Renderer.draw_blend_mode", false]], "draw_color (pygame._sdl2.video.renderer attribute)": [[48, "pygame._sdl2.video.Renderer.draw_color", false]], "draw_line() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.draw_line", false]], "draw_point() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.draw_point", false]], "draw_rect() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.draw_rect", false]], "eject() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.eject", false]], "elementwise() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.elementwise", false]], "elementwise() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.elementwise", false]], "ellipse() (in module pygame.draw)": [[24, "pygame.draw.ellipse", false]], "ellipse() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.ellipse", false]], "empty() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.empty", false]], "encode_file_path() (in module pygame)": [[44, "pygame.encode_file_path", false]], "encode_string() (in module pygame)": [[44, "pygame.encode_string", false]], "epsilon (pygame.math.vector2 attribute)": [[36, "pygame.math.Vector2.epsilon", false]], "epsilon (pygame.math.vector3 attribute)": [[36, "pygame.math.Vector3.epsilon", false]], "erase() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.erase", false]], "error": [[44, "pygame.error", false]], "event (class in pygame.event)": [[25, "pygame.event.Event", false]], "event_name() (in module pygame.event)": [[25, "pygame.event.event_name", false]], "eventlist.main() (in module pygame.examples)": [[26, "pygame.examples.eventlist.main", false]], "extract() (pygame.pixelarray method)": [[42, "pygame.PixelArray.extract", false]], "fadeout() (in module pygame.mixer)": [[38, "pygame.mixer.fadeout", false]], "fadeout() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.fadeout", false]], "fadeout() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.fadeout", false]], "fadeout() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.fadeout", false]], "fgcolor (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.fgcolor", false]], "fill() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.fill", false]], "fill() (pygame.surface method)": [[51, "pygame.Surface.fill", false]], "fill_rect() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.fill_rect", false]], "filled_circle() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.filled_circle", false]], "filled_ellipse() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.filled_ellipse", false]], "filled_polygon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.filled_polygon", false]], "filled_trigon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.filled_trigon", false]], "find_channel() (in module pygame.mixer)": [[38, "pygame.mixer.find_channel", false]], "fit() (pygame.rect method)": [[45, "pygame.Rect.fit", false]], "fixed_sizes (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.fixed_sizes", false]], "fixed_width (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.fixed_width", false]], "flip() (in module pygame.display)": [[23, "pygame.display.flip", false]], "flip() (in module pygame.transform)": [[56, "pygame.transform.flip", false]], "flip_x (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.flip_x", false]], "flip_y (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.flip_y", false]], "focus() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.focus", false]], "font (class in pygame.font)": [[28, "pygame.font.Font", false]], "font (class in pygame.freetype)": [[29, "pygame.freetype.Font", false]], "fonty.main() (in module pygame.examples)": [[26, "pygame.examples.fonty.main", false]], "freetype_misc.main() (in module pygame.examples)": [[26, "pygame.examples.freetype_misc.main", false]], "frequency_to_midi() (in module pygame.midi)": [[37, "pygame.midi.frequency_to_midi", false]], "from_display_module() (pygame._sdl2.video.window class method)": [[48, "pygame._sdl2.video.Window.from_display_module", false]], "from_joystick() (pygame._sdl2.controller.controller static method)": [[47, "pygame._sdl2.controller.Controller.from_joystick", false]], "from_polar() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.from_polar", false]], "from_spherical() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.from_spherical", false]], "from_surface() (in module pygame.mask)": [[35, "pygame.mask.from_surface", false]], "from_surface() (pygame._sdl2.video.texture static method)": [[48, "pygame._sdl2.video.Texture.from_surface", false]], "from_threshold() (in module pygame.mask)": [[35, "pygame.mask.from_threshold", false]], "from_window() (pygame._sdl2.video.renderer class method)": [[48, "pygame._sdl2.video.Renderer.from_window", false]], "from_window() (pygame._sdl2.video.window class method)": [[48, "pygame._sdl2.video.Window.from_window", false]], "frombuffer() (in module pygame.image)": [[31, "pygame.image.frombuffer", false]], "frombytes() (in module pygame.image)": [[31, "pygame.image.frombytes", false]], "fromstring() (in module pygame.image)": [[31, "pygame.image.fromstring", false]], "g (pygame.color attribute)": [[20, "pygame.Color.g", false]], "get() (in module pygame.event)": [[25, "pygame.event.get", false]], "get() (in module pygame.fastevent)": [[27, "pygame.fastevent.get", false]], "get() (in module pygame.scrap)": [[46, "pygame.scrap.get", false]], "get_abs_offset() (pygame.surface method)": [[51, "pygame.Surface.get_abs_offset", false]], "get_abs_parent() (pygame.surface method)": [[51, "pygame.Surface.get_abs_parent", false]], "get_active() (in module pygame.display)": [[23, "pygame.display.get_active", false]], "get_all() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_all", false]], "get_allow_screensaver() (in module pygame.display)": [[23, "pygame.display.get_allow_screensaver", false]], "get_alpha() (pygame.surface method)": [[51, "pygame.Surface.get_alpha", false]], "get_arraytype() (in module pygame.sndarray)": [[49, "pygame.sndarray.get_arraytype", false]], "get_arraytype() (in module pygame.surfarray)": [[52, "pygame.surfarray.get_arraytype", false]], "get_arraytypes() (in module pygame.sndarray)": [[49, "pygame.sndarray.get_arraytypes", false]], "get_arraytypes() (in module pygame.surfarray)": [[52, "pygame.surfarray.get_arraytypes", false]], "get_ascent() (pygame.font.font method)": [[28, "pygame.font.Font.get_ascent", false]], "get_at() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.get_at", false]], "get_at() (pygame.surface method)": [[51, "pygame.Surface.get_at", false]], "get_at_mapped() (pygame.surface method)": [[51, "pygame.Surface.get_at_mapped", false]], "get_axis() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.get_axis", false]], "get_axis() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_axis", false]], "get_backends() (in module pygame.camera)": [[18, "pygame.camera.get_backends", false]], "get_ball() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_ball", false]], "get_bitsize() (pygame.surface method)": [[51, "pygame.Surface.get_bitsize", false]], "get_blocked() (in module pygame.event)": [[25, "pygame.event.get_blocked", false]], "get_bold() (pygame.font.font method)": [[28, "pygame.font.Font.get_bold", false]], "get_bottom_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_bottom_layer", false]], "get_bounding_rect() (pygame.surface method)": [[51, "pygame.Surface.get_bounding_rect", false]], "get_bounding_rects() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.get_bounding_rects", false]], "get_buffer() (pygame.surface method)": [[51, "pygame.Surface.get_buffer", false]], "get_busy() (in module pygame.mixer)": [[38, "pygame.mixer.get_busy", false]], "get_busy() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.get_busy", false]], "get_busy() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_busy", false]], "get_busy() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.get_busy", false]], "get_button() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.get_button", false]], "get_button() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_button", false]], "get_bytesize() (pygame.surface method)": [[51, "pygame.Surface.get_bytesize", false]], "get_cache_size() (in module pygame.freetype)": [[29, "pygame.freetype.get_cache_size", false]], "get_caption() (in module pygame.display)": [[23, "pygame.display.get_caption", false]], "get_clip() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.get_clip", false]], "get_clip() (pygame.surface method)": [[51, "pygame.Surface.get_clip", false]], "get_colorkey() (pygame.surface method)": [[51, "pygame.Surface.get_colorkey", false]], "get_controls() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.get_controls", false]], "get_count() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.get_count", false]], "get_count() (in module pygame.cdrom)": [[19, "pygame.cdrom.get_count", false]], "get_count() (in module pygame.joystick)": [[32, "pygame.joystick.get_count", false]], "get_count() (in module pygame.midi)": [[37, "pygame.midi.get_count", false]], "get_current() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_current", false]], "get_cursor() (in module pygame.mouse)": [[39, "pygame.mouse.get_cursor", false]], "get_default_font() (in module pygame.font)": [[28, "pygame.font.get_default_font", false]], "get_default_font() (in module pygame.freetype)": [[29, "pygame.freetype.get_default_font", false]], "get_default_input_id() (in module pygame.midi)": [[37, "pygame.midi.get_default_input_id", false]], "get_default_output_id() (in module pygame.midi)": [[37, "pygame.midi.get_default_output_id", false]], "get_default_resolution() (in module pygame.freetype)": [[29, "pygame.freetype.get_default_resolution", false]], "get_descent() (pygame.font.font method)": [[28, "pygame.font.Font.get_descent", false]], "get_desktop_sizes() (in module pygame.display)": [[23, "pygame.display.get_desktop_sizes", false]], "get_device() (in module pygame._sdl2.touch)": [[55, "pygame._sdl2.touch.get_device", false]], "get_device_info() (in module pygame.midi)": [[37, "pygame.midi.get_device_info", false]], "get_driver() (in module pygame.display)": [[23, "pygame.display.get_driver", false]], "get_empty() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_empty", false]], "get_endevent() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.get_endevent", false]], "get_endevent() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.get_endevent", false]], "get_error() (in module pygame)": [[44, "pygame.get_error", false]], "get_error() (in module pygame.freetype)": [[29, "pygame.freetype.get_error", false]], "get_eventstate() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.get_eventstate", false]], "get_extended() (in module pygame.image)": [[31, "pygame.image.get_extended", false]], "get_finger() (in module pygame._sdl2.touch)": [[55, "pygame._sdl2.touch.get_finger", false]], "get_flags() (pygame.surface method)": [[51, "pygame.Surface.get_flags", false]], "get_focused() (in module pygame.key)": [[33, "pygame.key.get_focused", false]], "get_focused() (in module pygame.mouse)": [[39, "pygame.mouse.get_focused", false]], "get_fonts() (in module pygame.font)": [[28, "pygame.font.get_fonts", false]], "get_fps() (pygame.time.clock method)": [[54, "pygame.time.Clock.get_fps", false]], "get_grab() (in module pygame.event)": [[25, "pygame.event.get_grab", false]], "get_guid() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_guid", false]], "get_hardware() (pygame.overlay method)": [[41, "pygame.Overlay.get_hardware", false]], "get_hat() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_hat", false]], "get_height() (pygame.font.font method)": [[28, "pygame.font.Font.get_height", false]], "get_height() (pygame.surface method)": [[51, "pygame.Surface.get_height", false]], "get_id() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_id", false]], "get_id() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_id", false]], "get_image() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.get_image", false]], "get_init() (in module pygame)": [[44, "pygame.get_init", false]], "get_init() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.get_init", false]], "get_init() (in module pygame.cdrom)": [[19, "pygame.cdrom.get_init", false]], "get_init() (in module pygame.display)": [[23, "pygame.display.get_init", false]], "get_init() (in module pygame.fastevent)": [[27, "pygame.fastevent.get_init", false]], "get_init() (in module pygame.font)": [[28, "pygame.font.get_init", false]], "get_init() (in module pygame.freetype)": [[29, "pygame.freetype.get_init", false]], "get_init() (in module pygame.joystick)": [[32, "pygame.joystick.get_init", false]], "get_init() (in module pygame.midi)": [[37, "pygame.midi.get_init", false]], "get_init() (in module pygame.mixer)": [[38, "pygame.mixer.get_init", false]], "get_init() (in module pygame.scrap)": [[46, "pygame.scrap.get_init", false]], "get_init() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.get_init", false]], "get_init() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_init", false]], "get_init() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_init", false]], "get_instance_id() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_instance_id", false]], "get_italic() (pygame.font.font method)": [[28, "pygame.font.Font.get_italic", false]], "get_keyboard_grab() (in module pygame.event)": [[25, "pygame.event.get_keyboard_grab", false]], "get_layer_of_sprite() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_layer_of_sprite", false]], "get_length() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.get_length", false]], "get_linesize() (pygame.font.font method)": [[28, "pygame.font.Font.get_linesize", false]], "get_locked() (pygame.surface method)": [[51, "pygame.Surface.get_locked", false]], "get_locks() (pygame.surface method)": [[51, "pygame.Surface.get_locks", false]], "get_losses() (pygame.surface method)": [[51, "pygame.Surface.get_losses", false]], "get_mapping() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.get_mapping", false]], "get_masks() (pygame.surface method)": [[51, "pygame.Surface.get_masks", false]], "get_metrics() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_metrics", false]], "get_mods() (in module pygame.key)": [[33, "pygame.key.get_mods", false]], "get_name() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_name", false]], "get_name() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_name", false]], "get_num_channels() (in module pygame.mixer)": [[38, "pygame.mixer.get_num_channels", false]], "get_num_channels() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.get_num_channels", false]], "get_num_devices() (in module pygame._sdl2.touch)": [[55, "pygame._sdl2.touch.get_num_devices", false]], "get_num_displays() (in module pygame.display)": [[23, "pygame.display.get_num_displays", false]], "get_num_fingers() (in module pygame._sdl2.touch)": [[55, "pygame._sdl2.touch.get_num_fingers", false]], "get_numaxes() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_numaxes", false]], "get_numballs() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_numballs", false]], "get_numbuttons() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_numbuttons", false]], "get_numhats() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_numhats", false]], "get_numtracks() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_numtracks", false]], "get_offset() (pygame.surface method)": [[51, "pygame.Surface.get_offset", false]], "get_palette() (pygame.surface method)": [[51, "pygame.Surface.get_palette", false]], "get_palette_at() (pygame.surface method)": [[51, "pygame.Surface.get_palette_at", false]], "get_parent() (pygame.surface method)": [[51, "pygame.Surface.get_parent", false]], "get_paused() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_paused", false]], "get_pitch() (pygame.surface method)": [[51, "pygame.Surface.get_pitch", false]], "get_pos() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.get_pos", false]], "get_pos() (in module pygame.mouse)": [[39, "pygame.mouse.get_pos", false]], "get_power_level() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.get_power_level", false]], "get_pressed() (in module pygame.key)": [[33, "pygame.key.get_pressed", false]], "get_pressed() (in module pygame.mouse)": [[39, "pygame.mouse.get_pressed", false]], "get_queue() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.get_queue", false]], "get_raw() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.get_raw", false]], "get_raw() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.get_raw", false]], "get_rawtime() (pygame.time.clock method)": [[54, "pygame.time.Clock.get_rawtime", false]], "get_rect() (pygame._sdl2.video.image method)": [[48, "pygame._sdl2.video.Image.get_rect", false]], "get_rect() (pygame._sdl2.video.texture method)": [[48, "pygame._sdl2.video.Texture.get_rect", false]], "get_rect() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_rect", false]], "get_rect() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.get_rect", false]], "get_rect() (pygame.surface method)": [[51, "pygame.Surface.get_rect", false]], "get_rel() (in module pygame.mouse)": [[39, "pygame.mouse.get_rel", false]], "get_repeat() (in module pygame.key)": [[33, "pygame.key.get_repeat", false]], "get_sdl_byteorder() (in module pygame)": [[44, "pygame.get_sdl_byteorder", false]], "get_sdl_image_version() (in module pygame.image)": [[31, "pygame.image.get_sdl_image_version", false]], "get_sdl_mixer_version() (in module pygame.mixer)": [[38, "pygame.mixer.get_sdl_mixer_version", false]], "get_sdl_ttf_version() (in module pygame.font)": [[28, "pygame.font.get_sdl_ttf_version", false]], "get_sdl_version() (in module pygame)": [[44, "pygame.get_sdl_version", false]], "get_shifts() (pygame.surface method)": [[51, "pygame.Surface.get_shifts", false]], "get_size() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.get_size", false]], "get_size() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.get_size", false]], "get_size() (pygame.surface method)": [[51, "pygame.Surface.get_size", false]], "get_sized_ascender() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_sized_ascender", false]], "get_sized_descender() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_sized_descender", false]], "get_sized_glyph_height() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_sized_glyph_height", false]], "get_sized_height() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_sized_height", false]], "get_sizes() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.get_sizes", false]], "get_smoothscale_backend() (in module pygame.transform)": [[56, "pygame.transform.get_smoothscale_backend", false]], "get_sound() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.get_sound", false]], "get_sprite() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_sprite", false]], "get_sprites_at() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_sprites_at", false]], "get_sprites_from_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_sprites_from_layer", false]], "get_strikethrough() (pygame.font.font method)": [[28, "pygame.font.Font.get_strikethrough", false]], "get_surface() (in module pygame.display)": [[23, "pygame.display.get_surface", false]], "get_ticks() (in module pygame.time)": [[54, "pygame.time.get_ticks", false]], "get_time() (pygame.time.clock method)": [[54, "pygame.time.Clock.get_time", false]], "get_top_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_top_layer", false]], "get_top_sprite() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.get_top_sprite", false]], "get_track_audio() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_track_audio", false]], "get_track_length() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_track_length", false]], "get_track_start() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.get_track_start", false]], "get_types() (in module pygame.scrap)": [[46, "pygame.scrap.get_types", false]], "get_underline() (pygame.font.font method)": [[28, "pygame.font.Font.get_underline", false]], "get_version() (in module pygame.freetype)": [[29, "pygame.freetype.get_version", false]], "get_view() (pygame.surface method)": [[51, "pygame.Surface.get_view", false]], "get_viewport() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.get_viewport", false]], "get_visible() (in module pygame.mouse)": [[39, "pygame.mouse.get_visible", false]], "get_volume() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.get_volume", false]], "get_volume() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.get_volume", false]], "get_volume() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.get_volume", false]], "get_width() (pygame.surface method)": [[51, "pygame.Surface.get_width", false]], "get_window_size() (in module pygame.display)": [[23, "pygame.display.get_window_size", false]], "get_wm_info() (in module pygame.display)": [[23, "pygame.display.get_wm_info", false]], "gl_get_attribute() (in module pygame.display)": [[23, "pygame.display.gl_get_attribute", false]], "gl_set_attribute() (in module pygame.display)": [[23, "pygame.display.gl_set_attribute", false]], "glcube.main() (in module pygame.examples)": [[26, "pygame.examples.glcube.main", false]], "grab (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.grab", false]], "grayscale() (in module pygame.transform)": [[56, "pygame.transform.grayscale", false]], "grayscale() (pygame.color method)": [[20, "pygame.Color.grayscale", false]], "group (class in pygame.sprite)": [[50, "pygame.sprite.Group", false]], "groupcollide() (in module pygame.sprite)": [[50, "pygame.sprite.groupcollide", false]], "groups() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.groups", false]], "groupsingle() (in module pygame.sprite)": [[50, "pygame.sprite.GroupSingle", false]], "has() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.has", false]], "headless_no_windows_needed.main() (in module pygame.examples)": [[26, "pygame.examples.headless_no_windows_needed.main", false]], "height (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.height", false]], "height (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.height", false]], "hide() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.hide", false]], "hline() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.hline", false]], "hsla (pygame.color attribute)": [[20, "pygame.Color.hsla", false]], "hsva (pygame.color attribute)": [[20, "pygame.Color.hsva", false]], "i1i2i3 (pygame.color attribute)": [[20, "pygame.Color.i1i2i3", false]], "iconify() (in module pygame.display)": [[23, "pygame.display.iconify", false]], "id (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.id", false]], "image (class in pygame._sdl2.video)": [[48, "pygame._sdl2.video.Image", false]], "import_pygame_base (c function)": [[1, "c.import_pygame_base", false]], "inflate() (pygame.rect method)": [[45, "pygame.Rect.inflate", false]], "inflate_ip() (pygame.rect method)": [[45, "pygame.Rect.inflate_ip", false]], "info() (in module pygame.display)": [[23, "pygame.display.Info", false]], "init() (in module pygame)": [[44, "pygame.init", false]], "init() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.init", false]], "init() (in module pygame.camera)": [[18, "pygame.camera.init", false]], "init() (in module pygame.cdrom)": [[19, "pygame.cdrom.init", false]], "init() (in module pygame.display)": [[23, "pygame.display.init", false]], "init() (in module pygame.fastevent)": [[27, "pygame.fastevent.init", false]], "init() (in module pygame.font)": [[28, "pygame.font.init", false]], "init() (in module pygame.freetype)": [[29, "pygame.freetype.init", false]], "init() (in module pygame.joystick)": [[32, "pygame.joystick.init", false]], "init() (in module pygame.midi)": [[37, "pygame.midi.init", false]], "init() (in module pygame.mixer)": [[38, "pygame.mixer.init", false]], "init() (in module pygame.scrap)": [[46, "pygame.scrap.init", false]], "init() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.init", false]], "init() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.init", false]], "input (class in pygame.midi)": [[37, "pygame.midi.Input", false]], "invert() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.invert", false]], "is_controller() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.is_controller", false]], "is_normalized() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.is_normalized", false]], "is_normalized() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.is_normalized", false]], "italic (pygame.font.font attribute)": [[28, "pygame.font.Font.italic", false]], "itemsize (pygame.pixelarray attribute)": [[42, "pygame.PixelArray.itemsize", false]], "joystick (class in pygame.joystick)": [[32, "pygame.joystick.Joystick", false]], "joystick.main() (in module pygame.examples)": [[26, "pygame.examples.joystick.main", false]], "kerning (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.kerning", false]], "key_code() (in module pygame.key)": [[33, "pygame.key.key_code", false]], "kill() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.kill", false]], "laplacian() (in module pygame.transform)": [[56, "pygame.transform.laplacian", false]], "layereddirty (class in pygame.sprite)": [[50, "pygame.sprite.LayeredDirty", false]], "layeredupdates (class in pygame.sprite)": [[50, "pygame.sprite.LayeredUpdates", false]], "layers() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.layers", false]], "length (pygame.bufferproxy attribute)": [[17, "pygame.BufferProxy.length", false]], "length() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.length", false]], "length() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.length", false]], "length_squared() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.length_squared", false]], "length_squared() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.length_squared", false]], "lerp() (in module pygame.math)": [[36, "pygame.math.lerp", false]], "lerp() (pygame.color method)": [[20, "pygame.Color.lerp", false]], "lerp() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.lerp", false]], "lerp() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.lerp", false]], "line() (in module pygame.draw)": [[24, "pygame.draw.line", false]], "line() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.line", false]], "lines() (in module pygame.draw)": [[24, "pygame.draw.lines", false]], "liquid.main() (in module pygame.examples)": [[26, "pygame.examples.liquid.main", false]], "list_cameras() (in module pygame.camera)": [[18, "pygame.camera.list_cameras", false]], "list_modes() (in module pygame.display)": [[23, "pygame.display.list_modes", false]], "load() (in module pygame.image)": [[31, "pygame.image.load", false]], "load() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.load", false]], "load_basic() (in module pygame.image)": [[31, "pygame.image.load_basic", false]], "load_extended() (in module pygame.image)": [[31, "pygame.image.load_extended", false]], "load_xbm() (in module pygame.cursors)": [[22, "pygame.cursors.load_xbm", false]], "lock() (pygame.surface method)": [[51, "pygame.Surface.lock", false]], "logical_size (pygame._sdl2.video.renderer attribute)": [[48, "pygame._sdl2.video.Renderer.logical_size", false]], "lost() (in module pygame.scrap)": [[46, "pygame.scrap.lost", false]], "magnitude() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.magnitude", false]], "magnitude() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.magnitude", false]], "magnitude_squared() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.magnitude_squared", false]], "magnitude_squared() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.magnitude_squared", false]], "make_sound() (in module pygame.sndarray)": [[49, "pygame.sndarray.make_sound", false]], "make_surface() (in module pygame.pixelcopy)": [[43, "pygame.pixelcopy.make_surface", false]], "make_surface() (in module pygame.surfarray)": [[52, "pygame.surfarray.make_surface", false]], "make_surface() (pygame.pixelarray method)": [[42, "pygame.PixelArray.make_surface", false]], "map_array() (in module pygame.pixelcopy)": [[43, "pygame.pixelcopy.map_array", false]], "map_array() (in module pygame.surfarray)": [[52, "pygame.surfarray.map_array", false]], "map_rgb() (pygame.surface method)": [[51, "pygame.Surface.map_rgb", false]], "mask (class in pygame.mask)": [[35, "pygame.mask.Mask", false]], "mask.main() (in module pygame.examples)": [[26, "pygame.examples.mask.main", false]], "match_font() (in module pygame.font)": [[28, "pygame.font.match_font", false]], "maximize() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.maximize", false]], "metrics() (pygame.font.font method)": [[28, "pygame.font.Font.metrics", false]], "midi.main() (in module pygame.examples)": [[26, "pygame.examples.midi.main", false]], "midi_to_ansi_note() (in module pygame.midi)": [[37, "pygame.midi.midi_to_ansi_note", false]], "midi_to_frequency() (in module pygame.midi)": [[37, "pygame.midi.midi_to_frequency", false]], "midiexception": [[37, "pygame.midi.MidiException", false]], "midis2events() (in module pygame.midi)": [[37, "pygame.midi.midis2events", false]], "minimize() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.minimize", false]], "mode_ok() (in module pygame.display)": [[23, "pygame.display.mode_ok", false]], "module": [[18, "module-pygame.camera", false], [19, "module-pygame.cdrom", false], [22, "module-pygame.cursors", false], [23, "module-pygame.display", false], [24, "module-pygame.draw", false], [25, "module-pygame.event", false], [26, "module-pygame.examples", false], [27, "module-pygame.fastevent", false], [28, "module-pygame.font", false], [29, "module-pygame.freetype", false], [30, "module-pygame.gfxdraw", false], [31, "module-pygame.image", false], [32, "module-pygame.joystick", false], [33, "module-pygame.key", false], [34, "module-pygame.locals", false], [35, "module-pygame.mask", false], [36, "module-pygame.math", false], [37, "module-pygame.midi", false], [38, "module-pygame.mixer", false], [39, "module-pygame.mouse", false], [40, "module-pygame.mixer.music", false], [43, "module-pygame.pixelcopy", false], [44, "module-pygame", false], [44, "module-pygame.version", false], [46, "module-pygame.scrap", false], [47, "module-pygame._sdl2.controller", false], [48, "module-pygame._sdl2.video", false], [49, "module-pygame.sndarray", false], [50, "module-pygame.sprite", false], [52, "module-pygame.surfarray", false], [53, "module-pygame.tests", false], [54, "module-pygame.time", false], [55, "module-pygame._sdl2.touch", false], [56, "module-pygame.transform", false]], "move() (pygame.rect method)": [[45, "pygame.Rect.move", false]], "move_ip() (pygame.rect method)": [[45, "pygame.Rect.move_ip", false]], "move_to_back() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.move_to_back", false]], "move_to_front() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.move_to_front", false]], "move_towards() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.move_towards", false]], "move_towards() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.move_towards", false]], "move_towards_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.move_towards_ip", false]], "move_towards_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.move_towards_ip", false]], "moveit.main() (in module pygame.examples)": [[26, "pygame.examples.moveit.main", false]], "mustlock() (pygame.surface method)": [[51, "pygame.Surface.mustlock", false]], "name (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.name", false]], "name() (in module pygame.key)": [[33, "pygame.key.name", false]], "name_forindex() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.name_forindex", false]], "ndim (pygame.pixelarray attribute)": [[42, "pygame.PixelArray.ndim", false]], "normalize() (pygame.color method)": [[20, "pygame.Color.normalize", false]], "normalize() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.normalize", false]], "normalize() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.normalize", false]], "normalize() (pygame.rect method)": [[45, "pygame.Rect.normalize", false]], "normalize_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.normalize_ip", false]], "normalize_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.normalize_ip", false]], "note_off() (pygame.midi.output method)": [[37, "pygame.midi.Output.note_off", false]], "note_on() (pygame.midi.output method)": [[37, "pygame.midi.Output.note_on", false]], "oblique (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.oblique", false]], "opacity (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.opacity", false]], "orderedupdates() (in module pygame.sprite)": [[50, "pygame.sprite.OrderedUpdates", false]], "origin (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.origin", false]], "origin (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.origin", false]], "outline() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.outline", false]], "output (class in pygame.midi)": [[37, "pygame.midi.Output", false]], "overlap() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.overlap", false]], "overlap_area() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.overlap_area", false]], "overlap_mask() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.overlap_mask", false]], "overlay (class in pygame)": [[41, "pygame.Overlay", false]], "pad (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.pad", false]], "parent (pygame.bufferproxy attribute)": [[17, "pygame.BufferProxy.parent", false]], "path (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.path", false]], "pause() (in module pygame.mixer)": [[38, "pygame.mixer.pause", false]], "pause() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.pause", false]], "pause() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.pause", false]], "pause() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.pause", false]], "peek() (in module pygame.event)": [[25, "pygame.event.peek", false]], "pg_buffer (c type)": [[1, "c.pg_buffer", false]], "pg_buffer.consumer (c member)": [[1, "c.pg_buffer.consumer", false]], "pg_buffer.release_buffer (c member)": [[1, "c.pg_buffer.release_buffer", false]], "pg_buffer.view (c member)": [[1, "c.pg_buffer.view", false]], "pg_encodefilepath (c function)": [[9, "c.pg_EncodeFilePath", false]], "pg_encodestring (c function)": [[9, "c.pg_EncodeString", false]], "pg_floatfromobj (c function)": [[1, "c.pg_FloatFromObj", false]], "pg_floatfromobjindex (c function)": [[1, "c.pg_FloatFromObjIndex", false]], "pg_getdefaultwindow (c function)": [[1, "c.pg_GetDefaultWindow", false]], "pg_getdefaultwindowsurface (c function)": [[1, "c.pg_GetDefaultWindowSurface", false]], "pg_intfromobj (c function)": [[1, "c.pg_IntFromObj", false]], "pg_intfromobjindex (c function)": [[1, "c.pg_IntFromObjIndex", false]], "pg_major_version (c macro)": [[13, "c.PG_MAJOR_VERSION", false]], "pg_minor_version (c macro)": [[13, "c.PG_MINOR_VERSION", false]], "pg_mod_autoinit (c function)": [[1, "c.pg_mod_autoinit", false]], "pg_mod_autoquit (c function)": [[1, "c.pg_mod_autoquit", false]], "pg_patch_version (c macro)": [[13, "c.PG_PATCH_VERSION", false]], "pg_registerquit (c function)": [[1, "c.pg_RegisterQuit", false]], "pg_rgbafromobj (c function)": [[1, "c.pg_RGBAFromObj", false]], "pg_setdefaultwindow (c function)": [[1, "c.pg_SetDefaultWindow", false]], "pg_setdefaultwindowsurface (c function)": [[1, "c.pg_SetDefaultWindowSurface", false]], "pg_twofloatsfromobj (c function)": [[1, "c.pg_TwoFloatsFromObj", false]], "pg_twointsfromobj (c function)": [[1, "c.pg_TwoIntsFromObj", false]], "pg_uintfromobj (c function)": [[1, "c.pg_UintFromObj", false]], "pg_uintfromobjindex (c function)": [[1, "c.pg_UintFromObjIndex", false]], "pg_version_atleast (c macro)": [[13, "c.PG_VERSION_ATLEAST", false]], "pg_versionnum (c macro)": [[13, "c.PG_VERSIONNUM", false]], "pgbuffer_asarrayinterface (c function)": [[1, "c.pgBuffer_AsArrayInterface", false]], "pgbuffer_asarraystruct (c function)": [[1, "c.pgBuffer_AsArrayStruct", false]], "pgbuffer_release (c function)": [[1, "c.pgBuffer_Release", false]], "pgbufproxy_check (c function)": [[2, "c.pgBufproxy_Check", false]], "pgbufproxy_getparent (c function)": [[2, "c.pgBufproxy_GetParent", false]], "pgbufproxy_new (c function)": [[2, "c.pgBufproxy_New", false]], "pgbufproxy_trip (c function)": [[2, "c.pgBufproxy_Trip", false]], "pgbufproxy_type (c var)": [[2, "c.pgBufproxy_Type", false]], "pgchannel_asint (c function)": [[7, "c.pgChannel_AsInt", false]], "pgchannel_check (c function)": [[7, "c.pgChannel_Check", false]], "pgchannel_new (c function)": [[7, "c.pgChannel_New", false]], "pgchannel_type (c var)": [[7, "c.pgChannel_Type", false]], "pgchannelobject (c type)": [[7, "c.pgChannelObject", false]], "pgcolor_check (c function)": [[3, "c.pgColor_Check", false]], "pgcolor_new (c function)": [[3, "c.pgColor_New", false]], "pgcolor_newlength (c function)": [[3, "c.pgColor_NewLength", false]], "pgcolor_type (c var)": [[3, "c.pgColor_Type", false]], "pgdict_asbuffer (c function)": [[1, "c.pgDict_AsBuffer", false]], "pgevent_check (c function)": [[5, "c.pgEvent_Check", false]], "pgevent_filluserevent (c function)": [[5, "c.pgEvent_FillUserEvent", false]], "pgevent_new (c function)": [[5, "c.pgEvent_New", false]], "pgevent_new2 (c function)": [[5, "c.pgEvent_New2", false]], "pgevent_type (c type)": [[5, "c.pgEvent_Type", false]], "pgeventobject (c type)": [[5, "c.pgEventObject", false]], "pgeventobject.type (c member)": [[5, "c.pgEventObject.type", false]], "pgexc_buffererror (c var)": [[1, "c.pgExc_BufferError", false]], "pgexc_sdlerror (c var)": [[1, "c.pgExc_SDLError", false]], "pgfont_check (c function)": [[6, "c.pgFont_Check", false]], "pgfont_is_alive (c function)": [[6, "c.pgFont_IS_ALIVE", false]], "pgfont_new (c function)": [[6, "c.pgFont_New", false]], "pgfont_type (c type)": [[6, "c.pgFont_Type", false]], "pgfontobject (c type)": [[6, "c.pgFontObject", false]], "pglifetimelock_check (c function)": [[12, "c.pgLifetimeLock_Check", false]], "pglifetimelock_type (c var)": [[12, "c.pgLifetimeLock_Type", false]], "pglifetimelockobject (c type)": [[12, "c.pgLifetimeLockObject", false]], "pglifetimelockobject.lockobj (c member)": [[12, "c.pgLifetimeLockObject.lockobj", false]], "pglifetimelockobject.surface (c member)": [[12, "c.pgLifetimeLockObject.surface", false]], "pgobject_getbuffer (c function)": [[1, "c.pgObject_GetBuffer", false]], "pgrect_asrect (c function)": [[8, "c.pgRect_AsRect", false]], "pgrect_check (c function)": [[8, "c.pgRect_Check", false]], "pgrect_fromobject (c function)": [[8, "c.pgRect_FromObject", false]], "pgrect_new (c function)": [[8, "c.pgRect_New", false]], "pgrect_new4 (c function)": [[8, "c.pgRect_New4", false]], "pgrect_normalize (c function)": [[8, "c.pgRect_Normalize", false]], "pgrect_type (c var)": [[8, "c.pgRect_Type", false]], "pgrectobject (c type)": [[8, "c.pgRectObject", false]], "pgrectobject.r (c member)": [[8, "c.pgRectObject.r", false]], "pgrwops_fromfileobject (c function)": [[9, "c.pgRWops_FromFileObject", false]], "pgrwops_fromobject (c function)": [[9, "c.pgRWops_FromObject", false]], "pgrwops_isfileobject (c function)": [[9, "c.pgRWops_IsFileObject", false]], "pgrwops_releaseobject (c function)": [[9, "c.pgRWops_ReleaseObject", false]], "pgsound_aschunk (c function)": [[7, "c.pgSound_AsChunk", false]], "pgsound_check (c function)": [[7, "c.pgSound_Check", false]], "pgsound_new (c function)": [[7, "c.pgSound_New", false]], "pgsound_type (c var)": [[7, "c.pgSound_Type", false]], "pgsoundobject (c type)": [[7, "c.pgSoundObject", false]], "pgsurface_assurface (c function)": [[11, "c.pgSurface_AsSurface", false]], "pgsurface_blit (c function)": [[11, "c.pgSurface_Blit", false]], "pgsurface_check (c function)": [[11, "c.pgSurface_Check", false]], "pgsurface_lock (c function)": [[12, "c.pgSurface_Lock", false]], "pgsurface_lockby (c function)": [[12, "c.pgSurface_LockBy", false]], "pgsurface_locklifetime (c function)": [[12, "c.pgSurface_LockLifetime", false]], "pgsurface_new (c function)": [[11, "c.pgSurface_New", false]], "pgsurface_new2 (c function)": [[11, "c.pgSurface_New2", false]], "pgsurface_prep (c function)": [[12, "c.pgSurface_Prep", false]], "pgsurface_type (c var)": [[11, "c.pgSurface_Type", false]], "pgsurface_unlock (c function)": [[12, "c.pgSurface_UnLock", false]], "pgsurface_unlockby (c function)": [[12, "c.pgSurface_UnLockBy", false]], "pgsurface_unprep (c function)": [[12, "c.pgSurface_Unprep", false]], "pgsurfaceobject (c type)": [[11, "c.pgSurfaceObject", false]], "pgvidinfo_asvidinfo (c function)": [[4, "c.pgVidInfo_AsVidInfo", false]], "pgvidinfo_check (c function)": [[4, "c.pgVidInfo_Check", false]], "pgvidinfo_new (c function)": [[4, "c.pgVidInfo_New", false]], "pgvidinfo_type (c var)": [[4, "c.pgVidInfo_Type", false]], "pgvidinfoobject (c type)": [[4, "c.pgVidInfoObject", false]], "pie() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.pie", false]], "pitch_bend() (pygame.midi.output method)": [[37, "pygame.midi.Output.pitch_bend", false]], "pixel() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.pixel", false]], "pixelarray (class in pygame)": [[42, "pygame.PixelArray", false]], "pixelarray.main() (in module pygame.examples)": [[26, "pygame.examples.pixelarray.main", false]], "pixels2d() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels2d", false]], "pixels3d() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels3d", false]], "pixels_alpha() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels_alpha", false]], "pixels_blue() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels_blue", false]], "pixels_green() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels_green", false]], "pixels_red() (in module pygame.surfarray)": [[52, "pygame.surfarray.pixels_red", false]], "play() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.play", false]], "play() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.play", false]], "play() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.play", false]], "play() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.play", false]], "playmus.main() (in module pygame.examples)": [[26, "pygame.examples.playmus.main", false]], "poll() (in module pygame.event)": [[25, "pygame.event.poll", false]], "poll() (in module pygame.fastevent)": [[27, "pygame.fastevent.poll", false]], "poll() (pygame.midi.input method)": [[37, "pygame.midi.Input.poll", false]], "polygon() (in module pygame.draw)": [[24, "pygame.draw.polygon", false]], "polygon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.polygon", false]], "position (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.position", false]], "post() (in module pygame.event)": [[25, "pygame.event.post", false]], "post() (in module pygame.fastevent)": [[27, "pygame.fastevent.post", false]], "pre_init() (in module pygame.mixer)": [[38, "pygame.mixer.pre_init", false]], "premul_alpha() (pygame.color method)": [[20, "pygame.Color.premul_alpha", false]], "premul_alpha() (pygame.surface method)": [[51, "pygame.Surface.premul_alpha", false]], "present() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.present", false]], "project() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.project", false]], "project() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.project", false]], "pump() (in module pygame.event)": [[25, "pygame.event.pump", false]], "pump() (in module pygame.fastevent)": [[27, "pygame.fastevent.pump", false]], "put() (in module pygame.scrap)": [[46, "pygame.scrap.put", false]], "pygame": [[44, "module-pygame", false]], "pygame._sdl2.controller": [[47, "module-pygame._sdl2.controller", false]], "pygame._sdl2.touch": [[55, "module-pygame._sdl2.touch", false]], "pygame._sdl2.video": [[48, "module-pygame._sdl2.video", false]], "pygame.camera": [[18, "module-pygame.camera", false]], "pygame.cdrom": [[19, "module-pygame.cdrom", false]], "pygame.cursors": [[22, "module-pygame.cursors", false]], "pygame.display": [[23, "module-pygame.display", false]], "pygame.draw": [[24, "module-pygame.draw", false]], "pygame.event": [[25, "module-pygame.event", false]], "pygame.examples": [[26, "module-pygame.examples", false]], "pygame.fastevent": [[27, "module-pygame.fastevent", false]], "pygame.font": [[28, "module-pygame.font", false]], "pygame.freetype": [[29, "module-pygame.freetype", false]], "pygame.gfxdraw": [[30, "module-pygame.gfxdraw", false]], "pygame.image": [[31, "module-pygame.image", false]], "pygame.joystick": [[32, "module-pygame.joystick", false]], "pygame.key": [[33, "module-pygame.key", false]], "pygame.locals": [[34, "module-pygame.locals", false]], "pygame.mask": [[35, "module-pygame.mask", false]], "pygame.math": [[36, "module-pygame.math", false]], "pygame.midi": [[37, "module-pygame.midi", false]], "pygame.mixer": [[38, "module-pygame.mixer", false]], "pygame.mixer.music": [[40, "module-pygame.mixer.music", false]], "pygame.mouse": [[39, "module-pygame.mouse", false]], "pygame.pixelcopy": [[43, "module-pygame.pixelcopy", false]], "pygame.scrap": [[46, "module-pygame.scrap", false]], "pygame.sndarray": [[49, "module-pygame.sndarray", false]], "pygame.sprite": [[50, "module-pygame.sprite", false]], "pygame.surfarray": [[52, "module-pygame.surfarray", false]], "pygame.tests": [[53, "module-pygame.tests", false]], "pygame.time": [[54, "module-pygame.time", false]], "pygame.transform": [[56, "module-pygame.transform", false]], "pygame.version": [[44, "module-pygame.version", false]], "query_image() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.query_image", false]], "queue() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.queue", false]], "queue() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.queue", false]], "quit() (in module pygame)": [[44, "pygame.quit", false]], "quit() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.quit", false]], "quit() (in module pygame.cdrom)": [[19, "pygame.cdrom.quit", false]], "quit() (in module pygame.display)": [[23, "pygame.display.quit", false]], "quit() (in module pygame.font)": [[28, "pygame.font.quit", false]], "quit() (in module pygame.freetype)": [[29, "pygame.freetype.quit", false]], "quit() (in module pygame.joystick)": [[32, "pygame.joystick.quit", false]], "quit() (in module pygame.midi)": [[37, "pygame.midi.quit", false]], "quit() (in module pygame.mixer)": [[38, "pygame.mixer.quit", false]], "quit() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.quit", false]], "quit() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.quit", false]], "quit() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.quit", false]], "r (pygame.color attribute)": [[20, "pygame.Color.r", false]], "raw (pygame.bufferproxy attribute)": [[17, "pygame.BufferProxy.raw", false]], "read() (pygame.midi.input method)": [[37, "pygame.midi.Input.read", false]], "rect (class in pygame)": [[45, "pygame.Rect", false]], "rect() (in module pygame.draw)": [[24, "pygame.draw.rect", false]], "rectangle() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.rectangle", false]], "reflect() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.reflect", false]], "reflect() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.reflect", false]], "reflect_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.reflect_ip", false]], "reflect_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.reflect_ip", false]], "register_quit() (in module pygame)": [[44, "pygame.register_quit", false]], "relative_mouse (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.relative_mouse", false]], "remove() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.remove", false]], "remove() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.remove", false]], "remove_sprites_of_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.remove_sprites_of_layer", false]], "render() (pygame.font.font method)": [[28, "pygame.font.Font.render", false]], "render() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.render", false]], "render_raw() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.render_raw", false]], "render_raw_to() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.render_raw_to", false]], "render_to() (pygame.freetype.font method)": [[29, "pygame.freetype.Font.render_to", false]], "renderclear (class in pygame.sprite)": [[50, "pygame.sprite.RenderClear", false]], "renderer (class in pygame._sdl2.video)": [[48, "pygame._sdl2.video.Renderer", false]], "renderer (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.renderer", false]], "renderplain (class in pygame.sprite)": [[50, "pygame.sprite.RenderPlain", false]], "renderupdates (class in pygame.sprite)": [[50, "pygame.sprite.RenderUpdates", false]], "repaint_rect() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.repaint_rect", false]], "replace() (pygame.pixelarray method)": [[42, "pygame.PixelArray.replace", false]], "resizable (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.resizable", false]], "resolution (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.resolution", false]], "restore() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.restore", false]], "resume() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.resume", false]], "rev (in module pygame.version)": [[44, "pygame.version.rev", false]], "rewind() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.rewind", false]], "rotate() (in module pygame.transform)": [[56, "pygame.transform.rotate", false]], "rotate() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.rotate", false]], "rotate() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate", false]], "rotate_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.rotate_ip", false]], "rotate_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_ip", false]], "rotate_ip_rad() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.rotate_ip_rad", false]], "rotate_ip_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_ip_rad", false]], "rotate_rad() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.rotate_rad", false]], "rotate_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_rad", false]], "rotate_rad_ip() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.rotate_rad_ip", false]], "rotate_rad_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_rad_ip", false]], "rotate_x() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_x", false]], "rotate_x_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_x_ip", false]], "rotate_x_ip_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_x_ip_rad", false]], "rotate_x_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_x_rad", false]], "rotate_x_rad_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_x_rad_ip", false]], "rotate_y() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_y", false]], "rotate_y_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_y_ip", false]], "rotate_y_ip_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_y_ip_rad", false]], "rotate_y_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_y_rad", false]], "rotate_y_rad_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_y_rad_ip", false]], "rotate_z() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_z", false]], "rotate_z_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_z_ip", false]], "rotate_z_ip_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_z_ip_rad", false]], "rotate_z_rad() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_z_rad", false]], "rotate_z_rad_ip() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.rotate_z_rad_ip", false]], "rotation (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.rotation", false]], "rotozoom() (in module pygame.transform)": [[56, "pygame.transform.rotozoom", false]], "rumble() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.rumble", false]], "rumble() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.rumble", false]], "run() (in module pygame.tests)": [[53, "pygame.tests.run", false]], "samples() (in module pygame.sndarray)": [[49, "pygame.sndarray.samples", false]], "save() (in module pygame.image)": [[31, "pygame.image.save", false]], "save_extended() (in module pygame.image)": [[31, "pygame.image.save_extended", false]], "scalable (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.scalable", false]], "scale (pygame._sdl2.video.renderer attribute)": [[48, "pygame._sdl2.video.Renderer.scale", false]], "scale() (in module pygame.transform)": [[56, "pygame.transform.scale", false]], "scale() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.scale", false]], "scale2x() (in module pygame.transform)": [[56, "pygame.transform.scale2x", false]], "scale_by() (in module pygame.transform)": [[56, "pygame.transform.scale_by", false]], "scale_by() (pygame.rect method)": [[45, "pygame.Rect.scale_by", false]], "scale_by_ip() (pygame.rect method)": [[45, "pygame.Rect.scale_by_ip", false]], "scale_to_length() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.scale_to_length", false]], "scale_to_length() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.scale_to_length", false]], "scaletest.main() (in module pygame.examples)": [[26, "pygame.examples.scaletest.main", false]], "scrap_clipboard.main() (in module pygame.examples)": [[26, "pygame.examples.scrap_clipboard.main", false]], "scroll() (pygame.surface method)": [[51, "pygame.Surface.scroll", false]], "scroll.main() (in module pygame.examples)": [[26, "pygame.examples.scroll.main", false]], "sdl (in module pygame.version)": [[44, "pygame.version.SDL", false]], "set_allow_screensaver() (in module pygame.display)": [[23, "pygame.display.set_allow_screensaver", false]], "set_allowed() (in module pygame.event)": [[25, "pygame.event.set_allowed", false]], "set_alpha() (pygame.surface method)": [[51, "pygame.Surface.set_alpha", false]], "set_at() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.set_at", false]], "set_at() (pygame.surface method)": [[51, "pygame.Surface.set_at", false]], "set_blocked() (in module pygame.event)": [[25, "pygame.event.set_blocked", false]], "set_bold() (pygame.font.font method)": [[28, "pygame.font.Font.set_bold", false]], "set_caption() (in module pygame.display)": [[23, "pygame.display.set_caption", false]], "set_clip() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.set_clip", false]], "set_clip() (pygame.surface method)": [[51, "pygame.Surface.set_clip", false]], "set_colorkey() (pygame.surface method)": [[51, "pygame.Surface.set_colorkey", false]], "set_controls() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.set_controls", false]], "set_cursor() (in module pygame.mouse)": [[39, "pygame.mouse.set_cursor", false]], "set_default_resolution() (in module pygame.freetype)": [[29, "pygame.freetype.set_default_resolution", false]], "set_endevent() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.set_endevent", false]], "set_endevent() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.set_endevent", false]], "set_error() (in module pygame)": [[44, "pygame.set_error", false]], "set_eventstate() (in module pygame._sdl2.controller)": [[47, "pygame._sdl2.controller.set_eventstate", false]], "set_fullscreen() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.set_fullscreen", false]], "set_gamma() (in module pygame.display)": [[23, "pygame.display.set_gamma", false]], "set_gamma_ramp() (in module pygame.display)": [[23, "pygame.display.set_gamma_ramp", false]], "set_grab() (in module pygame.event)": [[25, "pygame.event.set_grab", false]], "set_icon() (in module pygame.display)": [[23, "pygame.display.set_icon", false]], "set_icon() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.set_icon", false]], "set_instrument() (pygame.midi.output method)": [[37, "pygame.midi.Output.set_instrument", false]], "set_italic() (pygame.font.font method)": [[28, "pygame.font.Font.set_italic", false]], "set_keyboard_grab() (in module pygame.event)": [[25, "pygame.event.set_keyboard_grab", false]], "set_length() (pygame.color method)": [[20, "pygame.Color.set_length", false]], "set_location() (pygame.overlay method)": [[41, "pygame.Overlay.set_location", false]], "set_mapping() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.set_mapping", false]], "set_masks() (pygame.surface method)": [[51, "pygame.Surface.set_masks", false]], "set_modal_for() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.set_modal_for", false]], "set_mode() (in module pygame.display)": [[23, "pygame.display.set_mode", false]], "set_mode() (in module pygame.scrap)": [[46, "pygame.scrap.set_mode", false]], "set_mods() (in module pygame.key)": [[33, "pygame.key.set_mods", false]], "set_num_channels() (in module pygame.mixer)": [[38, "pygame.mixer.set_num_channels", false]], "set_palette() (in module pygame.display)": [[23, "pygame.display.set_palette", false]], "set_palette() (pygame.surface method)": [[51, "pygame.Surface.set_palette", false]], "set_palette_at() (pygame.surface method)": [[51, "pygame.Surface.set_palette_at", false]], "set_pos() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.set_pos", false]], "set_pos() (in module pygame.mouse)": [[39, "pygame.mouse.set_pos", false]], "set_repeat() (in module pygame.key)": [[33, "pygame.key.set_repeat", false]], "set_reserved() (in module pygame.mixer)": [[38, "pygame.mixer.set_reserved", false]], "set_script() (pygame.font.font method)": [[28, "pygame.font.Font.set_script", false]], "set_shifts() (pygame.surface method)": [[51, "pygame.Surface.set_shifts", false]], "set_smoothscale_backend() (in module pygame.transform)": [[56, "pygame.transform.set_smoothscale_backend", false]], "set_strikethrough() (pygame.font.font method)": [[28, "pygame.font.Font.set_strikethrough", false]], "set_text_input_rect() (in module pygame.key)": [[33, "pygame.key.set_text_input_rect", false]], "set_timer() (in module pygame.time)": [[54, "pygame.time.set_timer", false]], "set_timing_threshold() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.set_timing_threshold", false]], "set_timing_treshold() (pygame.sprite.layereddirty method)": [[50, "pygame.sprite.LayeredDirty.set_timing_treshold", false]], "set_underline() (pygame.font.font method)": [[28, "pygame.font.Font.set_underline", false]], "set_viewport() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.set_viewport", false]], "set_visible() (in module pygame.mouse)": [[39, "pygame.mouse.set_visible", false]], "set_volume() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.set_volume", false]], "set_volume() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.set_volume", false]], "set_volume() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.set_volume", false]], "set_windowed() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.set_windowed", false]], "shape (pygame.pixelarray attribute)": [[42, "pygame.PixelArray.shape", false]], "show() (pygame._sdl2.video.window method)": [[48, "pygame._sdl2.video.Window.show", false]], "size (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.size", false]], "size (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.size", false]], "size() (pygame.font.font method)": [[28, "pygame.font.Font.size", false]], "slerp() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.slerp", false]], "slerp() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.slerp", false]], "smoothscale() (in module pygame.transform)": [[56, "pygame.transform.smoothscale", false]], "smoothscale_by() (in module pygame.transform)": [[56, "pygame.transform.smoothscale_by", false]], "sound (class in pygame.mixer)": [[38, "pygame.mixer.Sound", false]], "sound.main() (in module pygame.examples)": [[26, "pygame.examples.sound.main", false]], "sound_array_demos.main() (in module pygame.examples)": [[26, "pygame.examples.sound_array_demos.main", false]], "sprite (class in pygame.sprite)": [[50, "pygame.sprite.Sprite", false]], "spritecollide() (in module pygame.sprite)": [[50, "pygame.sprite.spritecollide", false]], "spritecollideany() (in module pygame.sprite)": [[50, "pygame.sprite.spritecollideany", false]], "sprites() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.sprites", false]], "sprites() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.sprites", false]], "srcrect (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.srcrect", false]], "stars.main() (in module pygame.examples)": [[26, "pygame.examples.stars.main", false]], "start() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.start", false]], "start_text_input() (in module pygame.key)": [[33, "pygame.key.start_text_input", false]], "stop() (in module pygame.mixer)": [[38, "pygame.mixer.stop", false]], "stop() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.stop", false]], "stop() (pygame.camera.camera method)": [[18, "pygame.camera.Camera.stop", false]], "stop() (pygame.cdrom.cd method)": [[19, "pygame.cdrom.CD.stop", false]], "stop() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.stop", false]], "stop() (pygame.mixer.sound method)": [[38, "pygame.mixer.Sound.stop", false]], "stop_rumble() (pygame._sdl2.controller.controller method)": [[47, "pygame._sdl2.controller.Controller.stop_rumble", false]], "stop_rumble() (pygame.joystick.joystick method)": [[32, "pygame.joystick.Joystick.stop_rumble", false]], "stop_text_input() (in module pygame.key)": [[33, "pygame.key.stop_text_input", false]], "strength (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.strength", false]], "strides (pygame.pixelarray attribute)": [[42, "pygame.PixelArray.strides", false]], "strikethrough (pygame.font.font attribute)": [[28, "pygame.font.Font.strikethrough", false]], "strong (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.strong", false]], "style (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.style", false]], "subsurface() (pygame.surface method)": [[51, "pygame.Surface.subsurface", false]], "surface (class in pygame)": [[51, "pygame.Surface", false]], "surface (pygame.pixelarray attribute)": [[42, "pygame.PixelArray.surface", false]], "surface_to_array() (in module pygame.pixelcopy)": [[43, "pygame.pixelcopy.surface_to_array", false]], "switch_layer() (pygame.sprite.layeredupdates method)": [[50, "pygame.sprite.LayeredUpdates.switch_layer", false]], "sysfont() (in module pygame.font)": [[28, "pygame.font.SysFont", false]], "sysfont() (in module pygame.freetype)": [[29, "pygame.freetype.SysFont", false]], "target (pygame._sdl2.video.renderer attribute)": [[48, "pygame._sdl2.video.Renderer.target", false]], "testsprite.main() (in module pygame.examples)": [[26, "pygame.examples.testsprite.main", false]], "texture (class in pygame._sdl2.video)": [[48, "pygame._sdl2.video.Texture", false]], "texture (pygame._sdl2.video.image attribute)": [[48, "pygame._sdl2.video.Image.texture", false]], "textured_polygon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.textured_polygon", false]], "threshold() (in module pygame.transform)": [[56, "pygame.transform.threshold", false]], "tick() (pygame.time.clock method)": [[54, "pygame.time.Clock.tick", false]], "tick_busy_loop() (pygame.time.clock method)": [[54, "pygame.time.Clock.tick_busy_loop", false]], "time() (in module pygame.midi)": [[37, "pygame.midi.time", false]], "title (pygame._sdl2.video.window attribute)": [[48, "pygame._sdl2.video.Window.title", false]], "to_surface() (pygame._sdl2.video.renderer method)": [[48, "pygame._sdl2.video.Renderer.to_surface", false]], "to_surface() (pygame.mask.mask method)": [[35, "pygame.mask.Mask.to_surface", false]], "tobytes() (in module pygame.image)": [[31, "pygame.image.tobytes", false]], "toggle_fullscreen() (in module pygame.display)": [[23, "pygame.display.toggle_fullscreen", false]], "tostring() (in module pygame.image)": [[31, "pygame.image.tostring", false]], "transpose() (pygame.pixelarray method)": [[42, "pygame.PixelArray.transpose", false]], "trigon() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.trigon", false]], "type (pygame.cursors.cursor attribute)": [[22, "pygame.cursors.Cursor.type", false]], "type (pygame.event.event attribute)": [[25, "pygame.event.Event.type", false]], "ucs4 (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.ucs4", false]], "underline (pygame.font.font attribute)": [[28, "pygame.font.Font.underline", false]], "underline (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.underline", false]], "underline_adjustment (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.underline_adjustment", false]], "union() (pygame.rect method)": [[45, "pygame.Rect.union", false]], "union_ip() (pygame.rect method)": [[45, "pygame.Rect.union_ip", false]], "unionall() (pygame.rect method)": [[45, "pygame.Rect.unionall", false]], "unionall_ip() (pygame.rect method)": [[45, "pygame.Rect.unionall_ip", false]], "unload() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.unload", false]], "unlock() (pygame.surface method)": [[51, "pygame.Surface.unlock", false]], "unmap_rgb() (pygame.surface method)": [[51, "pygame.Surface.unmap_rgb", false]], "unpause() (in module pygame.mixer)": [[38, "pygame.mixer.unpause", false]], "unpause() (in module pygame.mixer.music)": [[40, "pygame.mixer.music.unpause", false]], "unpause() (pygame.mixer.channel method)": [[38, "pygame.mixer.Channel.unpause", false]], "update() (in module pygame.display)": [[23, "pygame.display.update", false]], "update() (pygame._sdl2.video.texture method)": [[48, "pygame._sdl2.video.Texture.update", false]], "update() (pygame.color method)": [[20, "pygame.Color.update", false]], "update() (pygame.math.vector2 method)": [[36, "pygame.math.Vector2.update", false]], "update() (pygame.math.vector3 method)": [[36, "pygame.math.Vector3.update", false]], "update() (pygame.rect method)": [[45, "pygame.Rect.update", false]], "update() (pygame.sprite.group method)": [[50, "pygame.sprite.Group.update", false]], "update() (pygame.sprite.sprite method)": [[50, "pygame.sprite.Sprite.update", false]], "use_arraytype() (in module pygame.sndarray)": [[49, "pygame.sndarray.use_arraytype", false]], "use_arraytype() (in module pygame.surfarray)": [[52, "pygame.surfarray.use_arraytype", false]], "use_bitmap_strikes (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.use_bitmap_strikes", false]], "vector2 (class in pygame.math)": [[36, "pygame.math.Vector2", false]], "vector3 (class in pygame.math)": [[36, "pygame.math.Vector3", false]], "ver (in module pygame.version)": [[44, "pygame.version.ver", false]], "vernum (in module pygame.version)": [[44, "pygame.version.vernum", false]], "vertical (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.vertical", false]], "vgrade.main() (in module pygame.examples)": [[26, "pygame.examples.vgrade.main", false]], "vline() (in module pygame.gfxdraw)": [[30, "pygame.gfxdraw.vline", false]], "wait() (in module pygame.event)": [[25, "pygame.event.wait", false]], "wait() (in module pygame.fastevent)": [[27, "pygame.fastevent.wait", false]], "wait() (in module pygame.time)": [[54, "pygame.time.wait", false]], "was_init() (in module pygame.freetype)": [[29, "pygame.freetype.was_init", false]], "weakdirtysprite (class in pygame.sprite)": [[50, "pygame.sprite.WeakDirtySprite", false]], "weaksprite (class in pygame.sprite)": [[50, "pygame.sprite.WeakSprite", false]], "wide (pygame.freetype.font attribute)": [[29, "pygame.freetype.Font.wide", false]], "width (pygame._sdl2.video.texture attribute)": [[48, "pygame._sdl2.video.Texture.width", false]], "window (class in pygame._sdl2.video)": [[48, "pygame._sdl2.video.Window", false]], "write() (pygame.bufferproxy method)": [[17, "pygame.BufferProxy.write", false]], "write() (pygame.midi.output method)": [[37, "pygame.midi.Output.write", false]], "write_short() (pygame.midi.output method)": [[37, "pygame.midi.Output.write_short", false]], "write_sys_ex() (pygame.midi.output method)": [[37, "pygame.midi.Output.write_sys_ex", false]]}, "objects": {"": [[13, 0, 1, "c.PG_MAJOR_VERSION", "PG_MAJOR_VERSION"], [13, 0, 1, "c.PG_MINOR_VERSION", "PG_MINOR_VERSION"], [13, 0, 1, "c.PG_PATCH_VERSION", "PG_PATCH_VERSION"], [13, 0, 1, "c.PG_VERSIONNUM", "PG_VERSIONNUM"], [13, 0, 1, "c.PG_VERSION_ATLEAST", "PG_VERSION_ATLEAST"], [1, 1, 1, "c.import_pygame_base", "import_pygame_base"], [1, 1, 1, "c.pgBuffer_AsArrayInterface", "pgBuffer_AsArrayInterface"], [1, 1, 1, "c.pgBuffer_AsArrayStruct", "pgBuffer_AsArrayStruct"], [1, 1, 1, "c.pgBuffer_Release", "pgBuffer_Release"], [2, 1, 1, "c.pgBufproxy_Check", "pgBufproxy_Check"], [2, 1, 1, "c.pgBufproxy_GetParent", "pgBufproxy_GetParent"], [2, 1, 1, "c.pgBufproxy_New", "pgBufproxy_New"], [2, 1, 1, "c.pgBufproxy_Trip", "pgBufproxy_Trip"], [2, 3, 1, "c.pgBufproxy_Type", "pgBufproxy_Type"], [7, 4, 1, "c.pgChannelObject", "pgChannelObject"], [7, 1, 1, "c.pgChannel_AsInt", "pgChannel_AsInt"], [7, 1, 1, "c.pgChannel_Check", "pgChannel_Check"], [7, 1, 1, "c.pgChannel_New", "pgChannel_New"], [7, 3, 1, "c.pgChannel_Type", "pgChannel_Type"], [3, 1, 1, "c.pgColor_Check", "pgColor_Check"], [3, 1, 1, "c.pgColor_New", "pgColor_New"], [3, 1, 1, "c.pgColor_NewLength", "pgColor_NewLength"], [3, 3, 1, "c.pgColor_Type", "pgColor_Type"], [1, 1, 1, "c.pgDict_AsBuffer", "pgDict_AsBuffer"], [5, 4, 1, "c.pgEventObject", "pgEventObject"], [5, 1, 1, "c.pgEvent_Check", "pgEvent_Check"], [5, 1, 1, "c.pgEvent_FillUserEvent", "pgEvent_FillUserEvent"], [5, 1, 1, "c.pgEvent_New", "pgEvent_New"], [5, 1, 1, "c.pgEvent_New2", "pgEvent_New2"], [5, 4, 1, "c.pgEvent_Type", "pgEvent_Type"], [1, 3, 1, "c.pgExc_BufferError", "pgExc_BufferError"], [1, 3, 1, "c.pgExc_SDLError", "pgExc_SDLError"], [6, 4, 1, "c.pgFontObject", "pgFontObject"], [6, 1, 1, "c.pgFont_Check", "pgFont_Check"], [6, 1, 1, "c.pgFont_IS_ALIVE", "pgFont_IS_ALIVE"], [6, 1, 1, "c.pgFont_New", "pgFont_New"], [6, 4, 1, "c.pgFont_Type", "pgFont_Type"], [12, 4, 1, "c.pgLifetimeLockObject", "pgLifetimeLockObject"], [12, 1, 1, "c.pgLifetimeLock_Check", "pgLifetimeLock_Check"], [12, 3, 1, "c.pgLifetimeLock_Type", "pgLifetimeLock_Type"], [1, 1, 1, "c.pgObject_GetBuffer", "pgObject_GetBuffer"], [9, 1, 1, "c.pgRWops_FromFileObject", "pgRWops_FromFileObject"], [9, 1, 1, "c.pgRWops_FromObject", "pgRWops_FromObject"], [9, 1, 1, "c.pgRWops_IsFileObject", "pgRWops_IsFileObject"], [9, 1, 1, "c.pgRWops_ReleaseObject", "pgRWops_ReleaseObject"], [8, 4, 1, "c.pgRectObject", "pgRectObject"], [8, 1, 1, "c.pgRect_AsRect", "pgRect_AsRect"], [8, 1, 1, "c.pgRect_Check", "pgRect_Check"], [8, 1, 1, "c.pgRect_FromObject", "pgRect_FromObject"], [8, 1, 1, "c.pgRect_New", "pgRect_New"], [8, 1, 1, "c.pgRect_New4", "pgRect_New4"], [8, 1, 1, "c.pgRect_Normalize", "pgRect_Normalize"], [8, 3, 1, "c.pgRect_Type", "pgRect_Type"], [7, 4, 1, "c.pgSoundObject", "pgSoundObject"], [7, 1, 1, "c.pgSound_AsChunk", "pgSound_AsChunk"], [7, 1, 1, "c.pgSound_Check", "pgSound_Check"], [7, 1, 1, "c.pgSound_New", "pgSound_New"], [7, 3, 1, "c.pgSound_Type", "pgSound_Type"], [11, 4, 1, "c.pgSurfaceObject", "pgSurfaceObject"], [11, 1, 1, "c.pgSurface_AsSurface", "pgSurface_AsSurface"], [11, 1, 1, "c.pgSurface_Blit", "pgSurface_Blit"], [11, 1, 1, "c.pgSurface_Check", "pgSurface_Check"], [12, 1, 1, "c.pgSurface_Lock", "pgSurface_Lock"], [12, 1, 1, "c.pgSurface_LockBy", "pgSurface_LockBy"], [12, 1, 1, "c.pgSurface_LockLifetime", "pgSurface_LockLifetime"], [11, 1, 1, "c.pgSurface_New", "pgSurface_New"], [11, 1, 1, "c.pgSurface_New2", "pgSurface_New2"], [12, 1, 1, "c.pgSurface_Prep", "pgSurface_Prep"], [11, 3, 1, "c.pgSurface_Type", "pgSurface_Type"], [12, 1, 1, "c.pgSurface_UnLock", "pgSurface_UnLock"], [12, 1, 1, "c.pgSurface_UnLockBy", "pgSurface_UnLockBy"], [12, 1, 1, "c.pgSurface_Unprep", "pgSurface_Unprep"], [4, 4, 1, "c.pgVidInfoObject", "pgVidInfoObject"], [4, 1, 1, "c.pgVidInfo_AsVidInfo", "pgVidInfo_AsVidInfo"], [4, 1, 1, "c.pgVidInfo_Check", "pgVidInfo_Check"], [4, 1, 1, "c.pgVidInfo_New", "pgVidInfo_New"], [4, 3, 1, "c.pgVidInfo_Type", "pgVidInfo_Type"], [9, 1, 1, "c.pg_EncodeFilePath", "pg_EncodeFilePath"], [9, 1, 1, "c.pg_EncodeString", "pg_EncodeString"], [1, 1, 1, "c.pg_FloatFromObj", "pg_FloatFromObj"], [1, 1, 1, "c.pg_FloatFromObjIndex", "pg_FloatFromObjIndex"], [1, 1, 1, "c.pg_GetDefaultWindow", "pg_GetDefaultWindow"], [1, 1, 1, "c.pg_GetDefaultWindowSurface", "pg_GetDefaultWindowSurface"], [1, 1, 1, "c.pg_IntFromObj", "pg_IntFromObj"], [1, 1, 1, "c.pg_IntFromObjIndex", "pg_IntFromObjIndex"], [1, 1, 1, "c.pg_RGBAFromObj", "pg_RGBAFromObj"], [1, 1, 1, "c.pg_RegisterQuit", "pg_RegisterQuit"], [1, 1, 1, "c.pg_SetDefaultWindow", "pg_SetDefaultWindow"], [1, 1, 1, "c.pg_SetDefaultWindowSurface", "pg_SetDefaultWindowSurface"], [1, 1, 1, "c.pg_TwoFloatsFromObj", "pg_TwoFloatsFromObj"], [1, 1, 1, "c.pg_TwoIntsFromObj", "pg_TwoIntsFromObj"], [1, 1, 1, "c.pg_UintFromObj", "pg_UintFromObj"], [1, 1, 1, "c.pg_UintFromObjIndex", "pg_UintFromObjIndex"], [1, 4, 1, "c.pg_buffer", "pg_buffer"], [1, 1, 1, "c.pg_mod_autoinit", "pg_mod_autoinit"], [1, 1, 1, "c.pg_mod_autoquit", "pg_mod_autoquit"], [44, 5, 0, "-", "pygame"]], "pgBuffer_AsArrayInterface": [[1, 2, 1, "c.pgBuffer_AsArrayInterface", "view_p"]], "pgBuffer_AsArrayStruct": [[1, 2, 1, "c.pgBuffer_AsArrayStruct", "view_p"]], "pgBuffer_Release": [[1, 2, 1, "c.pgBuffer_Release", "pg_view_p"]], "pgBufproxy_Check": [[2, 2, 1, "c.pgBufproxy_Check", "x"]], "pgBufproxy_GetParent": [[2, 2, 1, "c.pgBufproxy_GetParent", "obj"]], "pgBufproxy_New": [[2, 2, 1, "c.pgBufproxy_New", "get_buffer"], [2, 2, 1, "c.pgBufproxy_New", "obj"]], "pgBufproxy_Trip": [[2, 2, 1, "c.pgBufproxy_Trip", "obj"]], "pgChannel_AsInt": [[7, 2, 1, "c.pgChannel_AsInt", "x"]], "pgChannel_Check": [[7, 2, 1, "c.pgChannel_Check", "obj"]], "pgChannel_New": [[7, 2, 1, "c.pgChannel_New", "channelnum"]], "pgColor_Check": [[3, 2, 1, "c.pgColor_Check", "obj"]], "pgColor_New": [[3, 2, 1, "c.pgColor_New", "rgba"]], "pgColor_NewLength": [[3, 2, 1, "c.pgColor_NewLength", "length"], [3, 2, 1, "c.pgColor_NewLength", "rgba"]], "pgDict_AsBuffer": [[1, 2, 1, "c.pgDict_AsBuffer", "dict"], [1, 2, 1, "c.pgDict_AsBuffer", "flags"], [1, 2, 1, "c.pgDict_AsBuffer", "pg_view_p"]], "pgEventObject": [[5, 3, 1, "c.pgEventObject.type", "type"]], "pgEvent_Check": [[5, 2, 1, "c.pgEvent_Check", "x"]], "pgEvent_FillUserEvent": [[5, 2, 1, "c.pgEvent_FillUserEvent", "e"], [5, 2, 1, "c.pgEvent_FillUserEvent", "event"]], "pgEvent_New": [[5, 2, 1, "c.pgEvent_New", "event"]], "pgEvent_New2": [[5, 2, 1, "c.pgEvent_New2", "dict"], [5, 2, 1, "c.pgEvent_New2", "type"]], "pgFont_Check": [[6, 2, 1, "c.pgFont_Check", "x"]], "pgFont_IS_ALIVE": [[6, 2, 1, "c.pgFont_IS_ALIVE", "o"]], "pgFont_New": [[6, 2, 1, "c.pgFont_New", "filename"], [6, 2, 1, "c.pgFont_New", "font_index"]], "pgLifetimeLockObject": [[12, 3, 1, "c.pgLifetimeLockObject.lockobj", "lockobj"], [12, 3, 1, "c.pgLifetimeLockObject.surface", "surface"]], "pgLifetimeLock_Check": [[12, 2, 1, "c.pgLifetimeLock_Check", "x"]], "pgObject_GetBuffer": [[1, 2, 1, "c.pgObject_GetBuffer", "flags"], [1, 2, 1, "c.pgObject_GetBuffer", "obj"], [1, 2, 1, "c.pgObject_GetBuffer", "pg_view_p"]], "pgRWops_FromFileObject": [[9, 2, 1, "c.pgRWops_FromFileObject", "obj"]], "pgRWops_FromObject": [[9, 2, 1, "c.pgRWops_FromObject", "extptr"], [9, 2, 1, "c.pgRWops_FromObject", "obj"]], "pgRWops_IsFileObject": [[9, 2, 1, "c.pgRWops_IsFileObject", "rw"]], "pgRWops_ReleaseObject": [[9, 2, 1, "c.pgRWops_ReleaseObject", "context"]], "pgRectObject": [[8, 3, 1, "c.pgRectObject.r", "r"]], "pgRect_AsRect": [[8, 2, 1, "c.pgRect_AsRect", "obj"]], "pgRect_Check": [[8, 2, 1, "c.pgRect_Check", "obj"]], "pgRect_FromObject": [[8, 2, 1, "c.pgRect_FromObject", "obj"], [8, 2, 1, "c.pgRect_FromObject", "temp"]], "pgRect_New": [[8, 2, 1, "c.pgRect_New", "r"]], "pgRect_New4": [[8, 2, 1, "c.pgRect_New4", "h"], [8, 2, 1, "c.pgRect_New4", "w"], [8, 2, 1, "c.pgRect_New4", "x"], [8, 2, 1, "c.pgRect_New4", "y"]], "pgRect_Normalize": [[8, 2, 1, "c.pgRect_Normalize", "rect"]], "pgSound_AsChunk": [[7, 2, 1, "c.pgSound_AsChunk", "x"]], "pgSound_Check": [[7, 2, 1, "c.pgSound_Check", "obj"]], "pgSound_New": [[7, 2, 1, "c.pgSound_New", "chunk"]], "pgSurface_AsSurface": [[11, 2, 1, "c.pgSurface_AsSurface", "x"]], "pgSurface_Blit": [[11, 2, 1, "c.pgSurface_Blit", "dstobj"], [11, 2, 1, "c.pgSurface_Blit", "dstrect"], [11, 2, 1, "c.pgSurface_Blit", "srcobj"], [11, 2, 1, "c.pgSurface_Blit", "srcrect"], [11, 2, 1, "c.pgSurface_Blit", "the_args"]], "pgSurface_Check": [[11, 2, 1, "c.pgSurface_Check", "x"]], "pgSurface_Lock": [[12, 2, 1, "c.pgSurface_Lock", "surfobj"]], "pgSurface_LockBy": [[12, 2, 1, "c.pgSurface_LockBy", "lockobj"], [12, 2, 1, "c.pgSurface_LockBy", "surfobj"]], "pgSurface_LockLifetime": [[12, 2, 1, "c.pgSurface_LockLifetime", "lockobj"], [12, 2, 1, "c.pgSurface_LockLifetime", "surfobj"]], "pgSurface_New": [[11, 2, 1, "c.pgSurface_New", "s"]], "pgSurface_New2": [[11, 2, 1, "c.pgSurface_New2", "owner"], [11, 2, 1, "c.pgSurface_New2", "s"]], "pgSurface_Prep": [[12, 2, 1, "c.pgSurface_Prep", "surfobj"]], "pgSurface_UnLock": [[12, 2, 1, "c.pgSurface_UnLock", "surfobj"]], "pgSurface_UnLockBy": [[12, 2, 1, "c.pgSurface_UnLockBy", "lockobj"], [12, 2, 1, "c.pgSurface_UnLockBy", "surfobj"]], "pgSurface_Unprep": [[12, 2, 1, "c.pgSurface_Unprep", "surfobj"]], "pgVidInfo_AsVidInfo": [[4, 2, 1, "c.pgVidInfo_AsVidInfo", "obj"]], "pgVidInfo_Check": [[4, 2, 1, "c.pgVidInfo_Check", "x"]], "pgVidInfo_New": [[4, 2, 1, "c.pgVidInfo_New", "i"]], "pg_EncodeFilePath": [[9, 2, 1, "c.pg_EncodeFilePath", "eclass"], [9, 2, 1, "c.pg_EncodeFilePath", "obj"]], "pg_EncodeString": [[9, 2, 1, "c.pg_EncodeString", "eclass"], [9, 2, 1, "c.pg_EncodeString", "encoding"], [9, 2, 1, "c.pg_EncodeString", "errors"], [9, 2, 1, "c.pg_EncodeString", "obj"]], "pg_FloatFromObj": [[1, 2, 1, "c.pg_FloatFromObj", "obj"], [1, 2, 1, "c.pg_FloatFromObj", "val"]], "pg_FloatFromObjIndex": [[1, 2, 1, "c.pg_FloatFromObjIndex", "index"], [1, 2, 1, "c.pg_FloatFromObjIndex", "obj"], [1, 2, 1, "c.pg_FloatFromObjIndex", "val"]], "pg_IntFromObj": [[1, 2, 1, "c.pg_IntFromObj", "obj"], [1, 2, 1, "c.pg_IntFromObj", "val"]], "pg_IntFromObjIndex": [[1, 2, 1, "c.pg_IntFromObjIndex", "index"], [1, 2, 1, "c.pg_IntFromObjIndex", "obj"], [1, 2, 1, "c.pg_IntFromObjIndex", "val"]], "pg_RGBAFromObj": [[1, 2, 1, "c.pg_RGBAFromObj", "RGBA"], [1, 2, 1, "c.pg_RGBAFromObj", "obj"]], "pg_RegisterQuit": [[1, 2, 1, "c.pg_RegisterQuit", "f"]], "pg_SetDefaultWindow": [[1, 2, 1, "c.pg_SetDefaultWindow", "win"]], "pg_SetDefaultWindowSurface": [[1, 2, 1, "c.pg_SetDefaultWindowSurface", "screen"]], "pg_TwoFloatsFromObj": [[1, 2, 1, "c.pg_TwoFloatsFromObj", "obj"], [1, 2, 1, "c.pg_TwoFloatsFromObj", "val1"], [1, 2, 1, "c.pg_TwoFloatsFromObj", "val2"]], "pg_TwoIntsFromObj": [[1, 2, 1, "c.pg_TwoIntsFromObj", "obj"], [1, 2, 1, "c.pg_TwoIntsFromObj", "v2"], [1, 2, 1, "c.pg_TwoIntsFromObj", "val1"]], "pg_UintFromObj": [[1, 2, 1, "c.pg_UintFromObj", "obj"], [1, 2, 1, "c.pg_UintFromObj", "val"]], "pg_UintFromObjIndex": [[1, 2, 1, "c.pg_UintFromObjIndex", "_index"], [1, 2, 1, "c.pg_UintFromObjIndex", "obj"], [1, 2, 1, "c.pg_UintFromObjIndex", "val"]], "pg_buffer": [[1, 3, 1, "c.pg_buffer.consumer", "consumer"], [1, 3, 1, "c.pg_buffer.release_buffer", "release_buffer"], [1, 3, 1, "c.pg_buffer.view", "view"]], "pg_mod_autoinit": [[1, 2, 1, "c.pg_mod_autoinit", "modname"]], "pg_mod_autoquit": [[1, 2, 1, "c.pg_mod_autoquit", "modname"]], "pygame": [[17, 6, 1, "", "BufferProxy"], [20, 6, 1, "", "Color"], [41, 6, 1, "", "Overlay"], [42, 6, 1, "", "PixelArray"], [45, 6, 1, "", "Rect"], [51, 6, 1, "", "Surface"], [18, 5, 0, "-", "camera"], [19, 5, 0, "-", "cdrom"], [22, 5, 0, "-", "cursors"], [23, 5, 0, "-", "display"], [24, 5, 0, "-", "draw"], [44, 9, 1, "", "encode_file_path"], [44, 9, 1, "", "encode_string"], [44, 10, 1, "", "error"], [25, 5, 0, "-", "event"], [26, 5, 0, "-", "examples"], [27, 5, 0, "-", "fastevent"], [28, 5, 0, "-", "font"], [29, 5, 0, "-", "freetype"], [44, 9, 1, "", "get_error"], [44, 9, 1, "", "get_init"], [44, 9, 1, "", "get_sdl_byteorder"], [44, 9, 1, "", "get_sdl_version"], [30, 5, 0, "-", "gfxdraw"], [31, 5, 0, "-", "image"], [44, 9, 1, "", "init"], [32, 5, 0, "-", "joystick"], [33, 5, 0, "-", "key"], [34, 5, 0, "-", "locals"], [35, 5, 0, "-", "mask"], [36, 5, 0, "-", "math"], [37, 5, 0, "-", "midi"], [38, 5, 0, "-", "mixer"], [39, 5, 0, "-", "mouse"], [43, 5, 0, "-", "pixelcopy"], [44, 9, 1, "", "quit"], [44, 9, 1, "", "register_quit"], [46, 5, 0, "-", "scrap"], [44, 9, 1, "", "set_error"], [49, 5, 0, "-", "sndarray"], [50, 5, 0, "-", "sprite"], [52, 5, 0, "-", "surfarray"], [53, 5, 0, "-", "tests"], [54, 5, 0, "-", "time"], [56, 5, 0, "-", "transform"], [44, 5, 0, "-", "version"]], "pygame.BufferProxy": [[17, 7, 1, "", "length"], [17, 7, 1, "", "parent"], [17, 7, 1, "", "raw"], [17, 8, 1, "", "write"]], "pygame.Color": [[20, 7, 1, "", "a"], [20, 7, 1, "", "b"], [20, 7, 1, "", "cmy"], [20, 8, 1, "", "correct_gamma"], [20, 7, 1, "", "g"], [20, 8, 1, "", "grayscale"], [20, 7, 1, "", "hsla"], [20, 7, 1, "", "hsva"], [20, 7, 1, "", "i1i2i3"], [20, 8, 1, "", "lerp"], [20, 8, 1, "", "normalize"], [20, 8, 1, "", "premul_alpha"], [20, 7, 1, "", "r"], [20, 8, 1, "", "set_length"], [20, 8, 1, "", "update"]], "pygame.Overlay": [[41, 8, 1, "", "display"], [41, 8, 1, "", "get_hardware"], [41, 8, 1, "", "set_location"]], "pygame.PixelArray": [[42, 8, 1, "", "close"], [42, 8, 1, "", "compare"], [42, 8, 1, "", "extract"], [42, 7, 1, "", "itemsize"], [42, 8, 1, "", "make_surface"], [42, 7, 1, "", "ndim"], [42, 8, 1, "", "replace"], [42, 7, 1, "", "shape"], [42, 7, 1, "", "strides"], [42, 7, 1, "", "surface"], [42, 8, 1, "", "transpose"]], "pygame.Rect": [[45, 8, 1, "", "clamp"], [45, 8, 1, "", "clamp_ip"], [45, 8, 1, "", "clip"], [45, 8, 1, "", "clipline"], [45, 8, 1, "", "collidedict"], [45, 8, 1, "", "collidedictall"], [45, 8, 1, "", "collidelist"], [45, 8, 1, "", "collidelistall"], [45, 8, 1, "", "collideobjects"], [45, 8, 1, "", "collideobjectsall"], [45, 8, 1, "", "collidepoint"], [45, 8, 1, "", "colliderect"], [45, 8, 1, "", "contains"], [45, 8, 1, "", "copy"], [45, 8, 1, "", "fit"], [45, 8, 1, "", "inflate"], [45, 8, 1, "", "inflate_ip"], [45, 8, 1, "", "move"], [45, 8, 1, "", "move_ip"], [45, 8, 1, "", "normalize"], [45, 8, 1, "", "scale_by"], [45, 8, 1, "", "scale_by_ip"], [45, 8, 1, "", "union"], [45, 8, 1, "", "union_ip"], [45, 8, 1, "", "unionall"], [45, 8, 1, "", "unionall_ip"], [45, 8, 1, "", "update"]], "pygame.Surface": [[51, 7, 1, "", "_pixels_address"], [51, 8, 1, "", "blit"], [51, 8, 1, "", "blits"], [51, 8, 1, "", "convert"], [51, 8, 1, "", "convert_alpha"], [51, 8, 1, "", "copy"], [51, 8, 1, "", "fill"], [51, 8, 1, "", "get_abs_offset"], [51, 8, 1, "", "get_abs_parent"], [51, 8, 1, "", "get_alpha"], [51, 8, 1, "", "get_at"], [51, 8, 1, "", "get_at_mapped"], [51, 8, 1, "", "get_bitsize"], [51, 8, 1, "", "get_bounding_rect"], [51, 8, 1, "", "get_buffer"], [51, 8, 1, "", "get_bytesize"], [51, 8, 1, "", "get_clip"], [51, 8, 1, "", "get_colorkey"], [51, 8, 1, "", "get_flags"], [51, 8, 1, "", "get_height"], [51, 8, 1, "", "get_locked"], [51, 8, 1, "", "get_locks"], [51, 8, 1, "", "get_losses"], [51, 8, 1, "", "get_masks"], [51, 8, 1, "", "get_offset"], [51, 8, 1, "", "get_palette"], [51, 8, 1, "", "get_palette_at"], [51, 8, 1, "", "get_parent"], [51, 8, 1, "", "get_pitch"], [51, 8, 1, "", "get_rect"], [51, 8, 1, "", "get_shifts"], [51, 8, 1, "", "get_size"], [51, 8, 1, "", "get_view"], [51, 8, 1, "", "get_width"], [51, 8, 1, "", "lock"], [51, 8, 1, "", "map_rgb"], [51, 8, 1, "", "mustlock"], [51, 8, 1, "", "premul_alpha"], [51, 8, 1, "", "scroll"], [51, 8, 1, "", "set_alpha"], [51, 8, 1, "", "set_at"], [51, 8, 1, "", "set_clip"], [51, 8, 1, "", "set_colorkey"], [51, 8, 1, "", "set_masks"], [51, 8, 1, "", "set_palette"], [51, 8, 1, "", "set_palette_at"], [51, 8, 1, "", "set_shifts"], [51, 8, 1, "", "subsurface"], [51, 8, 1, "", "unlock"], [51, 8, 1, "", "unmap_rgb"]], "pygame._sdl2": [[47, 5, 0, "-", "controller"], [55, 5, 0, "-", "touch"], [48, 5, 0, "-", "video"]], "pygame._sdl2.controller": [[47, 6, 1, "", "Controller"], [47, 9, 1, "", "get_count"], [47, 9, 1, "", "get_eventstate"], [47, 9, 1, "", "get_init"], [47, 9, 1, "", "init"], [47, 9, 1, "", "is_controller"], [47, 9, 1, "", "name_forindex"], [47, 9, 1, "", "quit"], [47, 9, 1, "", "set_eventstate"]], "pygame._sdl2.controller.Controller": [[47, 8, 1, "", "as_joystick"], [47, 8, 1, "", "attached"], [47, 8, 1, "", "from_joystick"], [47, 8, 1, "", "get_axis"], [47, 8, 1, "", "get_button"], [47, 8, 1, "", "get_init"], [47, 8, 1, "", "get_mapping"], [47, 8, 1, "", "quit"], [47, 8, 1, "", "rumble"], [47, 8, 1, "", "set_mapping"], [47, 8, 1, "", "stop_rumble"]], "pygame._sdl2.touch": [[55, 9, 1, "", "get_device"], [55, 9, 1, "", "get_finger"], [55, 9, 1, "", "get_num_devices"], [55, 9, 1, "", "get_num_fingers"]], "pygame._sdl2.video": [[48, 6, 1, "", "Image"], [48, 6, 1, "", "Renderer"], [48, 6, 1, "", "Texture"], [48, 6, 1, "", "Window"]], "pygame._sdl2.video.Image": [[48, 7, 1, "", "alpha"], [48, 7, 1, "", "angle"], [48, 7, 1, "", "blend_mode"], [48, 7, 1, "", "color"], [48, 8, 1, "", "draw"], [48, 7, 1, "", "flip_x"], [48, 7, 1, "", "flip_y"], [48, 8, 1, "", "get_rect"], [48, 7, 1, "", "origin"], [48, 7, 1, "", "srcrect"], [48, 7, 1, "", "texture"]], "pygame._sdl2.video.Renderer": [[48, 8, 1, "", "blit"], [48, 8, 1, "", "clear"], [48, 7, 1, "", "draw_blend_mode"], [48, 7, 1, "", "draw_color"], [48, 8, 1, "", "draw_line"], [48, 8, 1, "", "draw_point"], [48, 8, 1, "", "draw_rect"], [48, 8, 1, "", "fill_rect"], [48, 8, 1, "", "from_window"], [48, 8, 1, "", "get_viewport"], [48, 7, 1, "", "logical_size"], [48, 8, 1, "", "present"], [48, 7, 1, "", "scale"], [48, 8, 1, "", "set_viewport"], [48, 7, 1, "", "target"], [48, 8, 1, "", "to_surface"]], "pygame._sdl2.video.Texture": [[48, 7, 1, "", "alpha"], [48, 7, 1, "", "blend_mode"], [48, 7, 1, "", "color"], [48, 8, 1, "", "draw"], [48, 8, 1, "", "from_surface"], [48, 8, 1, "", "get_rect"], [48, 7, 1, "", "height"], [48, 7, 1, "", "renderer"], [48, 8, 1, "", "update"], [48, 7, 1, "", "width"]], "pygame._sdl2.video.Window": [[48, 7, 1, "", "borderless"], [48, 8, 1, "", "destroy"], [48, 7, 1, "", "display_index"], [48, 8, 1, "", "focus"], [48, 8, 1, "", "from_display_module"], [48, 8, 1, "", "from_window"], [48, 7, 1, "", "grab"], [48, 8, 1, "", "hide"], [48, 7, 1, "", "id"], [48, 8, 1, "", "maximize"], [48, 8, 1, "", "minimize"], [48, 7, 1, "", "opacity"], [48, 7, 1, "", "position"], [48, 7, 1, "", "relative_mouse"], [48, 7, 1, "", "resizable"], [48, 8, 1, "", "restore"], [48, 8, 1, "", "set_fullscreen"], [48, 8, 1, "", "set_icon"], [48, 8, 1, "", "set_modal_for"], [48, 8, 1, "", "set_windowed"], [48, 8, 1, "", "show"], [48, 7, 1, "", "size"], [48, 7, 1, "", "title"]], "pygame.camera": [[18, 6, 1, "", "Camera"], [18, 9, 1, "", "colorspace"], [18, 9, 1, "", "get_backends"], [18, 9, 1, "", "init"], [18, 9, 1, "", "list_cameras"]], "pygame.camera.Camera": [[18, 8, 1, "", "get_controls"], [18, 8, 1, "", "get_image"], [18, 8, 1, "", "get_raw"], [18, 8, 1, "", "get_size"], [18, 8, 1, "", "query_image"], [18, 8, 1, "", "set_controls"], [18, 8, 1, "", "start"], [18, 8, 1, "", "stop"]], "pygame.cdrom": [[19, 6, 1, "", "CD"], [19, 9, 1, "", "get_count"], [19, 9, 1, "", "get_init"], [19, 9, 1, "", "init"], [19, 9, 1, "", "quit"]], "pygame.cdrom.CD": [[19, 8, 1, "", "eject"], [19, 8, 1, "", "get_all"], [19, 8, 1, "", "get_busy"], [19, 8, 1, "", "get_current"], [19, 8, 1, "", "get_empty"], [19, 8, 1, "", "get_id"], [19, 8, 1, "", "get_init"], [19, 8, 1, "", "get_name"], [19, 8, 1, "", "get_numtracks"], [19, 8, 1, "", "get_paused"], [19, 8, 1, "", "get_track_audio"], [19, 8, 1, "", "get_track_length"], [19, 8, 1, "", "get_track_start"], [19, 8, 1, "", "init"], [19, 8, 1, "", "pause"], [19, 8, 1, "", "play"], [19, 8, 1, "", "quit"], [19, 8, 1, "", "resume"], [19, 8, 1, "", "stop"]], "pygame.cursors": [[22, 6, 1, "", "Cursor"], [22, 9, 1, "", "compile"], [22, 9, 1, "", "load_xbm"]], "pygame.cursors.Cursor": [[22, 8, 1, "", "copy"], [22, 7, 1, "", "data"], [22, 7, 1, "", "type"]], "pygame.display": [[23, 9, 1, "", "Info"], [23, 9, 1, "", "flip"], [23, 9, 1, "", "get_active"], [23, 9, 1, "", "get_allow_screensaver"], [23, 9, 1, "", "get_caption"], [23, 9, 1, "", "get_desktop_sizes"], [23, 9, 1, "", "get_driver"], [23, 9, 1, "", "get_init"], [23, 9, 1, "", "get_num_displays"], [23, 9, 1, "", "get_surface"], [23, 9, 1, "", "get_window_size"], [23, 9, 1, "", "get_wm_info"], [23, 9, 1, "", "gl_get_attribute"], [23, 9, 1, "", "gl_set_attribute"], [23, 9, 1, "", "iconify"], [23, 9, 1, "", "init"], [23, 9, 1, "", "list_modes"], [23, 9, 1, "", "mode_ok"], [23, 9, 1, "", "quit"], [23, 9, 1, "", "set_allow_screensaver"], [23, 9, 1, "", "set_caption"], [23, 9, 1, "", "set_gamma"], [23, 9, 1, "", "set_gamma_ramp"], [23, 9, 1, "", "set_icon"], [23, 9, 1, "", "set_mode"], [23, 9, 1, "", "set_palette"], [23, 9, 1, "", "toggle_fullscreen"], [23, 9, 1, "", "update"]], "pygame.draw": [[24, 9, 1, "", "aaline"], [24, 9, 1, "", "aalines"], [24, 9, 1, "", "arc"], [24, 9, 1, "", "circle"], [24, 9, 1, "", "ellipse"], [24, 9, 1, "", "line"], [24, 9, 1, "", "lines"], [24, 9, 1, "", "polygon"], [24, 9, 1, "", "rect"]], "pygame.event": [[25, 6, 1, "", "Event"], [25, 9, 1, "", "clear"], [25, 9, 1, "", "custom_type"], [25, 9, 1, "", "event_name"], [25, 9, 1, "", "get"], [25, 9, 1, "", "get_blocked"], [25, 9, 1, "", "get_grab"], [25, 9, 1, "", "get_keyboard_grab"], [25, 9, 1, "", "peek"], [25, 9, 1, "", "poll"], [25, 9, 1, "", "post"], [25, 9, 1, "", "pump"], [25, 9, 1, "", "set_allowed"], [25, 9, 1, "", "set_blocked"], [25, 9, 1, "", "set_grab"], [25, 9, 1, "", "set_keyboard_grab"], [25, 9, 1, "", "wait"]], "pygame.event.Event": [[25, 7, 1, "", "__dict__"], [25, 7, 1, "", "type"]], "pygame.examples.aliens": [[26, 9, 1, "", "main"]], "pygame.examples.arraydemo": [[26, 9, 1, "", "main"]], "pygame.examples.blend_fill": [[26, 9, 1, "", "main"]], "pygame.examples.blit_blends": [[26, 9, 1, "", "main"]], "pygame.examples.camera": [[26, 9, 1, "", "main"]], "pygame.examples.chimp": [[26, 9, 1, "", "main"]], "pygame.examples.cursors": [[26, 9, 1, "", "main"]], "pygame.examples.eventlist": [[26, 9, 1, "", "main"]], "pygame.examples.fonty": [[26, 9, 1, "", "main"]], "pygame.examples.freetype_misc": [[26, 9, 1, "", "main"]], "pygame.examples.glcube": [[26, 9, 1, "", "main"]], "pygame.examples.headless_no_windows_needed": [[26, 9, 1, "", "main"]], "pygame.examples.joystick": [[26, 9, 1, "", "main"]], "pygame.examples.liquid": [[26, 9, 1, "", "main"]], "pygame.examples.mask": [[26, 9, 1, "", "main"]], "pygame.examples.midi": [[26, 9, 1, "", "main"]], "pygame.examples.moveit": [[26, 9, 1, "", "main"]], "pygame.examples.pixelarray": [[26, 9, 1, "", "main"]], "pygame.examples.playmus": [[26, 9, 1, "", "main"]], "pygame.examples.scaletest": [[26, 9, 1, "", "main"]], "pygame.examples.scrap_clipboard": [[26, 9, 1, "", "main"]], "pygame.examples.scroll": [[26, 9, 1, "", "main"]], "pygame.examples.sound": [[26, 9, 1, "", "main"]], "pygame.examples.sound_array_demos": [[26, 9, 1, "", "main"]], "pygame.examples.stars": [[26, 9, 1, "", "main"]], "pygame.examples.testsprite": [[26, 9, 1, "", "main"]], "pygame.examples.vgrade": [[26, 9, 1, "", "main"]], "pygame.fastevent": [[27, 9, 1, "", "get"], [27, 9, 1, "", "get_init"], [27, 9, 1, "", "init"], [27, 9, 1, "", "poll"], [27, 9, 1, "", "post"], [27, 9, 1, "", "pump"], [27, 9, 1, "", "wait"]], "pygame.font": [[28, 6, 1, "", "Font"], [28, 9, 1, "", "SysFont"], [28, 9, 1, "", "get_default_font"], [28, 9, 1, "", "get_fonts"], [28, 9, 1, "", "get_init"], [28, 9, 1, "", "get_sdl_ttf_version"], [28, 9, 1, "", "init"], [28, 9, 1, "", "match_font"], [28, 9, 1, "", "quit"]], "pygame.font.Font": [[28, 7, 1, "", "bold"], [28, 8, 1, "", "get_ascent"], [28, 8, 1, "", "get_bold"], [28, 8, 1, "", "get_descent"], [28, 8, 1, "", "get_height"], [28, 8, 1, "", "get_italic"], [28, 8, 1, "", "get_linesize"], [28, 8, 1, "", "get_strikethrough"], [28, 8, 1, "", "get_underline"], [28, 7, 1, "", "italic"], [28, 8, 1, "", "metrics"], [28, 8, 1, "", "render"], [28, 8, 1, "", "set_bold"], [28, 8, 1, "", "set_italic"], [28, 8, 1, "", "set_script"], [28, 8, 1, "", "set_strikethrough"], [28, 8, 1, "", "set_underline"], [28, 8, 1, "", "size"], [28, 7, 1, "", "strikethrough"], [28, 7, 1, "", "underline"]], "pygame.freetype": [[29, 6, 1, "", "Font"], [29, 9, 1, "", "SysFont"], [29, 9, 1, "", "get_cache_size"], [29, 9, 1, "", "get_default_font"], [29, 9, 1, "", "get_default_resolution"], [29, 9, 1, "", "get_error"], [29, 9, 1, "", "get_init"], [29, 9, 1, "", "get_version"], [29, 9, 1, "", "init"], [29, 9, 1, "", "quit"], [29, 9, 1, "", "set_default_resolution"], [29, 9, 1, "", "was_init"]], "pygame.freetype.Font": [[29, 7, 1, "", "antialiased"], [29, 7, 1, "", "ascender"], [29, 7, 1, "", "bgcolor"], [29, 7, 1, "", "descender"], [29, 7, 1, "", "fgcolor"], [29, 7, 1, "", "fixed_sizes"], [29, 7, 1, "", "fixed_width"], [29, 8, 1, "", "get_metrics"], [29, 8, 1, "", "get_rect"], [29, 8, 1, "", "get_sized_ascender"], [29, 8, 1, "", "get_sized_descender"], [29, 8, 1, "", "get_sized_glyph_height"], [29, 8, 1, "", "get_sized_height"], [29, 8, 1, "", "get_sizes"], [29, 7, 1, "", "height"], [29, 7, 1, "", "kerning"], [29, 7, 1, "", "name"], [29, 7, 1, "", "oblique"], [29, 7, 1, "", "origin"], [29, 7, 1, "", "pad"], [29, 7, 1, "", "path"], [29, 8, 1, "", "render"], [29, 8, 1, "", "render_raw"], [29, 8, 1, "", "render_raw_to"], [29, 8, 1, "", "render_to"], [29, 7, 1, "", "resolution"], [29, 7, 1, "", "rotation"], [29, 7, 1, "", "scalable"], [29, 7, 1, "", "size"], [29, 7, 1, "", "strength"], [29, 7, 1, "", "strong"], [29, 7, 1, "", "style"], [29, 7, 1, "", "ucs4"], [29, 7, 1, "", "underline"], [29, 7, 1, "", "underline_adjustment"], [29, 7, 1, "", "use_bitmap_strikes"], [29, 7, 1, "", "vertical"], [29, 7, 1, "", "wide"]], "pygame.gfxdraw": [[30, 9, 1, "", "aacircle"], [30, 9, 1, "", "aaellipse"], [30, 9, 1, "", "aapolygon"], [30, 9, 1, "", "aatrigon"], [30, 9, 1, "", "arc"], [30, 9, 1, "", "bezier"], [30, 9, 1, "", "box"], [30, 9, 1, "", "circle"], [30, 9, 1, "", "ellipse"], [30, 9, 1, "", "filled_circle"], [30, 9, 1, "", "filled_ellipse"], [30, 9, 1, "", "filled_polygon"], [30, 9, 1, "", "filled_trigon"], [30, 9, 1, "", "hline"], [30, 9, 1, "", "line"], [30, 9, 1, "", "pie"], [30, 9, 1, "", "pixel"], [30, 9, 1, "", "polygon"], [30, 9, 1, "", "rectangle"], [30, 9, 1, "", "textured_polygon"], [30, 9, 1, "", "trigon"], [30, 9, 1, "", "vline"]], "pygame.image": [[31, 9, 1, "", "frombuffer"], [31, 9, 1, "", "frombytes"], [31, 9, 1, "", "fromstring"], [31, 9, 1, "", "get_extended"], [31, 9, 1, "", "get_sdl_image_version"], [31, 9, 1, "", "load"], [31, 9, 1, "", "load_basic"], [31, 9, 1, "", "load_extended"], [31, 9, 1, "", "save"], [31, 9, 1, "", "save_extended"], [31, 9, 1, "", "tobytes"], [31, 9, 1, "", "tostring"]], "pygame.joystick": [[32, 6, 1, "", "Joystick"], [32, 9, 1, "", "get_count"], [32, 9, 1, "", "get_init"], [32, 9, 1, "", "init"], [32, 9, 1, "", "quit"]], "pygame.joystick.Joystick": [[32, 8, 1, "", "get_axis"], [32, 8, 1, "", "get_ball"], [32, 8, 1, "", "get_button"], [32, 8, 1, "", "get_guid"], [32, 8, 1, "", "get_hat"], [32, 8, 1, "", "get_id"], [32, 8, 1, "", "get_init"], [32, 8, 1, "", "get_instance_id"], [32, 8, 1, "", "get_name"], [32, 8, 1, "", "get_numaxes"], [32, 8, 1, "", "get_numballs"], [32, 8, 1, "", "get_numbuttons"], [32, 8, 1, "", "get_numhats"], [32, 8, 1, "", "get_power_level"], [32, 8, 1, "", "init"], [32, 8, 1, "", "quit"], [32, 8, 1, "", "rumble"], [32, 8, 1, "", "stop_rumble"]], "pygame.key": [[33, 9, 1, "", "get_focused"], [33, 9, 1, "", "get_mods"], [33, 9, 1, "", "get_pressed"], [33, 9, 1, "", "get_repeat"], [33, 9, 1, "", "key_code"], [33, 9, 1, "", "name"], [33, 9, 1, "", "set_mods"], [33, 9, 1, "", "set_repeat"], [33, 9, 1, "", "set_text_input_rect"], [33, 9, 1, "", "start_text_input"], [33, 9, 1, "", "stop_text_input"]], "pygame.mask": [[35, 6, 1, "", "Mask"], [35, 9, 1, "", "from_surface"], [35, 9, 1, "", "from_threshold"]], "pygame.mask.Mask": [[35, 8, 1, "", "angle"], [35, 8, 1, "", "centroid"], [35, 8, 1, "", "clear"], [35, 8, 1, "", "connected_component"], [35, 8, 1, "", "connected_components"], [35, 8, 1, "", "convolve"], [35, 8, 1, "", "copy"], [35, 8, 1, "", "count"], [35, 8, 1, "", "draw"], [35, 8, 1, "", "erase"], [35, 8, 1, "", "fill"], [35, 8, 1, "", "get_at"], [35, 8, 1, "", "get_bounding_rects"], [35, 8, 1, "", "get_rect"], [35, 8, 1, "", "get_size"], [35, 8, 1, "", "invert"], [35, 8, 1, "", "outline"], [35, 8, 1, "", "overlap"], [35, 8, 1, "", "overlap_area"], [35, 8, 1, "", "overlap_mask"], [35, 8, 1, "", "scale"], [35, 8, 1, "", "set_at"], [35, 8, 1, "", "to_surface"]], "pygame.math": [[36, 6, 1, "", "Vector2"], [36, 6, 1, "", "Vector3"], [36, 9, 1, "", "clamp"], [36, 9, 1, "", "lerp"]], "pygame.math.Vector2": [[36, 8, 1, "", "angle_to"], [36, 8, 1, "", "as_polar"], [36, 8, 1, "", "clamp_magnitude"], [36, 8, 1, "", "clamp_magnitude_ip"], [36, 8, 1, "", "copy"], [36, 8, 1, "", "cross"], [36, 8, 1, "", "distance_squared_to"], [36, 8, 1, "", "distance_to"], [36, 8, 1, "", "dot"], [36, 8, 1, "", "elementwise"], [36, 7, 1, "", "epsilon"], [36, 8, 1, "", "from_polar"], [36, 8, 1, "", "is_normalized"], [36, 8, 1, "", "length"], [36, 8, 1, "", "length_squared"], [36, 8, 1, "", "lerp"], [36, 8, 1, "", "magnitude"], [36, 8, 1, "", "magnitude_squared"], [36, 8, 1, "", "move_towards"], [36, 8, 1, "", "move_towards_ip"], [36, 8, 1, "", "normalize"], [36, 8, 1, "", "normalize_ip"], [36, 8, 1, "", "project"], [36, 8, 1, "", "reflect"], [36, 8, 1, "", "reflect_ip"], [36, 8, 1, "", "rotate"], [36, 8, 1, "", "rotate_ip"], [36, 8, 1, "", "rotate_ip_rad"], [36, 8, 1, "", "rotate_rad"], [36, 8, 1, "", "rotate_rad_ip"], [36, 8, 1, "", "scale_to_length"], [36, 8, 1, "", "slerp"], [36, 8, 1, "", "update"]], "pygame.math.Vector3": [[36, 8, 1, "", "angle_to"], [36, 8, 1, "", "as_spherical"], [36, 8, 1, "", "clamp_magnitude"], [36, 8, 1, "", "clamp_magnitude_ip"], [36, 8, 1, "", "copy"], [36, 8, 1, "", "cross"], [36, 8, 1, "", "distance_squared_to"], [36, 8, 1, "", "distance_to"], [36, 8, 1, "", "dot"], [36, 8, 1, "", "elementwise"], [36, 7, 1, "", "epsilon"], [36, 8, 1, "", "from_spherical"], [36, 8, 1, "", "is_normalized"], [36, 8, 1, "", "length"], [36, 8, 1, "", "length_squared"], [36, 8, 1, "", "lerp"], [36, 8, 1, "", "magnitude"], [36, 8, 1, "", "magnitude_squared"], [36, 8, 1, "", "move_towards"], [36, 8, 1, "", "move_towards_ip"], [36, 8, 1, "", "normalize"], [36, 8, 1, "", "normalize_ip"], [36, 8, 1, "", "project"], [36, 8, 1, "", "reflect"], [36, 8, 1, "", "reflect_ip"], [36, 8, 1, "", "rotate"], [36, 8, 1, "", "rotate_ip"], [36, 8, 1, "", "rotate_ip_rad"], [36, 8, 1, "", "rotate_rad"], [36, 8, 1, "", "rotate_rad_ip"], [36, 8, 1, "", "rotate_x"], [36, 8, 1, "", "rotate_x_ip"], [36, 8, 1, "", "rotate_x_ip_rad"], [36, 8, 1, "", "rotate_x_rad"], [36, 8, 1, "", "rotate_x_rad_ip"], [36, 8, 1, "", "rotate_y"], [36, 8, 1, "", "rotate_y_ip"], [36, 8, 1, "", "rotate_y_ip_rad"], [36, 8, 1, "", "rotate_y_rad"], [36, 8, 1, "", "rotate_y_rad_ip"], [36, 8, 1, "", "rotate_z"], [36, 8, 1, "", "rotate_z_ip"], [36, 8, 1, "", "rotate_z_ip_rad"], [36, 8, 1, "", "rotate_z_rad"], [36, 8, 1, "", "rotate_z_rad_ip"], [36, 8, 1, "", "scale_to_length"], [36, 8, 1, "", "slerp"], [36, 8, 1, "", "update"]], "pygame.midi": [[37, 6, 1, "", "Input"], [37, 10, 1, "", "MidiException"], [37, 6, 1, "", "Output"], [37, 9, 1, "", "frequency_to_midi"], [37, 9, 1, "", "get_count"], [37, 9, 1, "", "get_default_input_id"], [37, 9, 1, "", "get_default_output_id"], [37, 9, 1, "", "get_device_info"], [37, 9, 1, "", "get_init"], [37, 9, 1, "", "init"], [37, 9, 1, "", "midi_to_ansi_note"], [37, 9, 1, "", "midi_to_frequency"], [37, 9, 1, "", "midis2events"], [37, 9, 1, "", "quit"], [37, 9, 1, "", "time"]], "pygame.midi.Input": [[37, 8, 1, "", "close"], [37, 8, 1, "", "poll"], [37, 8, 1, "", "read"]], "pygame.midi.Output": [[37, 8, 1, "", "abort"], [37, 8, 1, "", "close"], [37, 8, 1, "", "note_off"], [37, 8, 1, "", "note_on"], [37, 8, 1, "", "pitch_bend"], [37, 8, 1, "", "set_instrument"], [37, 8, 1, "", "write"], [37, 8, 1, "", "write_short"], [37, 8, 1, "", "write_sys_ex"]], "pygame.mixer": [[38, 6, 1, "", "Channel"], [38, 6, 1, "", "Sound"], [38, 9, 1, "", "fadeout"], [38, 9, 1, "", "find_channel"], [38, 9, 1, "", "get_busy"], [38, 9, 1, "", "get_init"], [38, 9, 1, "", "get_num_channels"], [38, 9, 1, "", "get_sdl_mixer_version"], [38, 9, 1, "", "init"], [40, 5, 0, "-", "music"], [38, 9, 1, "", "pause"], [38, 9, 1, "", "pre_init"], [38, 9, 1, "", "quit"], [38, 9, 1, "", "set_num_channels"], [38, 9, 1, "", "set_reserved"], [38, 9, 1, "", "stop"], [38, 9, 1, "", "unpause"]], "pygame.mixer.Channel": [[38, 8, 1, "", "fadeout"], [38, 8, 1, "", "get_busy"], [38, 8, 1, "", "get_endevent"], [38, 8, 1, "", "get_queue"], [38, 8, 1, "", "get_sound"], [38, 8, 1, "", "get_volume"], [38, 8, 1, "", "pause"], [38, 8, 1, "", "play"], [38, 8, 1, "", "queue"], [38, 8, 1, "", "set_endevent"], [38, 8, 1, "", "set_volume"], [38, 8, 1, "", "stop"], [38, 8, 1, "", "unpause"]], "pygame.mixer.Sound": [[38, 8, 1, "", "fadeout"], [38, 8, 1, "", "get_length"], [38, 8, 1, "", "get_num_channels"], [38, 8, 1, "", "get_raw"], [38, 8, 1, "", "get_volume"], [38, 8, 1, "", "play"], [38, 8, 1, "", "set_volume"], [38, 8, 1, "", "stop"]], "pygame.mixer.music": [[40, 9, 1, "", "fadeout"], [40, 9, 1, "", "get_busy"], [40, 9, 1, "", "get_endevent"], [40, 9, 1, "", "get_pos"], [40, 9, 1, "", "get_volume"], [40, 9, 1, "", "load"], [40, 9, 1, "", "pause"], [40, 9, 1, "", "play"], [40, 9, 1, "", "queue"], [40, 9, 1, "", "rewind"], [40, 9, 1, "", "set_endevent"], [40, 9, 1, "", "set_pos"], [40, 9, 1, "", "set_volume"], [40, 9, 1, "", "stop"], [40, 9, 1, "", "unload"], [40, 9, 1, "", "unpause"]], "pygame.mouse": [[39, 9, 1, "", "get_cursor"], [39, 9, 1, "", "get_focused"], [39, 9, 1, "", "get_pos"], [39, 9, 1, "", "get_pressed"], [39, 9, 1, "", "get_rel"], [39, 9, 1, "", "get_visible"], [39, 9, 1, "", "set_cursor"], [39, 9, 1, "", "set_pos"], [39, 9, 1, "", "set_visible"]], "pygame.pixelcopy": [[43, 9, 1, "", "array_to_surface"], [43, 9, 1, "", "make_surface"], [43, 9, 1, "", "map_array"], [43, 9, 1, "", "surface_to_array"]], "pygame.scrap": [[46, 9, 1, "", "contains"], [46, 9, 1, "", "get"], [46, 9, 1, "", "get_init"], [46, 9, 1, "", "get_types"], [46, 9, 1, "", "init"], [46, 9, 1, "", "lost"], [46, 9, 1, "", "put"], [46, 9, 1, "", "set_mode"]], "pygame.sndarray": [[49, 9, 1, "", "array"], [49, 9, 1, "", "get_arraytype"], [49, 9, 1, "", "get_arraytypes"], [49, 9, 1, "", "make_sound"], [49, 9, 1, "", "samples"], [49, 9, 1, "", "use_arraytype"]], "pygame.sprite": [[50, 6, 1, "", "DirtySprite"], [50, 6, 1, "", "Group"], [50, 9, 1, "", "GroupSingle"], [50, 6, 1, "", "LayeredDirty"], [50, 6, 1, "", "LayeredUpdates"], [50, 9, 1, "", "OrderedUpdates"], [50, 6, 1, "", "RenderClear"], [50, 6, 1, "", "RenderPlain"], [50, 6, 1, "", "RenderUpdates"], [50, 6, 1, "", "Sprite"], [50, 6, 1, "", "WeakDirtySprite"], [50, 6, 1, "", "WeakSprite"], [50, 9, 1, "", "collide_circle"], [50, 9, 1, "", "collide_circle_ratio"], [50, 9, 1, "", "collide_mask"], [50, 9, 1, "", "collide_rect"], [50, 9, 1, "", "collide_rect_ratio"], [50, 9, 1, "", "groupcollide"], [50, 9, 1, "", "spritecollide"], [50, 9, 1, "", "spritecollideany"]], "pygame.sprite.Group": [[50, 8, 1, "", "add"], [50, 8, 1, "", "clear"], [50, 8, 1, "", "copy"], [50, 8, 1, "", "draw"], [50, 8, 1, "", "empty"], [50, 8, 1, "", "has"], [50, 8, 1, "", "remove"], [50, 8, 1, "", "sprites"], [50, 8, 1, "", "update"]], "pygame.sprite.LayeredDirty": [[50, 8, 1, "", "change_layer"], [50, 8, 1, "", "clear"], [50, 8, 1, "", "draw"], [50, 8, 1, "", "get_clip"], [50, 8, 1, "", "repaint_rect"], [50, 8, 1, "", "set_clip"], [50, 8, 1, "", "set_timing_threshold"], [50, 8, 1, "", "set_timing_treshold"]], "pygame.sprite.LayeredUpdates": [[50, 8, 1, "", "add"], [50, 8, 1, "", "change_layer"], [50, 8, 1, "", "draw"], [50, 8, 1, "", "get_bottom_layer"], [50, 8, 1, "", "get_layer_of_sprite"], [50, 8, 1, "", "get_sprite"], [50, 8, 1, "", "get_sprites_at"], [50, 8, 1, "", "get_sprites_from_layer"], [50, 8, 1, "", "get_top_layer"], [50, 8, 1, "", "get_top_sprite"], [50, 8, 1, "", "layers"], [50, 8, 1, "", "move_to_back"], [50, 8, 1, "", "move_to_front"], [50, 8, 1, "", "remove_sprites_of_layer"], [50, 8, 1, "", "sprites"], [50, 8, 1, "", "switch_layer"]], "pygame.sprite.RenderUpdates": [[50, 8, 1, "", "draw"]], "pygame.sprite.Sprite": [[50, 8, 1, "", "add"], [50, 8, 1, "", "alive"], [50, 8, 1, "", "groups"], [50, 8, 1, "", "kill"], [50, 8, 1, "", "remove"], [50, 8, 1, "", "update"]], "pygame.surfarray": [[52, 9, 1, "", "array2d"], [52, 9, 1, "", "array3d"], [52, 9, 1, "", "array_alpha"], [52, 9, 1, "", "array_blue"], [52, 9, 1, "", "array_colorkey"], [52, 9, 1, "", "array_green"], [52, 9, 1, "", "array_red"], [52, 9, 1, "", "blit_array"], [52, 9, 1, "", "get_arraytype"], [52, 9, 1, "", "get_arraytypes"], [52, 9, 1, "", "make_surface"], [52, 9, 1, "", "map_array"], [52, 9, 1, "", "pixels2d"], [52, 9, 1, "", "pixels3d"], [52, 9, 1, "", "pixels_alpha"], [52, 9, 1, "", "pixels_blue"], [52, 9, 1, "", "pixels_green"], [52, 9, 1, "", "pixels_red"], [52, 9, 1, "", "use_arraytype"]], "pygame.tests": [[53, 9, 1, "", "run"]], "pygame.time": [[54, 6, 1, "", "Clock"], [54, 9, 1, "", "delay"], [54, 9, 1, "", "get_ticks"], [54, 9, 1, "", "set_timer"], [54, 9, 1, "", "wait"]], "pygame.time.Clock": [[54, 8, 1, "", "get_fps"], [54, 8, 1, "", "get_rawtime"], [54, 8, 1, "", "get_time"], [54, 8, 1, "", "tick"], [54, 8, 1, "", "tick_busy_loop"]], "pygame.transform": [[56, 9, 1, "", "average_color"], [56, 9, 1, "", "average_surfaces"], [56, 9, 1, "", "chop"], [56, 9, 1, "", "flip"], [56, 9, 1, "", "get_smoothscale_backend"], [56, 9, 1, "", "grayscale"], [56, 9, 1, "", "laplacian"], [56, 9, 1, "", "rotate"], [56, 9, 1, "", "rotozoom"], [56, 9, 1, "", "scale"], [56, 9, 1, "", "scale2x"], [56, 9, 1, "", "scale_by"], [56, 9, 1, "", "set_smoothscale_backend"], [56, 9, 1, "", "smoothscale"], [56, 9, 1, "", "smoothscale_by"], [56, 9, 1, "", "threshold"]], "pygame.version": [[44, 11, 1, "", "SDL"], [44, 11, 1, "", "rev"], [44, 11, 1, "", "ver"], [44, 11, 1, "", "vernum"]]}, "objnames": {"0": ["c", "macro", "C macro"], "1": ["c", "function", "C function"], "2": ["c", "functionParam", "C function parameter"], "3": ["c", "member", "C member"], "4": ["c", "type", "C type"], "5": ["py", "module", "Python module"], "6": ["py", "class", "Python class"], "7": ["py", "attribute", "Python attribute"], "8": ["py", "method", "Python method"], "9": ["py", "function", "Python function"], "10": ["py", "exception", "Python exception"], "11": ["py", "data", "Python data"]}, "objtypes": {"0": "c:macro", "1": "c:function", "2": "c:functionParam", "3": "c:member", "4": "c:type", "5": "py:module", "6": "py:class", "7": "py:attribute", "8": "py:method", "9": "py:function", "10": "py:exception", "11": "py:data"}, "terms": {"": [8, 11, 12, 14, 17, 20, 22, 23, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 54, 56, 58, 59, 61, 63, 64, 65, 67, 68, 69, 70, 71, 74, 84, 85, 86, 87, 88, 89], "0": [1, 2, 5, 6, 9, 11, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 50, 51, 52, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 86, 88, 89], "02778": 29, "08333": 29, "0d": 73, "0dev11": 32, "0x00": 20, "0x00000000": 51, "0x00000001": 51, "0x00000004": 51, "0x00000100": 51, "0x00001000": 51, "0x00002000": 51, "0x00004000": 51, "0x00010000": 51, "0x01000000": 51, "0x10": 37, "0x10000": 29, "0x10ffff": 29, "0x11": 37, "0x12": 37, "0x13": 37, "0x7d": 37, "0x90": 37, "0xaacce": 42, "0xc0": 37, "0xd800": 29, "0xdfff": 29, "0xf0": 37, "0xf7": 37, "0xff": 20, "0xff00ff": 42, "0xffff": 23, "0xrrggbb": 20, "0xrrggbbaa": 20, "0\uac1c": [78, 79], "0\ucc28\uc6d0": 81, "1": [1, 2, 3, 5, 8, 11, 13, 15, 16, 17, 18, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 33, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 83, 84], "10": [23, 24, 26, 32, 35, 36, 45, 54, 56, 58, 59, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85, 88, 89], "100": [20, 24, 36, 37, 51, 57, 62, 65, 84], "1000": [15, 50, 54], "101": 16, "1024": [37, 38], "105": 57, "1080": [23, 59], "1080p": 23, "10th": 35, "10\ub610\ub294": 79, "10\uc758": 76, "10\uc774": 77, "11": [32, 68, 69, 76, 77], "113": 24, "114": 42, "115": 24, "117": 26, "11\uc744": 77, "11\uc758": 76, "11\uc774": 77, "12": [10, 28, 29, 32, 44, 58, 66, 68, 76], "120": 22, "1234": 44, "125": 24, "127": [35, 37, 71, 72, 73, 79, 80, 81], "128": 65, "1280": [15, 58, 66, 84], "13": [32, 63, 65, 68, 76, 89], "135": 24, "14": [25, 32, 47, 68, 76], "140": [75, 76, 77, 78], "145": 57, "14\uc758": 76, "15": [24, 28, 32, 36, 45, 51, 58, 66, 68, 69, 76, 77], "150": [24, 45, 85], "1561": 16, "15924": 28, "15\uc758": 76, "16": [14, 22, 23, 25, 28, 29, 32, 38, 49, 59, 65, 68, 76], "1617": 16, "16711680": 59, "16bit": 38, "17": [63, 68, 76], "170": [42, 57, 58, 66], "179": 24, "17\uc5d0\uc11c\uc758": 76, "18": [24, 25, 36, 58, 63, 66, 68, 76], "187": [58, 66], "19": [68, 76], "192": 84, "1920": [23, 59], "19\ub294": 76, "19\uc5d0\uc11c": 76, "1d": [42, 73], "1e": 36, "1x1": 37, "1\uac1c": 78, "1\uac1c\uc758": 76, "1\uacfc": 77, "1\uc778": 75, "1\uc778\uc9c0": 81, "1\uc904\uc9dc\ub9ac": 76, "1\ucc28\uc6d0": 81, "1\ucd08\uc5d0": 77, "2": [1, 8, 11, 14, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 50, 51, 52, 54, 55, 56, 57, 58, 59, 61, 63, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 83, 84], "20": [22, 24, 28, 36, 45, 56, 65, 68, 71, 72, 73, 76, 79, 80, 81, 84], "200": [24, 45, 63], "2000": [15, 63, 84], "20000": 37, "2001": [16, 63], "2004": 18, "2021": 16, "2022": 84, "204": 42, "20500": 37, "20\uc77c": 79, "20\uc904\uc9dc\ub9ac": 76, "21": [37, 63], "210": 24, "214": 16, "22": 49, "220": [24, 75, 76, 77, 78], "22000": 49, "22050": 38, "225": 24, "23": 63, "235": [58, 66], "238": [42, 58, 66], "2380": 23, "24": [17, 18, 22, 24, 28, 31, 42, 51, 52, 56, 63, 65], "240": [63, 68, 69, 70, 76, 77, 78, 79, 80, 81], "24x24": 22, "25": [32, 56, 58, 66], "250": [24, 85], "255": [1, 20, 28, 29, 30, 32, 35, 42, 43, 50, 51, 56, 57, 58, 59, 65, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85], "256": [23, 37, 51], "260": 24, "27": 37, "270": [72, 73, 80, 81], "29": 16, "299": 42, "2d": [15, 35, 42, 43, 48, 52, 63, 64, 65, 68, 73, 76, 84], "2d\uc6a9": 76, "2pre": 36, "2x2": 65, "2\uac1c\ub97c": 80, "2\uac1c\uc758": 78, "2\ucc28\uc6d0": 81, "3": [1, 16, 17, 18, 20, 22, 23, 24, 25, 28, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 45, 47, 50, 51, 56, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 73, 76, 77, 78, 79, 81, 83, 84], "30": [22, 24, 29, 30, 32, 36, 38, 45, 53, 57, 63, 71, 84], "300": [15, 24, 45], "3072": 38, "30\uc73c\ub85c": 79, "315": 29, "32": [1, 14, 17, 18, 28, 29, 31, 35, 38, 51, 52, 56, 59, 65, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "320": [63, 68, 69, 70, 76, 77, 78, 79, 80, 81], "325": [72, 73, 80, 81], "32767": 47, "32768": 47, "32x32": 23, "33": 65, "35": 74, "359": 29, "35\ub144": 82, "36": [29, 85], "360": [20, 29, 36, 47, 58, 66], "390": 29, "3d": [23, 26, 43, 52, 63, 65, 67], "3f": 32, "3rd": 63, "3x3": 65, "3\uac1c\uc758": [76, 79], "4": [1, 3, 8, 17, 20, 24, 25, 28, 29, 33, 36, 37, 38, 39, 42, 47, 51, 56, 57, 58, 59, 60, 61, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 83, 84, 86, 89], "40": [15, 16, 22, 24, 54, 56, 62, 63, 71, 72, 73, 79, 80, 81, 84], "400": [22, 23, 24, 57], "4096": [37, 38], "42": 24, "425": [72, 73, 80, 81], "4321": 44, "438": 16, "44100": 38, "45": [29, 72, 73, 80, 81], "47": 89, "480": [18, 26, 39, 48, 57, 58, 59, 62, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 89], "480\uc73c\ub85c": 76, "4k": 23, "4th": 65, "4\uac1c\uc758": [76, 79], "5": [13, 20, 23, 24, 25, 29, 33, 35, 36, 37, 38, 39, 40, 42, 44, 45, 46, 49, 50, 57, 58, 61, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 83, 89], "50": [16, 22, 24, 26, 36, 45, 57, 65, 73, 81, 84, 85], "500": [32, 45], "500m": 37, "512": 38, "55": 56, "56": 24, "587": 42, "5x5": [73, 81], "5\uac00": 77, "5\uac1c\uc758": 76, "5\uc5d0": 78, "6": [24, 26, 32, 33, 36, 38, 44, 50, 61, 62, 65, 68, 69, 70, 71, 73, 76, 77, 78, 79, 81, 83], "60": [15, 16, 22, 24, 37, 38, 39, 58, 62, 66, 69, 71, 72, 73, 77, 79, 80, 81, 84, 89], "600": [22, 59, 62], "60\uc774\ub77c\ub294": 77, "63": 20, "64": [20, 29, 56, 58, 66], "640": [16, 18, 26, 39, 48, 57, 59, 62, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 89], "640x480": [23, 62, 65], "65": [37, 56], "65280": 59, "6x": 84, "6\uc744": 79, "7": [23, 32, 33, 43, 44, 63, 65, 68, 69, 70, 71, 76, 77, 78, 79, 83], "70": 24, "700": [23, 32], "72": 29, "720": [15, 29, 84], "75": [22, 24, 69, 77], "7\uc5d0\uc11c": 79, "7\uc758": 76, "8": [14, 17, 18, 20, 22, 23, 24, 26, 28, 29, 30, 31, 32, 33, 35, 38, 42, 43, 44, 46, 49, 50, 51, 52, 54, 56, 59, 62, 63, 65, 68, 69, 70, 76, 77, 78, 83, 89], "80": [24, 50, 58, 66], "800": 59, "8191": 37, "8192": 37, "8bit": 38, "8\uc758": 76, "9": [13, 15, 17, 18, 20, 23, 25, 26, 29, 30, 32, 33, 35, 36, 37, 38, 40, 42, 43, 44, 45, 46, 47, 51, 52, 56, 57, 65, 68, 69, 70, 76, 77, 78], "90": [24, 35, 36, 38, 56, 57, 58, 66, 89], "97": 33, "9\uc758": 76, "A": [1, 4, 7, 8, 11, 12, 14, 15, 17, 18, 20, 22, 23, 24, 25, 26, 29, 30, 32, 33, 35, 38, 39, 40, 42, 43, 45, 46, 49, 50, 51, 53, 54, 56, 58, 63, 64, 85, 86, 89], "AND": [18, 33], "ANDing": 84, "AS": 18, "And": [22, 32, 58, 62, 66, 68, 69, 71, 74, 84, 85, 89], "As": [24, 29, 35, 38, 39, 44, 51, 52, 56, 57, 58, 64, 65, 67, 68, 84, 85, 86, 88, 89], "At": [26, 31, 63, 64, 65], "BE": 18, "BUT": 18, "BY": 18, "Be": [22, 23, 24, 28, 44, 65], "Being": [63, 84], "But": [26, 39, 43, 51, 58, 62, 64, 65, 68, 69, 71, 72, 74, 85, 87, 88], "By": [23, 25, 29, 35, 39, 43, 44, 51, 53, 54, 56, 57, 61, 62, 64, 85, 86], "FOR": 18, "For": [1, 15, 17, 18, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 56, 57, 60, 61, 62, 63, 64, 65, 67, 68, 71, 84, 85, 86, 88], "IF": 18, "IN": [18, 27], "If": [5, 9, 11, 12, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 71, 72, 73, 74, 84, 85, 87, 88, 89], "In": [17, 18, 22, 23, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 43, 44, 51, 54, 57, 58, 59, 61, 62, 63, 64, 65, 68, 70, 72, 84, 85, 86, 87, 88, 89], "It": [2, 9, 12, 15, 17, 18, 19, 20, 23, 25, 26, 28, 29, 31, 32, 33, 36, 37, 38, 40, 42, 43, 44, 45, 47, 50, 51, 53, 54, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 68, 69, 72, 73, 84, 86, 87, 88, 89], "Its": 34, "NO": 18, "NOT": [18, 37, 40, 56], "No": [1, 5, 6, 8, 11, 33, 44, 45, 50, 51, 67, 70], "Not": [23, 25, 41, 45, 51, 60, 63, 64, 65, 69, 70], "OF": 18, "ON": 18, "OR": [18, 29, 35], "ORed": 38, "ORing": 33, "Of": [57, 69, 70, 71], "On": [1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 23, 25, 26, 37, 40, 44, 46, 50, 51, 56, 63, 66, 84], "One": [10, 19, 32, 63, 65, 84], "Or": [20, 26, 44, 64, 74], "SUCH": 18, "THE": 18, "TO": 18, "That": [15, 29, 56, 58, 62, 63, 64, 67, 68, 69, 70, 71, 73, 74, 84], "The": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 63, 65, 66, 68, 71, 84, 87, 88], "Then": [10, 22, 24, 58, 62, 64, 65, 68, 71, 73, 85, 86, 88, 89], "There": [16, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 30, 32, 33, 37, 39, 45, 46, 50, 51, 57, 58, 59, 60, 62, 64, 65, 69, 70, 72, 73, 85], "These": [16, 20, 22, 23, 24, 26, 29, 32, 37, 39, 44, 45, 47, 48, 50, 51, 52, 53, 56, 58, 59, 62, 64, 65, 84, 85, 87], "To": [22, 23, 25, 30, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 47, 50, 54, 57, 58, 65, 87, 88], "Will": [4, 5, 6, 11, 32, 38, 54], "With": [26, 31, 36, 37, 51, 62, 63, 65, 85, 87, 88], "_": [20, 33], "__class__": 45, "__copy__": 35, "__dict__": 25, "__file__": [26, 58, 66], "__init__": [32, 45, 50, 57, 58, 62, 64, 66, 86, 87, 88, 89], "__main__": [32, 66, 71, 72, 73, 79, 80, 81, 85, 89], "__name__": [32, 45, 66, 71, 72, 73, 79, 80, 81, 85, 89], "__new__": [29, 45], "__repr__": 45, "__tags__": 53, "_camera": 18, "_default_lay": 50, "_freetyp": [0, 10], "_index": 1, "_layer": 50, "_pixels_address": 51, "_pygam": 10, "_rect": 45, "_sdl2": 48, "_spin": [58, 66], "_sprite__g": 64, "_spritegroup": 64, "_tag": 53, "_test": 53, "_time_threshold": 50, "_use_upd": 50, "_walk": [58, 66], "a0": 37, "a6f89747b551": 44, "a_mask": 35, "aa": [20, 30, 65], "aaa": 65, "aacircl": 30, "aaellips": 30, "aalib": 23, "aalin": 24, "aapolygon": [24, 30], "aatrigon": 30, "abandon": 18, "abil": [44, 58, 63], "abl": [31, 32, 42, 58, 63, 84, 89], "abnorm": 89, "abort": 37, "about": [9, 15, 18, 19, 23, 25, 26, 28, 32, 36, 37, 39, 42, 44, 48, 50, 51, 55, 58, 59, 61, 62, 63, 64, 65, 67, 68, 70, 71, 72, 73, 74, 84, 85, 87, 88], "abov": [14, 18, 19, 22, 23, 24, 28, 29, 32, 40, 41, 42, 43, 45, 48, 57, 58, 62, 64, 65, 74, 84, 87], "absent": 23, "absolut": [19, 20, 29, 32, 37, 40, 51, 84], "abspath": [58, 66], "abstractgroup": 50, "acceler": [23, 24, 30, 41, 48, 51, 56, 63, 69], "accept": [14, 24, 26, 28, 29, 30, 31, 38, 39, 45, 50, 51, 52, 62], "access": [8, 9, 15, 17, 19, 24, 25, 26, 28, 29, 32, 35, 37, 38, 39, 41, 43, 44, 46, 50, 51, 59, 64, 65, 67, 84], "accident": 84, "accommod": 62, "accord": [20, 43, 56, 69], "accordingli": 51, "account": [32, 33, 36, 38, 40, 42, 56, 68], "accur": [39, 40, 54], "achiev": [62, 86], "acolor": 20, "acquir": [9, 51, 52], "across": [22, 23, 33, 58, 62, 65, 66, 84, 87, 88, 89], "act": [36, 58, 63, 70], "action": [25, 63, 65, 88], "activ": [23, 25, 38, 39, 40, 49, 51, 52, 55, 59, 72, 74], "activeev": [23, 25], "actual": [4, 11, 18, 19, 22, 23, 25, 28, 30, 32, 37, 38, 40, 41, 43, 45, 50, 51, 54, 56, 58, 59, 62, 63, 64, 65, 73, 84, 85, 88], "ad": [10, 23, 24, 25, 29, 31, 32, 33, 35, 38, 39, 40, 42, 43, 44, 45, 47, 50, 54, 56, 58, 64, 65, 68, 69, 70, 72, 73, 84, 87, 89], "add": [23, 29, 48, 50, 57, 58, 61, 62, 63, 64, 65, 68, 69, 70, 89], "add_intern": 64, "add_map": 47, "addit": [22, 23, 24, 25, 26, 32, 33, 36, 37, 44, 46, 48, 50, 51, 84], "addition": [23, 24, 47, 62], "address": [17, 51, 84, 86], "adequ": 72, "adjac": [24, 30], "adjust": [8, 20, 23, 28, 29, 37, 71, 89], "admit": 84, "adopt": [48, 61], "advanc": [15, 28, 29, 47, 50, 51, 58, 62, 63, 67, 70], "advancedinputoutput1": 80, "advancedinputoutput2": 80, "advancedinputoutput3": 80, "advancedinputoutput4": 80, "advancedinputoutput5": 80, "advancedoutputalpha1": 81, "advancedoutputalpha2": 81, "advancedoutputalpha3": 81, "advancedoutputprocess1": 79, "advancedoutputprocess2": 79, "advancedoutputprocess3": 79, "advancedoutputprocess4": 79, "advancedoutputprocess5": 79, "advancedoutputprocess6": 79, "advancemam": 56, "advantag": [59, 63, 64, 67, 84, 87], "advic": 63, "advis": [18, 22, 23], "ae": 28, "affect": [11, 23, 24, 29, 38, 42, 44, 45, 50, 51, 52, 65, 74], "afraid": 65, "after": [17, 19, 23, 24, 28, 29, 31, 32, 33, 37, 38, 40, 47, 49, 50, 51, 57, 58, 59, 62, 63, 65, 68, 69, 70, 84, 89], "again": [18, 33, 40, 44, 50, 51, 54, 59, 61, 62, 64, 65, 84, 85, 88, 89], "against": [29, 31, 44, 45, 56, 57, 64], "ago": 63, "agp": 65, "ahead": [24, 32], "ai": [86, 89], "aid": 29, "aim": 61, "alexei": 74, "algorithm": [24, 35, 44, 56, 67, 70, 84], "alia": [25, 31, 46, 50], "alias": [15, 23, 28, 29, 85], "aliceblu": 21, "alien": [26, 64], "alien1": 62, "align": [29, 35, 36, 45], "aliv": [17, 50, 64], "all": [15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 44, 45, 47, 48, 50, 51, 52, 53, 56, 57, 59, 60, 61, 63, 64, 65, 67, 68, 84, 85, 86, 87, 88], "all_my_sprites_list": 84, "allblack": 65, "alloc": [9, 31, 38, 68], "allot": 53, "allow": [15, 18, 20, 22, 23, 25, 27, 28, 29, 31, 32, 36, 38, 42, 44, 45, 46, 50, 51, 56, 57, 58, 59, 62, 64, 65, 87, 89], "allowedchang": 38, "allsprit": [58, 66], "almost": [15, 58, 61, 63, 64, 65, 67, 86], "alon": [26, 84], "along": [24, 25, 26, 29, 37, 45, 53, 62, 63, 64, 65, 66, 74, 87], "alpha": [1, 20, 23, 24, 26, 28, 29, 30, 31, 35, 43, 44, 48, 51, 52, 56, 63, 65, 86], "alreadi": [9, 19, 23, 25, 29, 32, 37, 38, 40, 45, 47, 50, 57, 62, 63, 64, 65, 84, 88], "alsa": 37, "also": [10, 12, 14, 15, 17, 19, 20, 22, 23, 25, 26, 28, 29, 30, 31, 33, 35, 36, 37, 38, 39, 40, 42, 44, 45, 47, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 72, 73, 84, 85, 86, 87, 88, 89], "alt": [25, 33], "alter": [35, 39, 56], "altern": [19, 23, 24, 26, 28, 29, 30, 45], "altgr": 33, "although": [58, 68, 86], "alwai": [18, 23, 25, 26, 28, 29, 30, 31, 32, 33, 35, 39, 41, 44, 45, 47, 49, 50, 51, 54, 56, 58, 59, 60, 62, 63, 64, 65, 68, 69, 70, 71, 77, 84, 86, 87, 89], "always\ubb38": [76, 77], "always\ubb38\uacfc": 79, "always\ubb38\uc5d0": [76, 77], "always\ubb38\uc5d0\uc11c": 76, "always\ubb38\uc758": 77, "always\ubb38\uc774": 77, "ambigu": 38, "among": 18, "amongst": 15, "amount": [23, 25, 28, 29, 32, 39, 54, 55, 56, 58, 63], "ampersand": 33, "amplitud": 49, "an": [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 63, 64, 65, 67, 69, 84, 85, 86, 87, 88, 89], "an_id": 37, "analog": [32, 42, 47], "andal": 29, "andmask": [22, 39], "android": [25, 33], "angl": [24, 29, 30, 35, 36, 48, 56, 57, 87, 89], "angle_to": 36, "angular": 87, "ani": [1, 9, 15, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 37, 38, 40, 41, 42, 43, 44, 45, 47, 48, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 74, 84, 85, 86, 87, 88], "anim": [15, 26, 31, 50, 56, 62, 63, 64, 67], "anisotrop": 44, "annoi": [15, 61, 73], "anoth": [10, 20, 23, 24, 26, 28, 29, 30, 32, 33, 35, 36, 39, 40, 42, 43, 45, 46, 47, 48, 50, 51, 62, 63, 64, 65, 73, 74, 84, 86, 89], "ansi": 37, "ansi_not": 37, "answer": [63, 84], "anti": [15, 23, 29, 85], "antialia": 28, "antialias": [24, 28, 29, 30, 56, 58], "anticip": [44, 69], "antiquewhit": 21, "antiquewhite1": 21, "antiquewhite2": 21, "antiquewhite3": 21, "antiquewhite4": 21, "anymor": 84, "anyon": 64, "anyth": [15, 19, 23, 27, 44, 50, 51, 58, 62, 63, 64, 65, 74, 84], "anywai": [65, 70, 85], "anywher": [12, 50, 62], "apart": 38, "api": [15, 18, 23, 25, 30, 33, 37, 46, 47, 48, 57, 84], "app": [23, 26, 44, 57], "app_didenterbackground": 25, "app_didenterforeground": 25, "app_lowmemori": 25, "app_termin": 25, "app_willenterbackground": 25, "app_willenterforeground": 25, "appear": [15, 23, 25, 54, 62, 63, 64, 70, 84], "append": [27, 57, 62, 64, 69, 73, 81, 84], "appli": [20, 24, 26, 29, 35, 36, 37, 51, 55, 56, 65, 84], "applic": [0, 17, 23, 25, 26, 27, 33, 34, 37, 38, 46, 51, 59, 84, 88], "approach": 61, "appropri": [23, 25, 39, 58, 59, 64, 69, 88], "approxim": [32, 35, 51, 57], "aptitud": 74, "aqua": 21, "aquamarin": 21, "aquamarine1": 21, "aquamarine2": 21, "aquamarine3": 21, "aquamarine4": 21, "ar": [1, 8, 9, 10, 12, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 67, 68, 70, 71, 72, 73, 74, 85, 86, 87, 88, 89], "arang": 65, "arbitrari": [50, 56, 62], "arbitrarili": 23, "arc": [24, 30], "arcad": [63, 84], "architectur": 56, "archiv": [28, 29, 84], "area": [11, 23, 24, 26, 28, 29, 32, 35, 44, 45, 48, 50, 51, 56, 57, 58, 62, 63, 64, 66, 68, 72, 84, 85, 87, 88, 89], "aren": [58, 62, 84], "arg": [26, 50, 53], "argb": 31, "argb_premult": 31, "argument": [1, 2, 3, 9, 11, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 38, 39, 40, 41, 43, 44, 45, 47, 48, 50, 51, 53, 54, 56, 58, 59, 62, 64, 84, 85], "aris": 18, "arithmet": [20, 36, 42, 65], "arkanoid": 69, "arm": 84, "around": [15, 23, 24, 26, 32, 36, 39, 42, 45, 48, 54, 57, 58, 59, 62, 63, 65, 84, 85, 86, 89], "arrai": [1, 2, 3, 15, 20, 22, 26, 29, 31, 33, 38, 42, 49, 51, 62, 63, 65, 72, 73, 80], "arrang": [65, 68], "array2d": [52, 65], "array3d": [52, 65], "array_alpha": [52, 65], "array_blu": 52, "array_colorkei": [43, 52, 65], "array_green": 52, "array_r": 52, "array_to_surfac": 43, "arraydemo": [15, 26, 65], "arraytyp": [26, 49, 52], "arrayxd": 65, "arriv": 84, "arrow": [22, 26, 33, 62, 70], "art": [67, 74], "articl": [62, 63], "artifact": 56, "as_joystick": 47, "as_polar": 36, "as_spher": 36, "ascend": 29, "ascent": [28, 29], "ascii": [14, 22, 33, 52, 67], "asid": 67, "ask": [23, 28, 29, 57, 58, 59, 62, 63, 84], "aspect": [44, 45], "assembl": 33, "assembli": 63, "assert": 62, "assertequ": 56, "assertnotequ": 56, "asset": 84, "assign": [20, 25, 29, 32, 42, 45, 47, 50, 53, 58, 62, 65, 88], "assist": [51, 84], "associ": [7, 29, 48, 55], "assum": [1, 9, 11, 20, 28, 29, 37, 39, 45, 51, 57, 61, 64, 86, 88], "asterisk": 33, "asteroid": 84, "astyp": 65, "asurf": 31, "asymmetr": 36, "asyncblit": 51, "asynchron": 51, "attach": [9, 23, 26, 47, 57], "attack": [36, 63], "attempt": [18, 28, 29, 37, 51, 58, 60, 65], "attent": [63, 70], "attract": 70, "attribut": [5, 23, 25, 27, 28, 29, 30, 32, 33, 34, 35, 36, 39, 44, 45, 50, 51, 53, 54, 58, 64, 85, 87, 88, 89], "attributeerror": [29, 64], "audio": [25, 26, 37, 38, 44, 46, 49, 63], "audio_allow_any_chang": 38, "audio_allow_channels_chang": 38, "audio_allow_format_chang": 38, "audio_allow_frequency_chang": 38, "audiodevic": 25, "audiodevicead": 25, "audiodeviceremov": 25, "august": 16, "author": [18, 28, 33, 36, 45, 51, 56, 57, 58, 59, 60, 62, 63, 64, 65, 84], "automat": [12, 19, 23, 28, 29, 30, 31, 32, 33, 34, 37, 38, 44, 46, 50, 51, 58, 60, 65, 68, 69], "avail": [0, 9, 16, 18, 20, 22, 23, 26, 27, 28, 29, 30, 31, 36, 37, 38, 41, 44, 45, 46, 49, 51, 52, 53, 55, 56, 58, 59, 60, 62, 63, 64, 65, 84], "avalanch": 74, "averag": [28, 29, 53, 54, 56, 57, 65, 84], "average_color": [56, 57], "average_surfac": [56, 57], "avid": 84, "avoid": [19, 23, 28, 33, 36, 38, 42, 53, 65, 84, 89], "awai": [15, 23, 25, 36, 64, 71, 89], "awar": [23, 25, 28, 44, 65, 84], "awhil": 19, "awkward": [62, 84], "ax": [23, 29, 32, 47, 87], "axi": [24, 25, 29, 36, 39, 42, 47, 48, 49, 52, 56, 85, 87], "axis_numb": 32, "azimuth": 36, "azur": 21, "azure1": 21, "azure2": 21, "azure3": 21, "azure4": 21, "b": [20, 24, 26, 32, 33, 36, 42, 43, 45, 46, 47, 51, 56, 65], "b0": 47, "b3": 47, "b_black": [73, 81], "b_height": [73, 81], "b_red": [73, 81], "b_width": [73, 81], "bach": 40, "back": [19, 28, 29, 32, 33, 37, 38, 50, 57, 58, 59, 66, 68, 88, 89], "backend": [18, 23, 25, 43, 44, 56, 59], "background": [24, 25, 26, 28, 29, 38, 44, 50, 57, 61, 64, 66, 84, 85, 87, 89], "backslash": 33, "backslashreplac": 44, "backspac": 33, "backward": [23, 25, 29, 38, 54], "backyard": 63, "bad": 84, "bagic": [76, 77, 78], "baker": 63, "ball": [25, 32, 61, 63, 69, 75, 76, 77, 78, 79, 80, 81, 85, 86, 88], "ball_numb": 32, "ballrect": [63, 75, 76, 77, 78, 79, 80, 81], "ballsprit": 89, "banner": [15, 26, 58, 66], "bar": [71, 79], "barrier": 67, "base": [0, 10, 17, 18, 24, 25, 26, 28, 29, 35, 36, 48, 50, 57, 58, 62, 64, 65, 66, 67, 68, 84, 87, 89], "baselin": [28, 29], "basic": [15, 18, 20, 23, 26, 31, 46, 58, 61, 62, 63, 64, 65, 76, 84, 86, 87, 88, 89], "bat": 61, "battleship": [67, 75], "bayer": 18, "bb": 20, "bdf": 29, "beam": 22, "bear": [28, 29, 39], "beauti": 62, "becam": 25, "becaus": [24, 27, 29, 33, 36, 37, 45, 51, 58, 61, 64, 65, 67, 68, 69, 70, 72, 73, 84, 85, 86, 87, 88, 89], "becom": [23, 25, 29, 38, 40, 49, 51, 52, 62, 63, 64, 65, 68, 73, 84], "been": [19, 20, 22, 23, 24, 25, 29, 32, 36, 38, 39, 40, 44, 46, 47, 50, 51, 59, 60, 62, 63, 64, 84, 88, 89], "befor": [9, 17, 18, 19, 22, 23, 27, 28, 29, 32, 33, 35, 37, 38, 39, 40, 44, 46, 47, 50, 51, 53, 54, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 70, 71, 84, 87, 88], "beforehand": 25, "begin": [15, 17, 18, 26, 38, 40, 45, 56, 62, 63, 65, 69, 87], "beginn": 65, "behalf": 17, "behavior": [29, 31, 36, 39, 50], "behaviour": [23, 39, 44, 46, 89], "behind": [15, 23, 31, 50, 87, 88, 89], "beig": 21, "being": [8, 18, 23, 24, 25, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 46, 62, 63, 64, 69, 84, 88], "believ": 84, "belong": [50, 58, 64, 85], "below": [15, 18, 20, 23, 24, 26, 27, 32, 37, 44, 45, 47, 50, 57, 62, 71], "bend": 37, "benefit": [50, 62, 64], "besid": [19, 26, 65], "best": [12, 18, 23, 25, 26, 35, 38, 42, 43, 51, 52, 59, 62, 63, 64, 65, 72, 86], "bet": 59, "better": [23, 28, 30, 37, 39, 40, 51, 56, 62, 63, 64, 65, 71, 84, 85], "between": [1, 3, 20, 23, 24, 25, 28, 29, 32, 33, 35, 36, 37, 38, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 57, 64, 65, 67, 70, 84], "beyond": 84, "bezier": 30, "bg": 57, "bgcolor": 29, "bgd": 50, "bgr": [31, 51, 65], "bgra": 31, "bgsurf": 50, "bid": 58, "big": [17, 18, 44, 50, 57, 62, 64, 65, 70, 71, 72], "bigger": [15, 23, 58, 73], "biggest": [23, 59], "bilinear": 56, "bin": [66, 85, 86, 87], "binari": [15, 18, 20, 22, 41], "bind": [37, 59], "bisqu": 21, "bisque1": 21, "bisque2": 21, "bisque3": 21, "bisque4": 21, "bit": [1, 14, 15, 17, 18, 22, 23, 25, 26, 28, 29, 30, 31, 32, 35, 38, 40, 42, 43, 49, 50, 51, 52, 56, 57, 58, 59, 61, 62, 64, 65, 84, 87, 89], "bitblt": 62, "bitmap": [22, 29, 31, 56, 62], "bitmap_1": 22, "bitmap_2": 22, "bitmask": [22, 33, 35, 50, 51], "bitsiz": [23, 35, 59], "bitstream": [28, 29], "bitstreamverasan": 28, "bitwis": [23, 33, 35], "bl": 89, "bla": 31, "black": [21, 22, 23, 24, 26, 29, 35, 42, 51, 56, 57, 58, 63, 65, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84], "blade": 63, "blanchedalmond": 21, "blank": [28, 57, 62, 84, 85], "blanket": [43, 51], "blend": [20, 24, 26, 29, 44, 48, 51], "blend_add": 51, "blend_alpha_sdl2": 51, "blend_fil": 26, "blend_max": 51, "blend_min": 51, "blend_mod": 48, "blend_mult": 51, "blend_premultipl": 51, "blend_premultipli": [20, 51], "blend_rgb_add": 51, "blend_rgb_max": 51, "blend_rgb_min": 51, "blend_rgb_mult": 51, "blend_rgb_sub": 51, "blend_rgba_add": 51, "blend_rgba_max": 51, "blend_rgba_min": 51, "blend_rgba_mult": 51, "blend_rgba_sub": 51, "blend_sub": 51, "blend_xxx": 26, "blendmod": 50, "blink": 67, "blit": [11, 20, 23, 26, 28, 29, 30, 32, 43, 48, 50, 51, 52, 56, 57, 58, 61, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 87, 88, 89], "blit_arrai": [43, 52, 65], "blit_blend": 26, "blit_hw": [23, 59], "blit_hw_a": [23, 59], "blit_hw_cc": [23, 59], "blit_sequ": 51, "blit_sw": [23, 59], "blit_sw_a": [23, 59], "blit_sw_cc": [23, 59], "blitter": [44, 51, 62], "blitzbas": 26, "blit\uc774": 76, "blit\ud568\uc218\ub294": 76, "blob": [56, 57], "block": [17, 18, 25, 35, 40, 50, 51, 57, 58, 63, 73, 84], "block_list": 50, "blocks_hit_list": 50, "bloodi": 63, "blt": 62, "blue": [1, 18, 20, 21, 23, 24, 28, 31, 42, 51, 52, 56, 57, 65, 68, 71, 72, 73, 79, 80, 81, 85, 87], "blue1": 21, "blue2": 21, "blue3": 21, "blue4": 21, "blueviolet": 21, "bluish": 65, "bmp": [31, 46, 62, 63], "board": [73, 81], "bodi": 70, "bold": [28, 29], "bomb": 64, "bonu": 62, "book": 84, "bool": [17, 18, 19, 23, 24, 25, 27, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 44, 45, 46, 47, 48, 50, 51, 56], "boolean": [25, 28, 32, 33, 39, 56, 62, 64], "boom": 64, "boom_sound": 64, "border": [23, 24, 30, 39, 45, 48], "border_bottom_left_radiu": 24, "border_bottom_right_radiu": 24, "border_radiu": 24, "border_top_left_radiu": 24, "border_top_right_radiu": 24, "borderless": 48, "bore": [67, 86], "borrow": [1, 63, 86], "both": [19, 23, 24, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 42, 50, 51, 56, 57, 62, 63, 64, 65, 67, 71, 74, 84, 87, 89], "bother": [58, 61, 86], "bottleneck": 84, "bottom": [23, 24, 26, 28, 29, 30, 31, 35, 45, 50, 63, 75, 76, 77, 78, 79, 80, 81, 89], "bottomleft": [45, 89], "bottomright": [45, 89], "bounc": [26, 36, 56, 63, 89], "bound": [22, 24, 29, 35, 50, 51], "boundari": [24, 29], "box": [24, 29, 30, 33, 57, 64, 88], "br": 89, "bracket": [33, 87], "break": [18, 30, 33, 46, 54, 62, 88], "breakag": 42, "breakdown": 63, "brief": [19, 26, 44, 61, 62], "briefli": 85, "bright": [18, 57], "brighten": 23, "brightmap": 65, "bring": [50, 63, 84], "broadcast": [42, 65], "broader": 84, "broken": [39, 44], "broken_x": 22, "brought": 56, "brown": 21, "brown1": 21, "brown2": 21, "brown3": 21, "brown4": 21, "bu": 65, "buffer": [1, 2, 18, 20, 23, 31, 37, 38, 43, 46, 51, 58, 63, 64], "buffer_s": 37, "bufferproxi": [0, 15, 17, 31, 51], "buffers": [37, 38], "bug": [20, 44, 56, 63, 84, 89], "build": [44, 64, 65, 85, 89], "built": [14, 18, 28, 29, 31, 44, 47, 84, 85], "builtin": [18, 28, 64], "bullet": [64, 84], "bump": 84, "bumper": 32, "bunch": [57, 64, 84], "bundl": [28, 29], "burlywood": 21, "burlywood1": 21, "burlywood2": 21, "burlywood3": 21, "burlywood4": 21, "busi": [18, 19, 38, 54, 62], "button": [24, 25, 26, 27, 32, 33, 39, 47, 58, 61, 62, 69, 73, 80, 84, 85, 88], "button1": 39, "button2": 39, "button3": 39, "button4": 39, "button5": 39, "bx": 47, "bye": 68, "bypass": 44, "byte": [9, 14, 17, 18, 20, 22, 23, 28, 29, 31, 37, 38, 42, 43, 44, 46, 51], "byte_data": 46, "bytearrai": 31, "bytecod": 63, "byteord": 17, "bytes": [17, 23, 35, 51, 59], "bytestr": 38, "b\u00e9zier": 30, "b\uac12": 76, "c": [10, 15, 17, 18, 20, 26, 28, 30, 33, 43, 44, 45, 51, 60, 63, 65, 67, 75], "c_api": 0, "cach": 29, "cache_s": 29, "cadetblu": 21, "cadetblue1": 21, "cadetblue2": 21, "cadetblue3": 21, "cadetblue4": 21, "cadillac": 64, "calcnewpo": [87, 89], "calcul": [24, 29, 35, 36, 40, 42, 50, 68, 69, 72, 73, 87, 88], "calibr": 57, "call": [1, 9, 12, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 50, 51, 53, 54, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 84, 85, 86, 87, 88, 89], "callabl": [17, 44, 50], "callback": [1, 2, 17, 50], "caller": [9, 37], "calling_mask": 35, "cam": 57, "came": [26, 31], "camera": [15, 26, 44, 84], "camlist": 57, "can": [1, 8, 9, 13, 14, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 84, 85, 86, 87, 88, 89], "candid": [12, 33], "cannot": [14, 18, 23, 25, 28, 30, 38, 40, 45, 48, 50, 51, 52, 54, 62, 63, 64, 69, 84, 86, 89], "canva": [68, 71], "cap": 33, "capabl": [23, 59, 63, 86, 87], "capslock": 33, "caption": [23, 68], "captur": [15, 18, 25, 26, 31, 32], "capword": 25, "card": [23, 38, 65], "care": [9, 27, 38, 58, 65, 72, 85], "caret": 33, "carri": 89, "case": [15, 18, 20, 23, 24, 28, 29, 31, 33, 36, 37, 38, 40, 43, 44, 51, 57, 58, 59, 62, 64, 68, 70, 72, 73, 74, 84, 85, 87, 88], "castl": 63, "catch": [44, 64, 89], "categor": 64, "categori": 84, "caught": 63, "caus": [2, 18, 20, 25, 28, 35, 40, 42, 44, 56, 58, 64, 65, 66, 68, 84, 88], "caveat": 23, "cc": 57, "ccolor": 57, "cd": 19, "cdrom": 63, "cdrom_tag": 53, "cdrom_test": 53, "ceil": 63, "center": [24, 26, 30, 32, 35, 44, 45, 48, 50, 51, 57, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85], "centeri": 45, "centerx": [45, 58, 66, 85], "center\ub77c\ub294": 76, "centr": 44, "centroid": [35, 57], "certain": [20, 25, 33, 36, 57, 58, 61, 67, 68, 69, 70, 71, 72, 84], "certainli": [62, 65], "cff": 29, "challeng": [63, 84], "chanc": [38, 65, 84], "chang": [15, 18, 20, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 56, 57, 58, 59, 61, 63, 64, 65, 68, 69, 70, 71, 73, 84, 87, 88, 89], "change_color": 56, "change_lay": 50, "channel": [7, 20, 31, 37, 38, 40, 51, 65, 84], "channelnum": 7, "char": [1, 6, 9, 28, 29, 51], "char_bit": 35, "charact": [14, 17, 23, 28, 29, 33, 43, 44, 52, 62, 84, 88], "character": 36, "characterist": [20, 74], "charset": 46, "chart": 65, "chartreus": 21, "chartreuse1": 21, "chartreuse2": 21, "chartreuse3": 21, "chartreuse4": 21, "chase": 64, "chaser": 64, "chatroom": 62, "cheap": [50, 64], "check": [3, 4, 5, 6, 7, 8, 11, 15, 18, 23, 25, 26, 28, 32, 35, 38, 39, 40, 42, 44, 46, 47, 49, 50, 52, 57, 58, 60, 62, 63, 64, 67, 68, 70, 71, 72, 84, 85, 86, 87, 88, 89], "checkout": 44, "chew": 54, "chief": 86, "child": 51, "chimp": [15, 26, 61, 64, 86], "chimpanze": 15, "chocol": 21, "chocolate1": 21, "chocolate2": 21, "chocolate3": 21, "chocolate4": 21, "choic": [18, 23, 38, 57], "choos": [18, 22, 23, 26, 59, 61, 65, 73, 84], "chop": 56, "choppi": 62, "chord": 84, "chore": 84, "chose": 73, "chosen": [18, 23, 29], "chromin": 18, "chunk": [7, 17, 89], "circl": [15, 22, 24, 26, 30, 32, 50, 57], "circular": 64, "circumst": 89, "claim": 18, "clamp": [26, 36, 45], "clamp_ip": 45, "clamp_magnitud": 36, "clamp_magnitude_ip": 36, "clariti": 35, "clark": 84, "class": [0, 15, 17, 19, 20, 26, 29, 31, 32, 35, 37, 38, 45, 51, 57, 61, 62, 66, 84, 86, 89], "classless": [86, 87], "classmethod": 48, "claus": 89, "clean": [39, 42, 58, 60, 62, 63, 64, 84], "cleaner": [62, 63], "cleanli": [60, 62, 63, 64, 89], "cleanup": 42, "clear": [15, 23, 24, 25, 32, 33, 35, 38, 43, 48, 50, 51, 62, 63, 64, 67, 75, 84], "clear_callback": 50, "clearer": 60, "clench": [58, 66], "click": [15, 22, 24, 26, 32, 33, 39, 69, 72, 84, 85], "client": 25, "clip": [24, 26, 45, 50, 51, 62], "clipboard": [15, 25, 26], "clipboardupd": 25, "cliplin": 45, "clipped_lin": 45, "clist": 57, "clock": [15, 22, 24, 32, 39, 54, 58, 62, 66, 69, 70, 71, 72, 73, 77, 78, 79, 80, 81, 84, 89], "clockwis": [29, 30, 36, 56, 87], "clone": 20, "close": [9, 15, 18, 22, 23, 24, 25, 26, 29, 32, 37, 40, 42, 47, 56, 57, 62, 65, 85, 86], "close_to_play": 64, "close_to_player2": 64, "close_to_player3": 64, "closest": [23, 37, 38, 59], "cmy": 20, "co": [87, 89], "cocoa": 23, "code": [5, 14, 15, 17, 18, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 35, 39, 42, 43, 44, 45, 46, 50, 51, 56, 57, 58, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 84, 85, 86, 87, 88, 89], "codec": 44, "codepoint": 28, "coder": 84, "coercion": 65, "col": 30, "collect": [12, 23, 33, 50, 51, 60, 84], "collid": [35, 45, 50, 58, 64, 66, 87, 89], "collide_circl": 50, "collide_circle_ratio": 50, "collide_mask": [35, 50], "collide_rect": 50, "collide_rect_ratio": 50, "collided_cal": 50, "collidedict": 45, "collidedictal": 45, "collidelist": 45, "collidelistal": 45, "collideobject": 45, "collideobjectsal": 45, "collidepoint": [45, 72, 73, 80, 81, 84, 89], "collidepoint\ub97c": 80, "colliderect": [45, 50, 58, 66, 89], "colliding_sprit": 50, "collis": [26, 35, 36, 45, 50, 57, 67, 89], "collision_box": 45, "colon": 33, "color": [0, 1, 10, 15, 22, 23, 24, 28, 29, 30, 31, 35, 42, 43, 48, 50, 51, 52, 56, 57, 58, 59, 62, 63, 65, 67, 68, 71, 72, 73, 80, 81, 84, 85], "color_valu": 20, "colordict": 21, "colorkei": [23, 26, 28, 29, 31, 43, 51, 52, 56, 58, 63, 65, 66], "colormap": [26, 43, 52], "colorspac": 18, "colour": [20, 44, 51], "column": [35, 42, 65, 73, 81], "com": 56, "combin": [23, 29, 33, 45, 50, 51, 56, 63, 64, 84, 89], "come": [25, 26, 28, 29, 37, 44, 57, 62, 63, 64, 65, 66, 84, 87, 88, 89], "comfort": [15, 87], "comma": [28, 29, 33, 37, 65], "command": [24, 25, 26, 32, 53, 65, 67, 68, 69, 70], "comment": [26, 61, 64, 66, 87, 89], "commerci": [15, 63], "commit": [33, 63], "common": [23, 28, 29, 47, 50, 56, 60, 61, 62, 63, 84], "commun": [25, 48, 84], "compani": 63, "compar": [33, 35, 36, 42, 44, 50, 56, 63, 68, 84], "comparison": [20, 25, 26, 42, 65, 70, 74], "compat": [23, 25, 29, 31, 38, 48, 50, 54, 67, 86], "compens": 63, "compil": [13, 15, 19, 22, 25, 28, 29, 31, 38, 41, 44], "complement": 36, "complet": [24, 33, 38, 40, 45, 50, 51, 52, 63, 64, 84, 89], "complex": [38, 58, 63, 64, 67, 69, 84, 86, 87, 88], "complic": [68, 84], "compon": [20, 35, 36, 43, 51, 65, 68], "composit": 33, "compositor": 44, "compound": 46, "compound_text": 46, "compress": [40, 51], "comput": [15, 18, 19, 26, 28, 31, 32, 38, 54, 58, 62, 63, 64, 69, 74, 84], "concept": [15, 61, 62, 63, 65, 69, 74, 84], "concern": [64, 74], "conclud": 63, "conclus": 74, "concret": 84, "concurr": 84, "condit": [18, 22, 38, 68, 89], "confid": 84, "configur": [15, 23, 29, 39, 59, 65], "confin": [48, 62], "confus": [24, 62, 68, 84], "connect": [24, 26, 30, 32, 35, 47, 61, 62, 74, 86], "connected_compon": [35, 57], "connenct": 32, "consequ": 86, "consequenti": 18, "consid": [23, 24, 25, 29, 35, 36, 38, 40, 44, 45, 50, 51, 56, 84, 89], "consider": 51, "consider_alpha": 56, "consist": [19, 24, 25, 27, 38, 46, 61, 65, 85, 86], "consol": [26, 44, 53, 67], "const": [1, 6, 9, 70], "constant": [15, 22, 25, 29, 33, 38, 39, 44, 46, 47, 54, 57, 60, 62, 68, 72], "constrain": [24, 39, 70], "construct": [36, 45, 62], "constructor": [29, 32, 50, 58, 64], "consum": [1, 54, 65, 84], "contact": [57, 58, 59, 60, 62, 63, 64, 65], "contain": [0, 6, 8, 15, 19, 22, 23, 24, 25, 28, 29, 31, 32, 33, 34, 35, 37, 38, 39, 43, 45, 46, 47, 50, 51, 53, 55, 58, 62, 63, 64, 66, 84, 86, 88, 89], "content": [23, 34, 41, 42, 46, 58, 59, 68, 70], "context": [9, 23, 42, 48, 84], "contigu": [17, 24, 51], "continu": [35, 38, 44, 50, 51, 63, 85], "contract": [18, 56], "contrast": [39, 70, 87], "contribut": 64, "contributor": 18, "control": [4, 15, 18, 22, 25, 26, 28, 29, 33, 34, 38, 41, 44, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 69, 70, 78, 84, 86], "controller_axis_lefti": 47, "controller_axis_leftx": 47, "controller_axis_righti": 47, "controller_axis_rightx": 47, "controller_axis_triggerleft": 47, "controller_axis_triggerright": 47, "controller_button_a": 47, "controller_button_b": 47, "controller_button_back": 47, "controller_button_dpad_down": 47, "controller_button_dpad_left": 47, "controller_button_dpad_right": 47, "controller_button_dpad_up": 47, "controller_button_guid": 47, "controller_button_i": 47, "controller_button_leftshould": 47, "controller_button_leftstick": 47, "controller_button_rightshould": 47, "controller_button_rightstick": 47, "controller_button_start": 47, "controller_button_x": 47, "controlleraxismot": 47, "controllerbuttondown": 47, "controllerbuttonup": 47, "controllerdevicead": [25, 47], "controllerdeviceremap": [25, 47], "controllerdeviceremov": [25, 47], "controllertouchpaddown": 47, "controllertouchpadmot": 47, "controllertouchpadup": 47, "convei": 22, "conveni": [36, 44, 50, 53, 62, 63, 84], "convent": 47, "convers": [1, 18, 20, 35, 42, 52, 84], "convert": [1, 18, 20, 22, 24, 29, 31, 37, 38, 40, 43, 45, 49, 51, 52, 58, 59, 62, 65, 66, 85, 86, 89], "convert_alpha": [26, 31, 51, 86, 89], "convolut": [35, 65], "convolv": 35, "cool": 84, "cooper": 19, "coord": 57, "coordin": [8, 22, 24, 29, 30, 32, 35, 36, 39, 50, 51, 56, 85, 89], "copi": [17, 22, 26, 31, 35, 36, 38, 42, 45, 46, 48, 49, 50, 51, 52, 56, 58, 62, 63, 64, 65, 84, 85], "copyright": 18, "coral": 21, "coral1": 21, "coral2": 21, "coral3": 21, "coral4": 21, "cord": 26, "core": 84, "coremidi": 37, "corner": [24, 29, 30, 35, 39, 42, 44, 50, 51, 56, 57, 58, 62, 84, 89], "cornflowerblu": 21, "cornsilk": 21, "cornsilk1": 21, "cornsilk2": 21, "cornsilk3": 21, "cornsilk4": 21, "correct": [18, 22, 28, 41, 45, 51, 56, 62, 65, 68, 73], "correct_gamma": 20, "correctli": [24, 36, 37, 38, 56, 57, 58, 62, 65], "correspond": [29, 33, 35, 37, 42, 51, 53, 68, 84, 89], "could": [22, 25, 33, 36, 41, 45, 48, 49, 50, 52, 56, 57, 58, 61, 62, 64, 65, 85, 86, 87], "couldn": [86, 89], "count": [17, 19, 32, 35, 38, 42, 56, 57, 69, 73], "counterclockwis": [24, 29, 36, 56], "coupl": [32, 58, 62, 64, 65], "courier": 29, "cours": [57, 69, 70, 71, 85, 86, 87, 89], "cover": [15, 24, 33, 44, 45, 51, 56, 57, 59, 62, 74, 84, 85], "coverag": 24, "cprofil": 84, "cpu": [24, 54, 67, 75, 84], "crash": [41, 64], "creat": [1, 2, 5, 6, 15, 16, 17, 19, 20, 21, 22, 23, 25, 26, 28, 29, 31, 32, 33, 35, 36, 38, 39, 41, 42, 43, 45, 47, 48, 49, 50, 51, 52, 54, 56, 57, 59, 63, 64, 65, 66, 74, 84, 85, 86, 87, 88, 89], "create_graphics_screen": 62, "create_screen": 62, "creation": [28, 29, 32, 47, 49, 50, 52, 62], "creativ": 74, "crect": 57, "crimson": 21, "critic": 84, "critter": [58, 66], "crop": [45, 56], "cross": [32, 36, 58, 63, 65, 86], "crossbar": 26, "crossbon": 22, "crossfad": 65, "crosshair": 22, "crt": 23, "crucial": 84, "crude": [26, 58, 62], "cryptic": 64, "ctrl": 25, "cube": 26, "cui": [67, 68, 73, 81], "cui\uac00": 75, "cui\uace0": 75, "cui\ud658\uacbd\uc5d0\uc11c\ub9cc": 76, "current": [13, 18, 19, 22, 23, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 58, 59, 62, 63, 64, 67, 68, 69, 71, 84, 87, 88, 89], "current_h": [23, 59], "current_level": 36, "current_w": [23, 59], "cursor": [15, 26, 39, 58, 63, 84], "cursor_arg": 22, "cursor_index": 22, "cursorfil": 22, "curv": 30, "custom": [15, 23, 25, 26, 38, 50, 63], "custom_typ": 25, "customis": 86, "cut": [38, 46, 84], "cutout": 62, "cx1": 45, "cx2": 45, "cy1": 45, "cy2": 45, "cyan": 21, "cyan1": 21, "cyan2": 21, "cyan3": 21, "cyan4": 21, "c\ub85c": 75, "d": [32, 33, 51, 62, 84, 85, 87], "da": 61, "dai": [63, 64, 84], "damag": 18, "dark": [58, 63], "darkblu": 21, "darkcyan": 21, "darken": 23, "darkgoldenrod": 21, "darkgoldenrod1": 21, "darkgoldenrod2": 21, "darkgoldenrod3": 21, "darkgoldenrod4": 21, "darkgrai": 21, "darkgreen": 21, "darkgrei": 21, "darkkhaki": 21, "darkmagenta": 21, "darkolivegreen": 21, "darkolivegreen1": 21, "darkolivegreen2": 21, "darkolivegreen3": 21, "darkolivegreen4": 21, "darkorang": 21, "darkorange1": 21, "darkorange2": 21, "darkorange3": 21, "darkorange4": 21, "darkorchid": 21, "darkorchid1": 21, "darkorchid2": 21, "darkorchid3": 21, "darkorchid4": 21, "darkr": 21, "darksalmon": 21, "darkseagreen": 21, "darkseagreen1": 21, "darkseagreen2": 21, "darkseagreen3": 21, "darkseagreen4": 21, "darkslateblu": 21, "darkslategrai": 21, "darkslategray1": 21, "darkslategray2": 21, "darkslategray3": 21, "darkslategray4": 21, "darkslategrei": 21, "darkturquois": 21, "darkviolet": 21, "data": [0, 2, 15, 17, 18, 19, 22, 26, 31, 35, 37, 38, 39, 40, 41, 43, 46, 51, 56, 58, 62, 63, 65, 66, 68, 70, 71, 72, 73, 84, 86, 89], "data1": 37, "data2": 37, "data3": 37, "data_dir": [58, 66], "datatyp": 65, "date": 84, "david": 84, "dead": 27, "deal": [33, 44, 65, 68, 84, 88], "dealloc": 32, "dealt": [25, 27, 88], "death": 63, "debat": 84, "debug": [25, 56, 84, 88], "decapit": 63, "decept": 72, "decid": [25, 27, 35, 58, 61, 64, 68, 84], "decim": 24, "decis": 84, "declar": [8, 62, 65], "decod": [22, 29, 33, 46, 63], "decor": 85, "decreas": [38, 45, 72], "decrement": 9, "dedic": 64, "deeper": 84, "deeppink": 21, "deeppink1": 21, "deeppink2": 21, "deeppink3": 21, "deeppink4": 21, "deepskyblu": 21, "deepskyblue1": 21, "deepskyblue2": 21, "deepskyblue3": 21, "deepskyblue4": 21, "def": [29, 32, 35, 39, 45, 50, 56, 57, 58, 62, 64, 66, 71, 72, 73, 79, 80, 81, 85, 86, 87, 88, 89], "default": [1, 14, 18, 20, 22, 23, 24, 25, 26, 28, 29, 31, 33, 35, 36, 37, 38, 40, 43, 44, 45, 48, 50, 51, 53, 54, 56, 58, 59, 62, 63], "default_id": 37, "default_lay": 50, "defin": [1, 2, 3, 6, 7, 8, 10, 11, 25, 28, 29, 34, 36, 44, 46, 50, 58, 61, 62, 84, 85, 88], "definit": [46, 60, 64, 65, 70], "deflat": 89, "degre": [29, 30, 35, 36, 52, 56, 58, 87, 89], "del": [25, 32, 65], "delai": [26, 33, 37, 54, 69, 84], "delet": [25, 33, 62, 64, 84], "deliv": 37, "delta": [15, 36], "demo": [26, 58, 65], "demonstr": [15, 26, 36, 58, 65], "denot": [40, 45, 54], "depend": [14, 23, 25, 28, 29, 31, 35, 38, 40, 42, 43, 46, 49, 58, 63, 66, 69, 71, 84], "deprec": [20, 23, 24, 25, 26, 27, 29, 32, 36, 49, 50, 51, 52], "deprecationwarn": [49, 52], "depth": [15, 18, 23, 35, 42, 48, 51, 52, 56, 57, 58, 59, 62], "deriv": [26, 44, 50, 58, 64, 84], "descend": 29, "descent": [28, 29], "describ": [1, 17, 22, 23, 37, 39, 45, 57, 59, 86], "descript": [1, 18, 22, 25, 29, 33, 34, 44, 46, 62, 64], "design": [17, 50, 61, 62, 63, 64, 68, 71, 88], "desir": [18, 51, 56, 58, 59, 84], "desktop": [23, 48, 59, 84], "desper": 62, "dest": [29, 35, 48, 51, 56, 65], "dest_rect": 56, "dest_siz": 56, "dest_surf": 56, "dest_surfac": 56, "destin": [18, 28, 29, 30, 35, 42, 50, 51, 56, 62, 63, 85], "destroi": [1, 11, 48, 64], "destruct": [56, 58], "destsurfac": 18, "detail": [15, 23, 24, 29, 32, 33, 34, 35, 36, 37, 41, 50, 51, 56, 57, 61, 71, 84], "detect": [23, 26, 35, 36, 42, 44, 45, 50, 57], "determin": [19, 20, 22, 23, 24, 25, 28, 29, 31, 33, 36, 37, 40, 41, 50, 51, 56, 59, 64, 65, 68, 69, 72, 87], "dev": [18, 57], "dev13": 25, "dev3": [25, 54], "dev7": 39, "dev8": 24, "deva": 28, "devanagari": 28, "develop": [23, 28, 33, 36, 39, 44, 45, 48, 51, 53, 56, 63, 67, 84, 86], "devic": [15, 18, 19, 25, 26, 32, 33, 37, 39, 44, 47, 55, 57, 59, 63, 84], "device_id": [26, 37], "device_index": [25, 32], "devicenam": 38, "dga": 23, "diagon": [35, 56], "diagram": [87, 88], "diamond": 22, "dict": [1, 5, 17, 20, 23, 25, 32, 35, 45, 47, 51, 53, 55], "dictionari": [1, 5, 23, 25, 45, 47, 50, 53, 64, 84], "did": [18, 24, 62, 63, 64], "didn": [84, 85], "diff": 65, "differ": [15, 18, 19, 20, 22, 23, 25, 26, 27, 28, 29, 30, 32, 33, 35, 38, 40, 42, 44, 48, 50, 51, 56, 57, 58, 59, 60, 62, 63, 64, 65, 68, 69, 70, 84, 85, 87, 88], "difficult": [37, 61, 63], "digit": [20, 32, 36], "dilemma": 67, "dimens": [17, 18, 23, 24, 26, 28, 29, 30, 35, 36, 42, 43, 45, 50, 51, 52, 56, 58, 65, 89], "dimension": [20, 24, 29, 30, 35, 36, 42, 62, 65], "dimgrai": 21, "dimgrei": 21, "direct": [18, 23, 24, 25, 26, 29, 30, 32, 36, 39, 43, 47, 51, 58, 62, 65, 70, 84, 87, 89], "directfb": 23, "directli": [14, 17, 22, 24, 25, 28, 29, 31, 33, 42, 43, 46, 49, 51, 52, 53, 62, 64, 65, 67, 84], "directmedia": 63, "directori": [23, 28, 29, 53, 58, 62, 67, 68, 86], "directx": [23, 37, 63], "dirti": [50, 64, 84], "dirty_rect": 84, "dirtysprit": 50, "disabl": [23, 25, 29, 33, 44, 47, 51, 54, 58, 66], "disable_advanced_featur": 44, "disadvantag": [59, 84], "disallow": 23, "disappear": [18, 30, 46], "disc": 19, "discard": 54, "disclaim": 18, "disconnect": [32, 61], "discontinu": [17, 51, 67], "discourag": 27, "discov": [63, 84], "discret": 71, "discuss": [64, 84], "disk": [50, 84], "displac": 69, "displai": [0, 1, 15, 22, 24, 25, 26, 30, 32, 33, 34, 36, 38, 39, 41, 44, 46, 48, 50, 51, 53, 57, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "display_index": [25, 48], "distanc": [36, 42, 56], "distance_squared_to": 36, "distance_to": 36, "distil": 84, "distort": 23, "distribut": [15, 18, 53, 58], "dive": 68, "divers": 61, "divid": [33, 61], "divis": [20, 22, 84], "dizzi": [58, 66], "do": [8, 15, 18, 19, 20, 22, 23, 24, 25, 26, 27, 29, 32, 33, 35, 41, 42, 44, 45, 50, 51, 56, 57, 58, 59, 60, 61, 63, 64, 65, 67, 68, 69, 74, 85, 86, 87, 88, 89], "doc": [23, 27, 48, 50, 51, 70, 78, 84], "document": [18, 34, 38, 44, 51, 57, 59, 61, 62, 63, 64, 65, 84, 86, 87], "dodgerblu": 21, "dodgerblue1": 21, "dodgerblue2": 21, "dodgerblue3": 21, "dodgerblue4": 21, "doe": [3, 4, 7, 12, 19, 22, 23, 24, 25, 26, 28, 29, 30, 32, 33, 35, 36, 37, 38, 40, 41, 43, 44, 45, 46, 47, 50, 51, 54, 56, 57, 58, 62, 64, 65, 68, 84, 86, 88], "doesn": [18, 23, 26, 28, 29, 44, 51, 58, 62, 64, 72, 73, 84, 86, 87, 88, 89], "dokil": [50, 64], "dokill1": [50, 64], "dokill2": [50, 64], "dollar": 33, "domain": 26, "don": [9, 18, 24, 26, 29, 32, 36, 50, 54, 56, 57, 58, 61, 62, 63, 64, 65, 68, 70, 74, 85, 86, 87, 89], "done": [24, 28, 29, 32, 40, 58, 62, 63, 65, 68, 70, 71, 84, 87], "doreturn": 51, "dot": [29, 36], "doubl": [9, 22, 23, 56, 58, 63, 65], "doublebuf": [23, 84], "doubler": 56, "down": [23, 29, 32, 33, 39, 44, 47, 51, 57, 59, 62, 63, 65, 68, 69, 71, 72, 84, 88], "download": [63, 89], "down\ub41c": 80, "dpad": 47, "dpi": 29, "drastic": 85, "draw": [15, 20, 23, 28, 29, 31, 32, 35, 48, 50, 51, 57, 62, 63, 64, 66, 67, 68, 71, 72, 73, 79, 80, 81, 84, 85, 89], "draw_blend_mod": 48, "draw_bottom_left": 24, "draw_bottom_right": 24, "draw_circle_part": 24, "draw_color": 48, "draw_lin": 48, "draw_point": 48, "draw_rect": [45, 48], "draw_top_left": 24, "draw_top_right": 24, "drawboard": [73, 81], "drawbutton": [72, 73, 80, 81], "drawbuttons\uc5d0": 80, "drawhp": [71, 72, 73, 79, 80, 81], "drawhp\ub77c\ub294": 79, "drawn": [23, 24, 30, 32, 35, 50, 51, 58, 62, 63, 64, 68, 84, 85], "dream": 67, "drew": 62, "drift": 32, "drive": 19, "driven": [23, 50, 67], "driver": [23, 37, 44, 59], "drivernam": 44, "drop": [25, 29, 38], "dropbegin": 25, "dropcomplet": 25, "dropfil": 25, "dropout": 38, "droptext": 25, "dstobj": 11, "dstrect": [11, 48], "dt": 15, "dualshock": 47, "due": [20, 23, 25, 36, 51, 84], "dull": 84, "dummi": [58, 64, 88], "dump": 53, "dungeon": 63, "duplic": [50, 51], "durat": [32, 47], "dure": [18, 23, 29, 30, 33, 40, 42, 52, 65], "dvd": [19, 69, 77], "dx": [25, 35, 51, 87, 89], "dy": [25, 35, 51, 87, 89], "dynam": [9, 69, 84], "e": [5, 20, 23, 24, 28, 30, 31, 32, 33, 35, 36, 37, 38, 44, 45, 50, 51, 57, 61, 84, 85, 87, 88], "each": [17, 18, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 32, 35, 36, 38, 39, 42, 46, 49, 50, 51, 52, 53, 56, 57, 58, 60, 61, 62, 63, 64, 65, 71, 84, 85, 87, 88, 89], "earli": [15, 48, 63, 84], "earlier": [26, 40, 43, 62], "easi": [15, 26, 48, 59, 60, 61, 62, 63, 64, 65, 70, 84, 88, 89], "easier": [45, 50, 51, 59, 60, 62, 87], "easiest": [26, 47, 62, 71, 85], "easili": [22, 44, 57, 58, 60, 62, 64, 65, 69, 74, 84, 87], "east": 22, "eat": 64, "echo": 26, "eclass": 9, "eclecti": 57, "ed": [51, 64], "edg": [24, 28, 39, 45, 56, 89], "edit": [33, 47], "editbox": 84, "editor": [33, 84], "effect": [15, 23, 25, 26, 28, 32, 40, 44, 46, 47, 50, 51, 56, 57, 58, 62, 63, 64, 65, 73, 84, 86], "effici": [15, 23, 25, 28, 50, 62, 64, 84], "effort": 74, "eg": [26, 29, 31], "eight": 26, "either": [14, 17, 19, 22, 23, 29, 31, 32, 36, 38, 39, 45, 50, 51, 56, 58, 59, 89], "eject": 19, "element": [3, 17, 20, 29, 36, 37, 42, 43, 49, 51, 62, 64, 65], "elementari": 87, "elementwis": 36, "elif": [39, 58, 66, 69, 70, 71, 72, 73, 77, 78, 79, 80, 81, 88, 89], "ellips": [15, 24, 30], "ellipt": [24, 30], "els": [1, 8, 33, 36, 43, 44, 45, 46, 50, 58, 59, 64, 66, 69, 86, 89], "elsewher": [1, 84], "else\ubb38\uc740": 77, "emb": 23, "embed": [23, 29], "emit": 39, "emoji": 28, "emphasi": 84, "emploi": 84, "empti": [5, 19, 23, 25, 27, 28, 29, 32, 33, 35, 44, 45, 46, 50, 53, 59, 64], "emul": [23, 28, 39, 59], "enabl": [23, 25, 28, 29, 33, 44, 47, 48, 63, 67], "encapsul": 88, "enclos": [24, 50, 68], "encod": [9, 14, 22, 28, 29, 40, 44, 46, 51], "encode_file_path": 44, "encode_str": 44, "encount": 35, "end": [19, 24, 25, 30, 31, 33, 38, 40, 44, 45, 50, 58, 61, 62, 63, 66, 69, 74, 84, 85, 88], "end_index": 65, "end_po": 24, "endcap": [24, 30], "endev": [38, 40], "endian": [17, 44], "endpoint": [24, 30, 45], "enemi": [36, 64], "engin": [63, 67, 84], "enhanc": [15, 28, 29], "enjoy": 70, "enlarg": 29, "enough": [29, 50, 57, 58, 60, 62, 63, 64, 65, 67, 71, 84], "ensur": [23, 25, 27, 33, 38, 53, 62, 63, 84, 85, 86], "entail": 84, "enter": [25, 28, 33, 39, 48, 58, 67], "entertain": 32, "entir": [15, 19, 23, 24, 35, 38, 48, 50, 51, 62, 63, 64, 68, 69, 70, 72, 84], "entiti": 62, "entri": [17, 33, 51, 84], "enumer": 18, "env": [66, 86], "env_var": 44, "environ": [23, 25, 28, 32, 37, 44, 46, 63, 67, 68, 86, 87], "epsilon": 36, "equal": [13, 17, 20, 22, 24, 25, 29, 33, 35, 36, 51, 67, 73], "equat": [35, 69], "equip": 65, "equival": [17, 29, 35, 37, 42, 43, 84], "eras": [32, 35, 50, 58, 62, 63, 64, 84], "err": [86, 89], "errno": 37, "error": [1, 2, 6, 9, 11, 20, 23, 25, 26, 28, 29, 33, 36, 37, 43, 44, 46, 51, 53, 56, 58, 60, 64, 65, 74, 84, 86], "error_msg": 44, "errorstr": 44, "esc": 26, "escap": [14, 29, 33, 58, 84, 89], "especi": [25, 29, 44, 64, 65, 86], "essenti": 65, "etc": [25, 31, 32, 37, 42, 45, 61, 65, 67, 84], "etyp": 44, "euclidean": [36, 42], "euro": 33, "eval": 53, "evalu": 58, "even": [14, 18, 19, 23, 24, 26, 28, 29, 32, 36, 40, 42, 44, 54, 57, 62, 63, 64, 65, 67, 68, 72, 84], "event": [0, 15, 18, 22, 23, 24, 26, 32, 33, 34, 37, 38, 39, 40, 47, 54, 57, 61, 62, 63, 66, 67, 69, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 89], "event_nam": 25, "eventlist": [25, 26], "eventtyp": [5, 25], "eventu": [12, 23, 25], "event\ubb38": [76, 78], "event\ubb38\uc5d0": 80, "event\ubb38\uc5d0\uc11c": 79, "event\ubb38\uc744": 79, "event\ubb38\uc774": 78, "ever": [37, 40, 59], "everi": [15, 18, 19, 22, 25, 26, 32, 33, 35, 36, 38, 40, 44, 50, 51, 52, 54, 56, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 74, 84, 85, 86, 88], "everyth": [25, 43, 44, 62, 63, 64, 65, 66, 67, 68, 73, 84, 85, 89], "evil": 84, "evolv": 26, "ex": [39, 70], "exact": [20, 22, 23, 24, 31, 32, 39, 57, 72, 73], "exactli": [37, 42, 45, 51, 58, 62, 63, 64, 65, 68, 84], "examin": 15, "exampl": [10, 15, 17, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 44, 45, 46, 47, 49, 50, 51, 53, 56, 57, 60, 61, 62, 63, 64, 67, 68, 70, 71, 74, 84, 85, 86, 87, 88], "exce": 51, "exceedingli": 29, "excel": [58, 62, 63], "except": [1, 3, 4, 5, 6, 7, 8, 9, 11, 14, 20, 22, 23, 24, 25, 28, 29, 31, 33, 37, 38, 44, 45, 46, 51, 56, 59, 60, 61, 65, 69, 86, 89], "exchang": 42, "excit": [58, 62, 63, 65], "exclaim": 33, "exclud": [17, 25, 51, 53], "exclus": [37, 51, 59], "execut": [15, 39, 53, 66, 67, 68, 69, 70, 71, 73, 84], "exemplari": 18, "exist": [18, 19, 22, 23, 24, 25, 28, 29, 32, 33, 37, 47, 48, 50, 51, 64], "exit": [23, 24, 32, 37, 44, 48, 62, 63, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 85, 86, 89], "expand": [56, 61], "expans": 56, "expect": [20, 27, 31, 33, 36, 37, 38, 39, 43, 53, 57, 62], "expens": 65, "experi": [25, 63, 64, 70], "experiment": [18, 23, 25, 28, 30, 33, 36, 44, 45, 46, 48, 51, 56, 57], "expir": 53, "explain": [58, 62, 64, 68, 71, 73, 86, 87, 89], "explan": [26, 36, 60, 64, 66, 68, 70, 84], "explanatori": 58, "explicit": [23, 39, 43], "explicitli": [18, 23, 29, 30, 42, 54, 56, 58, 60], "explor": 63, "explos": 64, "export": [0, 10, 20, 31, 38, 42, 43, 51], "expos": [2, 10, 29, 43, 51], "express": [18, 20], "extend": [17, 24, 31, 44, 50], "extens": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 31, 56, 63], "extern": [22, 25, 49, 52, 67], "extptr": 9, "extra": [22, 29, 38, 45, 50, 51, 58, 59, 62, 63, 64, 65, 70, 84, 89], "extract": [42, 56, 58], "extrem": [26, 58, 62, 65], "ey": 63, "f": [1, 17, 18, 32, 33, 45, 84, 86, 89], "f1": 33, "f10": 33, "f11": 33, "f12": 33, "f13": 33, "f14": 33, "f15": 33, "f2": 33, "f3": 33, "f4": 33, "f5": 33, "f6": 33, "f7": 33, "f8": 33, "f9": 33, "face": 15, "fact": [30, 64, 84], "factor": [26, 29, 56, 65], "fade": [26, 38, 40, 65], "fade_m": [38, 40], "fadeout": [38, 40], "fail": [23, 25, 27, 29, 44, 53, 58, 60, 84], "failur": [1, 2, 3, 4, 5, 7, 8, 53], "fairli": [50, 60, 63, 84, 86, 87], "fake": [26, 28, 53], "fall": [28, 29, 50], "fals": [2, 4, 5, 6, 11, 12, 15, 17, 18, 19, 22, 23, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 44, 45, 46, 47, 48, 50, 53, 56, 57, 58, 62, 66, 89], "famili": 28, "familiar": [62, 63, 64, 84, 85, 88], "fan": 15, "fantast": 62, "far": [20, 36, 58, 62, 71, 84, 87, 88, 89], "farther": [58, 62], "fast": [32, 35, 42, 51, 52, 56, 58, 62, 63, 64, 65, 69, 84], "faster": [31, 36, 44, 50, 51, 52, 56, 58, 64, 65, 67, 84], "fastest": [23, 51, 58, 63], "fastev": 27, "fastrendergroup": 26, "favorit": 84, "favour": 27, "fbcon": 23, "fear": 84, "featur": [15, 23, 25, 26, 27, 28, 29, 35, 36, 44, 45, 50, 51, 56, 59, 64, 65, 84, 87], "fed": 18, "feed": 89, "feedback": [23, 28, 33, 36, 45, 51, 56], "feel": [26, 62, 64, 84], "felt": 63, "fetch": 50, "fever": [58, 66], "few": [22, 28, 54, 62, 63, 64, 65, 74, 84, 86], "ffff": 14, "fgcolor": 29, "field": [4, 8, 12, 17, 32, 37], "fighter": 63, "figur": [62, 84], "file": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 22, 23, 25, 26, 28, 29, 31, 38, 40, 44, 49, 53, 58, 61, 63, 65, 67, 68, 73, 84, 86], "file_path": [26, 28], "filenam": [6, 22, 28, 29, 31, 38, 40, 62, 86], "filenotfounderror": [86, 89], "fileobj": [31, 40], "filesystem": 14, "fill": [5, 8, 9, 15, 22, 23, 24, 25, 26, 29, 30, 32, 35, 39, 43, 48, 50, 51, 56, 57, 58, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 89], "fill_rect": 48, "filled_": 30, "filled_circl": 30, "filled_ellips": 30, "filled_polygon": 30, "filled_trigon": 30, "fill\ud568\uc218\ub098": 76, "filter": [15, 25, 35, 56, 65], "fin": 26, "final": [22, 26, 28, 29, 42, 44, 51, 58, 61, 62, 71, 84, 85, 86, 89], "final_level": 36, "find": [15, 18, 19, 23, 24, 25, 26, 28, 32, 35, 36, 38, 50, 51, 56, 57, 58, 59, 62, 63, 64, 65, 69, 71, 84, 87, 89], "find_channel": 38, "fine": [38, 51, 59, 64, 84], "finer": 57, "finger": [55, 70, 86], "finger_id": 25, "fingerdown": 25, "fingermot": 25, "fingerup": 25, "finish": [12, 33, 38, 40, 51, 60, 61, 62, 63, 65, 84], "finit": 35, "fire": [65, 84], "firebrick": 21, "firebrick1": 21, "firebrick2": 21, "firebrick3": 21, "firebrick4": 21, "firmer": [61, 63], "first": [3, 17, 19, 24, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 40, 42, 43, 45, 49, 50, 51, 52, 54, 57, 58, 59, 60, 61, 63, 64, 65, 68, 69, 70, 71, 72, 73, 84, 85, 87, 88, 89], "firstli": 70, "fist": [15, 58, 66], "fist_offset": [58, 66], "fit": [16, 18, 23, 29, 38, 45, 51, 85], "five": [38, 39, 40], "fix": [25, 29, 51, 56, 63, 65, 68, 69, 71, 84, 89], "fixed_s": 29, "fixed_width": 29, "flag": [1, 17, 20, 23, 24, 26, 28, 29, 32, 34, 35, 43, 50, 51, 58, 59, 64, 84], "flame": 65, "flash": 15, "flavor": 84, "flesh": 15, "flexibl": [15, 60, 64, 86, 87], "flip": [15, 18, 22, 23, 24, 25, 31, 32, 35, 39, 42, 45, 48, 50, 56, 57, 58, 63, 65, 66, 75, 76, 77, 78, 79, 80, 81, 84, 85, 89], "flip_i": [48, 56], "flip_x": [48, 56], "float": [1, 17, 19, 20, 24, 25, 29, 30, 32, 35, 36, 38, 40, 45, 48, 50, 54, 56, 65], "flood": [73, 81], "floor": 20, "floralwhit": 21, "flourish": 70, "flush": [31, 37], "fly": [45, 84, 85, 88], "fnt": 29, "focu": [23, 25, 32, 33, 39, 44, 48, 57, 67], "folder": [58, 63], "folk": 62, "follow": [17, 18, 22, 24, 25, 26, 29, 30, 31, 32, 33, 35, 36, 37, 38, 40, 45, 46, 47, 49, 50, 52, 58, 61, 63, 64, 65, 66, 67, 84], "font": [6, 15, 26, 32, 44, 58, 60, 63, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85], "font_index": [6, 29], "fonti": 26, "foo": 84, "fool": [63, 65], "forbidden": 9, "forc": [23, 26, 38, 44, 63], "forcibli": 38, "forego": 25, "foreground": [25, 29], "foreign": 62, "forestgreen": 21, "forev": [62, 84], "forget": [32, 64, 87], "form": [18, 22, 25, 30, 37, 51, 84, 85], "formal": 62, "format": [18, 20, 22, 23, 24, 29, 30, 31, 35, 37, 38, 40, 41, 42, 43, 44, 45, 49, 51, 52, 56, 58, 59, 62, 63, 65, 68, 84, 85], "formula": [20, 36, 42, 44, 51, 56, 87], "forth": 58, "fortun": [26, 62, 65], "forward": 33, "found": [15, 20, 26, 28, 29, 33, 34, 35, 37, 45, 49, 50, 51, 52, 58, 59, 62, 65, 70, 85, 88], "four": [3, 19, 22, 23, 24, 29, 45, 52, 70, 89], "fourth": [22, 71], "fout": 26, "fp": [15, 46, 50, 69, 77, 84], "fpsclock": [69, 70, 71, 72, 73, 77, 78, 79, 80, 81], "fps\uac00": 77, "fps\uac12\uc774": 77, "fps\ub294": 77, "fps\ub300\ub85c": 77, "fraction": 29, "frame": [15, 18, 23, 25, 27, 32, 44, 50, 54, 57, 58, 62, 63, 64, 69, 77, 84, 85, 88, 89], "framebuff": 23, "framer": [15, 18, 54, 57, 58, 62], "framework": [26, 63, 84], "free": [9, 25, 26, 40, 62, 64, 84], "freed": 11, "freedom": [15, 63], "freesansbold": 28, "freetyp": [6, 15, 26, 28, 29, 44], "freetype2": 29, "freetype_misc": [26, 29], "frequenc": [37, 38], "frequency_to_midi": 37, "frequent": [32, 62, 64, 69, 84], "fresh": 84, "friendli": [24, 68], "friendlier": 63, "frill": 64, "from": [0, 3, 5, 6, 8, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 60, 61, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89], "from_display_modul": 48, "from_joystick": 47, "from_polar": 36, "from_spher": 36, "from_surfac": [35, 48, 50], "from_threshold": [35, 57], "from_window": 48, "frombuff": 31, "frombyt": 31, "fromstr": 31, "front": 50, "frontend": 25, "frozen": [53, 84], "frustrat": 84, "ftfont": [28, 29], "fuchsia": 21, "full": [15, 23, 25, 26, 27, 28, 29, 31, 32, 38, 39, 40, 42, 50, 51, 56, 57, 58, 59, 62, 63, 64, 66, 70, 84, 85, 86, 89], "fulli": [20, 23, 33, 45, 48, 50, 51, 52, 58, 62, 65, 84], "fullnam": [58, 66, 86, 89], "fullscreen": [23, 34, 48, 50, 59], "fullscreen_desktop": 48, "fun": [26, 57, 62, 63, 65, 84], "func": 45, "function": [0, 1, 7, 8, 9, 12, 15, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 63, 64, 66, 67, 68, 69, 70, 72, 73, 84, 85, 87, 88, 89], "fundament": [61, 84], "funni": 64, "further": 46, "furthermor": [68, 70, 71], "futur": [18, 19, 23, 25, 27, 28, 29, 32, 37, 38, 41, 46, 64], "g": [20, 23, 24, 26, 28, 30, 31, 33, 36, 37, 42, 43, 44, 45, 50, 51, 56, 84, 87, 88], "gain": [23, 25, 29, 57, 84], "gainsboro": 21, "game": [15, 16, 18, 22, 23, 25, 26, 27, 32, 39, 44, 47, 51, 54, 57, 59, 60, 62, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 86, 88, 89], "gameobject": [62, 84], "gamepad": [25, 47], "gameplai": [33, 64, 89], "gamma": [20, 23], "gap": [17, 51], "garbag": [12, 50, 51], "gather": 59, "gaussian": 65, "gener": [1, 15, 23, 25, 26, 31, 32, 33, 36, 37, 39, 40, 42, 47, 51, 52, 56, 57, 58, 63, 64, 65, 68, 73, 84, 86, 89], "generateboard": [73, 81], "geniu": 74, "geometri": [67, 71], "get": [2, 15, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 35, 37, 38, 39, 40, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 85, 86, 87, 88, 89], "get_abs_offset": 51, "get_abs_par": 51, "get_act": 23, "get_al": 19, "get_allow_screensav": 23, "get_alpha": [51, 86, 89], "get_and_flip": 57, "get_arraytyp": [49, 52], "get_asc": 28, "get_at": [35, 51, 56, 58, 65, 66, 84], "get_at_map": [20, 51], "get_axi": [32, 47], "get_backend": 18, "get_bal": 32, "get_bits": 51, "get_block": 25, "get_bold": 28, "get_bottom_lay": 50, "get_bounding_rect": [35, 51], "get_buff": [2, 17, 51], "get_busi": [19, 38, 40], "get_button": [32, 47], "get_bytes": [42, 51], "get_cache_s": 29, "get_capt": 23, "get_clip": [50, 51], "get_colorkei": 51, "get_control": [18, 57], "get_count": [19, 32, 37, 47], "get_curr": 19, "get_cursor": 39, "get_default_font": [28, 29], "get_default_input_id": 37, "get_default_output_id": 37, "get_default_resolut": 29, "get_desc": 28, "get_desktop_s": [23, 59], "get_devic": 55, "get_device_info": 37, "get_driv": [23, 59], "get_empti": 19, "get_endev": [38, 40], "get_error": [29, 44], "get_eventst": 47, "get_extend": 31, "get_fing": 55, "get_flag": 51, "get_focus": [23, 33, 39], "get_font": 28, "get_fp": 54, "get_grab": 25, "get_guid": 32, "get_hardwar": 41, "get_hat": 32, "get_height": [15, 28, 51], "get_id": [19, 32], "get_imag": [18, 57], "get_init": [19, 23, 27, 28, 29, 32, 37, 38, 44, 46, 47, 49, 58, 60, 66], "get_instance_id": 32, "get_ital": 28, "get_keyboard_grab": 25, "get_layer_of_sprit": 50, "get_length": 38, "get_lines": 28, "get_lock": 51, "get_loss": 51, "get_map": 47, "get_mask": 51, "get_metr": 29, "get_mod": 33, "get_nam": [19, 32], "get_num_channel": 38, "get_num_devic": 55, "get_num_displai": 23, "get_num_fing": 55, "get_numax": 32, "get_numbal": 32, "get_numbutton": 32, "get_numhat": 32, "get_numtrack": 19, "get_offset": 51, "get_palett": 51, "get_palette_at": 51, "get_par": 51, "get_paus": 19, "get_pitch": 51, "get_po": [39, 40, 58, 66, 84], "get_power_level": 32, "get_press": [15, 33, 39, 62, 84], "get_queu": 38, "get_raw": [18, 38], "get_rawtim": 54, "get_rect": [29, 35, 48, 50, 51, 56, 58, 62, 63, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 85, 86, 87, 88, 89], "get_rel": 39, "get_repeat": 33, "get_sdl_byteord": 44, "get_sdl_image_vers": 31, "get_sdl_mixer_vers": 38, "get_sdl_ttf_vers": 28, "get_sdl_vers": [23, 44], "get_shift": 51, "get_siz": [18, 29, 35, 42, 51, 58, 66, 85, 89], "get_sized_ascend": 29, "get_sized_descend": 29, "get_sized_glyph_height": 29, "get_sized_height": 29, "get_smoothscale_backend": 56, "get_sound": 38, "get_sprit": 50, "get_sprites_at": 50, "get_sprites_from_lay": 50, "get_strikethrough": 28, "get_surfac": [23, 58, 66, 87, 88, 89], "get_tick": 54, "get_tim": 54, "get_top_lay": 50, "get_top_sprit": 50, "get_track_audio": 19, "get_track_length": 19, "get_track_start": 19, "get_typ": 46, "get_underlin": 28, "get_vers": 29, "get_view": [17, 51], "get_viewport": 48, "get_vis": 39, "get_volum": [38, 40], "get_width": [15, 51, 58, 66], "get_window_s": 23, "get_wm_info": 23, "getbufferproc": 2, "getch": [67, 75], "getfilesystemencod": [14, 44], "getopt": [86, 89], "gfxdraw": [10, 15, 24, 30], "gg": 20, "ggi": 23, "ghost": 64, "ghostwhit": 21, "gif": [31, 63, 79, 80, 81, 84], "gil": [9, 30], "github": [44, 46, 56], "give": [15, 17, 22, 23, 29, 32, 37, 40, 42, 50, 51, 58, 59, 60, 61, 65, 74, 84, 85, 86, 87], "given": [8, 13, 17, 20, 22, 23, 24, 25, 28, 29, 30, 32, 35, 36, 37, 38, 39, 44, 45, 46, 47, 50, 51, 52, 54, 55, 56, 58, 59, 64, 68, 70, 73, 84], "gl": 23, "gl_accelerated_visu": 23, "gl_accum_alpha_s": 23, "gl_accum_blue_s": 23, "gl_accum_green_s": 23, "gl_accum_red_s": 23, "gl_alpha_s": 23, "gl_buffer_s": 23, "gl_context_flag": 23, "gl_context_major_vers": 23, "gl_context_minor_vers": 23, "gl_context_profile_": 23, "gl_context_profile_compat": 23, "gl_context_profile_cor": 23, "gl_context_profile_mask": 23, "gl_context_release_behavior": 23, "gl_depth_siz": 23, "gl_framebuffer_srgb_cap": 23, "gl_get_attribut": 23, "gl_multisamplebuff": 23, "gl_multisamplesampl": 23, "gl_set_attribut": 23, "gl_share_with_current_context": 23, "gl_stencil_s": 23, "gl_stereo": 23, "glcube": 26, "glitch": 89, "global": [28, 53, 60, 68, 86, 89], "glsl": 36, "glue": 89, "glyph": [28, 29], "gnu": [86, 89], "go": [20, 22, 23, 24, 32, 36, 37, 51, 53, 57, 58, 59, 60, 61, 64, 65, 66, 68, 70, 74, 84, 85, 86, 88, 89], "goal": 63, "goe": [58, 62, 64, 68, 84, 89], "gold": 21, "gold1": 21, "gold2": 21, "gold3": 21, "gold4": 21, "golden": 59, "goldenrod": 21, "goldenrod1": 21, "goldenrod2": 21, "goldenrod3": 21, "goldenrod4": 21, "gone": [62, 89], "good": [15, 18, 22, 23, 26, 28, 38, 44, 51, 58, 61, 62, 63, 64, 65, 67, 68, 84, 85, 86, 87, 89], "goodluck": 26, "googl": 84, "got": [15, 25, 40, 57, 62, 64, 84], "gotten": 62, "grab": [25, 33, 39, 48, 62, 64, 89], "grace": 89, "gradient": [26, 35, 65], "grai": [21, 29, 71, 72, 73, 79, 80, 81], "grain": 84, "graphic": [23, 26, 56, 58, 59, 62, 63, 64, 65, 67, 68, 84], "grasp": [61, 89], "grave": 33, "gray0": 21, "gray1": 21, "gray10": 21, "gray100": 21, "gray11": 21, "gray12": 21, "gray13": 21, "gray14": 21, "gray15": 21, "gray16": 21, "gray17": 21, "gray18": 21, "gray19": 21, "gray2": 21, "gray20": 21, "gray21": 21, "gray22": 21, "gray23": 21, "gray24": 21, "gray25": 21, "gray26": 21, "gray27": 21, "gray28": 21, "gray29": 21, "gray3": 21, "gray30": 21, "gray31": 21, "gray32": 21, "gray33": 21, "gray34": 21, "gray35": 21, "gray36": 21, "gray37": 21, "gray38": 21, "gray39": 21, "gray4": 21, "gray40": 21, "gray41": 21, "gray42": 21, "gray43": 21, "gray44": 21, "gray45": 21, "gray46": 21, "gray47": 21, "gray48": 21, "gray49": 21, "gray5": 21, "gray50": 21, "gray51": 21, "gray52": 21, "gray53": 21, "gray54": 21, "gray55": 21, "gray56": 21, "gray57": 21, "gray58": 21, "gray59": 21, "gray6": 21, "gray60": 21, "gray61": 21, "gray62": 21, "gray63": 21, "gray64": 21, "gray65": 21, "gray66": 21, "gray67": 21, "gray68": 21, "gray69": 21, "gray7": 21, "gray70": 21, "gray71": 21, "gray72": 21, "gray73": 21, "gray74": 21, "gray75": 21, "gray76": 21, "gray77": 21, "gray78": 21, "gray79": 21, "gray8": 21, "gray80": 21, "gray81": 21, "gray82": 21, "gray83": 21, "gray84": 21, "gray85": 21, "gray86": 21, "gray87": 21, "gray88": 21, "gray89": 21, "gray9": 21, "gray90": 21, "gray91": 21, "gray92": 21, "gray93": 21, "gray94": 21, "gray95": 21, "gray96": 21, "gray97": 21, "gray98": 21, "gray99": 21, "grayscal": [20, 56], "great": [23, 57, 63, 65, 84], "greater": [17, 24, 28, 29, 33, 35, 36, 37, 40, 51, 74], "green": [1, 18, 20, 21, 23, 24, 42, 51, 52, 56, 57, 58, 65, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85], "green1": 21, "green2": 21, "green3": 21, "green4": 21, "greenyellow": 21, "grei": [21, 58], "grey0": 21, "grey1": 21, "grey10": 21, "grey100": 21, "grey11": 21, "grey12": 21, "grey13": 21, "grey14": 21, "grey15": 21, "grey16": 21, "grey17": 21, "grey18": 21, "grey19": 21, "grey2": 21, "grey20": 21, "grey21": 21, "grey22": 21, "grey23": 21, "grey24": 21, "grey25": 21, "grey26": 21, "grey27": 21, "grey28": 21, "grey29": 21, "grey3": 21, "grey30": 21, "grey31": 21, "grey32": 21, "grey33": 21, "grey34": 21, "grey35": 21, "grey36": 21, "grey37": 21, "grey38": 21, "grey39": 21, "grey4": 21, "grey40": 21, "grey41": 21, "grey42": 21, "grey43": 21, "grey44": 21, "grey45": 21, "grey46": 21, "grey47": 21, "grey48": 21, "grey49": 21, "grey5": 21, "grey50": 21, "grey51": 21, "grey52": 21, "grey53": 21, "grey54": 21, "grey55": 21, "grey56": 21, "grey57": 21, "grey58": 21, "grey59": 21, "grey6": 21, "grey60": 21, "grey61": 21, "grey62": 21, "grey63": 21, "grey64": 21, "grey65": 21, "grey66": 21, "grey67": 21, "grey68": 21, "grey69": 21, "grey7": 21, "grey70": 21, "grey71": 21, "grey72": 21, "grey73": 21, "grey74": 21, "grey75": 21, "grey76": 21, "grey77": 21, "grey78": 21, "grey79": 21, "grey8": 21, "grey80": 21, "grey81": 21, "grey82": 21, "grey83": 21, "grey84": 21, "grey85": 21, "grey86": 21, "grey87": 21, "grey88": 21, "grey89": 21, "grey9": 21, "grey90": 21, "grey91": 21, "grey92": 21, "grey93": 21, "grey94": 21, "grey95": 21, "grey96": 21, "grey97": 21, "grey98": 21, "grey99": 21, "greyscal": 56, "grid": 29, "ground": 62, "group": [26, 35, 50, 58, 63, 84, 86], "group1": [50, 64], "group2": [50, 64], "group_list": 50, "groupcollid": [50, 64, 87], "groupmulti": 64, "groupsingl": [50, 64], "grow": [24, 45, 84], "guarante": [18, 23, 25, 33, 38, 46, 53], "guess": [38, 84], "gui": [25, 63, 67, 71, 72, 73, 75, 79], "guid": [15, 32, 57, 65, 85], "gui\uac00": 80, "gui\ub97c": 76, "gui\uc5d0\uc11c\uc758": 80, "gui\uc774\ubbc0\ub85c": 76, "gui\uc774\uc9c0\ub9cc": 81, "gui\uc784\uc744": 76, "gui\ud658\uacbd\uc5d0\uc11c": 76, "gun": 71, "g\uac12": 76, "h": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 20, 25, 26, 30, 33, 42, 43, 45, 51, 56], "ha": [1, 2, 10, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 35, 36, 38, 39, 40, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 84, 86, 87, 89], "habit": [84, 87], "had": [24, 51, 63, 64, 84], "hadn": 84, "half": [24, 26, 36, 50, 63], "hand": [20, 22, 42, 43, 58, 60, 62, 84, 87], "handi": [26, 61, 84, 86, 87, 88, 89], "handili": 89, "handl": [9, 14, 15, 23, 25, 28, 29, 32, 33, 38, 43, 44, 50, 56, 59, 61, 63, 64, 65, 66, 70, 84, 87, 89], "handler": [25, 27], "hang": 32, "happen": [19, 24, 58, 60, 62, 63, 64, 84, 85, 88, 89], "hard": [62, 63, 65, 68], "hardcod": 26, "harder": [61, 65, 84], "hardest": 64, "hardwar": [22, 23, 24, 30, 32, 37, 38, 41, 51, 63, 64, 65, 84], "harfbuzz": 28, "harmless": 23, "hasattr": 28, "hash": 33, "hashabl": 45, "hasn": 29, "hat": [25, 32, 47], "hat_numb": 32, "have": [1, 3, 8, 10, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 35, 36, 38, 40, 41, 42, 43, 44, 45, 47, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70, 72, 73, 84, 85, 86, 87, 88, 89], "haven": 84, "he": [58, 84, 85], "header": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 68, 70, 76], "header\uc5d0\uc120": 76, "header\uc758": 78, "headless": 26, "headless_no_windows_need": 26, "heavili": 25, "height": [8, 18, 22, 23, 24, 26, 28, 29, 31, 32, 35, 41, 42, 45, 48, 50, 51, 56, 59, 62, 63, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84], "held": [33, 62, 88], "hello": [68, 69, 70, 76, 77, 78, 85], "help": [15, 22, 25, 26, 28, 32, 33, 36, 37, 38, 39, 48, 50, 51, 53, 54, 58, 59, 61, 64, 65, 70, 84, 86, 87, 88], "helper": 63, "henc": 29, "here": [15, 16, 18, 23, 25, 26, 34, 35, 39, 41, 44, 50, 51, 58, 59, 60, 64, 65, 66, 68, 70, 84, 85, 86, 87, 88, 89], "hex": 20, "hflip": [18, 57], "hi": [58, 62], "hidden": [23, 25, 39], "hide": [33, 39, 44, 48, 58], "high": [0, 23, 26, 50, 67, 69], "high_frequ": [32, 47], "higher": [15, 16, 23, 36, 37, 47, 50, 52, 59, 63, 64], "highest": 44, "highgui": 44, "highli": [27, 51, 84], "him": [58, 62], "hindi": 28, "hint": [23, 84], "hit": [25, 26, 58, 61, 84, 85, 87, 88], "hitbox": [58, 66], "hkey_local_machin": 37, "hline": 30, "hmm": 62, "hold": [1, 25, 28, 32, 33, 50, 56, 64, 65, 70], "holdov": 64, "home": [32, 33], "honeydew": 21, "honeydew1": 21, "honeydew2": 21, "honeydew3": 21, "honeydew4": 21, "hook": 50, "hoonwhitecatr": [68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "hope": 64, "hopefulli": [62, 65, 87], "horizont": [18, 24, 25, 26, 29, 30, 39, 56, 58, 62, 65], "horizontal_advance_i": 29, "horizontal_advance_x": 29, "hot": 25, "hotkei": 25, "hotpink": 21, "hotpink1": 21, "hotpink2": 21, "hotpink3": 21, "hotpink4": 21, "hotplug": 32, "hotspot": [22, 39], "hour": 84, "how": [14, 15, 19, 20, 23, 24, 26, 29, 31, 32, 33, 35, 36, 38, 40, 44, 46, 50, 51, 54, 56, 58, 61, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 84, 87, 88, 89], "howev": [12, 18, 29, 31, 33, 34, 36, 42, 44, 45, 47, 50, 56, 62, 67, 68, 69, 70, 74, 84], "hp": [70, 71, 72, 73, 78, 79, 80, 81], "hp\ub294": [79, 81], "hp\ub97c": [79, 80], "hp\ubc14": 83, "hp\uc744": 79, "hp\uc758": 79, "hsl": 20, "hsla": 20, "hsv": [18, 20, 57], "hsva": 20, "html": [20, 70, 78], "http": [39, 56, 70, 78, 86, 89], "hue": 18, "human": [63, 74, 86], "humung": 63, "hundr": 63, "husano896": 39, "hw": [23, 59], "hwaccel": 51, "hwsurfac": [23, 51, 65, 84], "hx": 47, "i": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 67, 68, 69, 71, 72, 73, 74, 79, 80, 81, 85, 86, 87, 88, 89], "i1": 20, "i1i2i3": 20, "i2": 20, "i3": 20, "i686": 56, "icc": 25, "iceberg": 64, "icon": [23, 48], "iconifi": 23, "icontitl": 23, "id": [19, 23, 25, 32, 33, 37, 38, 48, 55], "id3": 40, "idea": [23, 51, 57, 59, 61, 63, 65, 70, 71, 72, 73, 86, 87], "ident": [25, 42, 51, 72], "identif": 37, "identifi": [25, 28, 32, 33, 44, 46, 57, 84], "idiom": [58, 84], "idl": [24, 25, 27, 32, 38, 40], "idx": 50, "ie": [31, 65], "ignor": [25, 29, 35, 37, 38, 40, 51, 53, 84], "illeg": 45, "illus": 62, "illustr": [62, 65, 87], "im": 33, "imag": [11, 12, 15, 16, 18, 20, 22, 23, 26, 28, 29, 30, 41, 46, 48, 50, 52, 56, 58, 59, 61, 63, 64, 65, 66, 67, 69, 70, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89], "image_fil": 26, "imagefil": 26, "imagin": [62, 85, 89], "imgsurfac": 65, "immanuel": 74, "immedi": [23, 25, 32, 37, 38, 58], "immut": 20, "implement": [9, 10, 12, 17, 18, 20, 23, 26, 29, 33, 37, 42, 43, 44, 50, 61, 63, 64, 73, 74, 84, 89], "impli": [18, 29], "implicitli": [23, 51], "import": [1, 10, 15, 18, 22, 24, 25, 26, 27, 28, 29, 30, 32, 34, 36, 38, 39, 44, 47, 49, 52, 53, 59, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 86, 89], "import_pygame_bas": [1, 10], "importantli": 62, "importerror": [65, 86, 89], "import\ud558\ub294": 76, "impos": [33, 38], "imprecis": 84, "impress": 63, "improperli": 41, "improv": [24, 28, 40, 58, 73, 84], "inact": [29, 38], "inch": 29, "incident": 18, "inclin": 36, "includ": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 22, 23, 26, 28, 29, 30, 31, 32, 34, 37, 44, 45, 50, 51, 53, 54, 56, 58, 61, 63, 64, 65, 67, 70, 74, 84, 86, 87], "inclus": [1, 3, 20, 25, 29, 38], "incom": 15, "incomplet": 53, "incorrect": 43, "increas": [23, 29, 38, 45, 50, 68, 72, 74, 84], "incredibli": [74, 84], "increment": [10, 56, 65], "inde": 3, "indefinit": [38, 40], "indent": [32, 61], "independ": [15, 31, 58, 86], "index": [1, 6, 15, 18, 19, 23, 25, 29, 32, 33, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 55, 59, 62, 65, 84], "indexerror": [30, 35, 37, 51], "indexexcept": 17, "indexoutofbound": 50, "indianr": 21, "indianred1": 21, "indianred2": 21, "indianred3": 21, "indianred4": 21, "indic": [11, 23, 24, 25, 30, 35, 37, 39, 40, 42, 45, 46, 49, 50, 52, 53, 65], "indices0": 45, "indices1": 45, "indices2": 45, "indices3": 45, "indices4": 45, "indices5": 45, "indigo": 21, "indirect": 18, "individu": [15, 19, 29, 35, 44, 51, 53, 58, 84], "ineffici": 58, "inequ": 25, "infinit": [58, 63, 68], "inflat": [29, 45, 58, 66, 84, 89], "inflate_ip": 45, "influenc": 23, "info": [4, 23, 37, 59, 86], "inform": [5, 13, 15, 17, 18, 19, 23, 25, 28, 32, 33, 36, 37, 39, 47, 51, 52, 53, 55, 59, 62, 64, 65, 68], "inherit": [36, 50, 51, 64, 86, 87], "init": [1, 15, 18, 19, 22, 23, 24, 27, 28, 29, 32, 37, 38, 39, 44, 46, 47, 53, 54, 58, 59, 62, 63, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 89], "initi": [15, 18, 19, 23, 24, 25, 27, 28, 29, 32, 33, 34, 37, 38, 44, 46, 47, 49, 50, 51, 54, 57, 59, 63, 64, 66, 68, 69, 77, 84, 87], "initialis": [32, 44, 47, 61, 85, 88, 89], "initial\ubb38": 76, "initial\ubb38\uc5d0": 76, "initial\ubb38\uc758": 77, "innat": 74, "inner": 72, "input": [15, 18, 23, 25, 26, 28, 32, 33, 39, 44, 45, 48, 51, 53, 56, 57, 61, 63, 66, 67, 68, 69, 72, 73, 74, 78, 84, 87], "input_onli": 48, "inputimag": 26, "input\ud568\uc218\ub97c": 76, "input\ud568\uc218\uc640\ub294": 76, "insensit": [43, 51], "insert": [33, 69, 73], "insid": [22, 24, 26, 31, 44, 45, 50, 51, 57, 58, 63, 65, 71, 72, 89], "insight": 84, "inspir": 15, "instal": [15, 18, 26, 28, 31, 58, 65], "instanc": [2, 3, 4, 5, 6, 7, 8, 11, 12, 17, 19, 25, 26, 29, 30, 32, 35, 36, 38, 42, 45, 50, 51, 58, 61, 64, 85, 87, 88], "instance_id": [25, 32], "instantli": 84, "instead": [8, 14, 20, 22, 23, 24, 25, 26, 28, 29, 32, 33, 36, 38, 42, 44, 50, 51, 53, 56, 62, 64, 65, 69, 84, 87], "instruct": 56, "instrument": 37, "instrument_id": 37, "int": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 17, 18, 20, 23, 24, 25, 28, 29, 30, 32, 33, 35, 36, 37, 41, 42, 44, 45, 47, 48, 50, 51, 55, 56, 71, 72, 73, 79, 80, 81], "int32": 65, "int8": 65, "int_valu": 51, "integ": [1, 8, 13, 17, 19, 20, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 37, 38, 39, 40, 42, 43, 44, 45, 47, 49, 50, 51, 52, 53, 54, 55, 59, 65, 71, 73], "integr": 18, "intend": [25, 43, 50, 51], "interact": [5, 15, 26, 33, 34, 42, 47, 50, 53, 62, 63, 65, 70, 87], "interest": [25, 46, 63, 67, 74, 84], "interf": 37, "interfac": [1, 17, 20, 29, 33, 37, 38, 42, 43, 47, 51, 57, 63, 70, 73], "interior": 56, "intern": [0, 12, 23, 24, 25, 27, 33, 38, 44, 46, 51, 58, 68, 70, 84, 89], "internet": 84, "interpol": [20, 30, 36], "interpret": [14, 26, 29, 44, 61, 84], "interrupt": 18, "intersect": [35, 45, 50, 64, 67], "interv": 33, "intimid": 65, "intric": 63, "intro_bal": 63, "introduc": [23, 25, 31, 43, 61, 62, 65, 87, 89], "introduct": [15, 60, 75], "introspect": 43, "intuit": [70, 84], "invad": 84, "invalid": [1, 23, 38, 45, 47, 56], "invalu": 87, "inverse_set": 56, "invert": [29, 35, 36, 42], "invis": 85, "invok": [17, 29, 88], "involv": [15, 26, 29, 51, 64, 84, 87, 89], "inward": 24, "in\ubb38\uc744": 76, "ip": 45, "is_control": 47, "is_norm": 36, "iscaptur": 25, "ish": 24, "isn": [19, 27, 48, 57, 61, 62, 64, 69, 71, 84, 88], "iso": 28, "isol": [51, 57], "issu": [23, 62, 63], "ital": [28, 29], "item": [29, 30, 42, 43, 45, 50, 62, 64], "items": 42, "iter": [28, 29, 33, 42, 45, 50, 64, 84, 85, 88, 89], "its": [2, 9, 12, 17, 18, 23, 24, 25, 29, 30, 32, 35, 36, 37, 38, 42, 44, 45, 46, 48, 50, 51, 58, 59, 61, 62, 63, 64, 68, 69, 70, 84, 85, 86, 87, 88, 89], "itself": [12, 18, 28, 29, 36, 42, 43, 44, 48, 51, 58, 61, 62, 63, 64, 84, 87, 88, 89], "it\uc744": 81, "ivori": 21, "ivory1": 21, "ivory2": 21, "ivory3": 21, "ivory4": 21, "iyuv_overlai": 41, "j": [33, 35, 73, 81], "jaggi": 56, "jid": 32, "jitter": 32, "job": [63, 64], "joi": 25, "join": [31, 45, 58, 66, 86, 89], "joint": 24, "journal": 84, "joyaxismot": [25, 32], "joyballmot": [25, 32], "joybuttondown": [25, 32], "joybuttonup": [25, 32], "joydevicead": [25, 32], "joydeviceremov": [25, 32], "joyhatmot": [25, 32], "joystick": [15, 25, 26, 44, 47, 63, 84, 85, 88], "joystick_count": 32, "jp": 18, "jpeg": [31, 84], "jpg": [31, 63], "juli": 16, "jumbl": 84, "jump": [39, 62, 63, 65], "just": [24, 26, 28, 29, 30, 32, 35, 38, 40, 41, 50, 51, 52, 53, 56, 57, 58, 59, 60, 63, 64, 65, 67, 68, 69, 70, 71, 73, 84, 85, 87, 88, 89], "k": [33, 36], "k_": [15, 33, 34, 70], "k_0": 33, "k_1": 33, "k_2": 33, "k_3": 33, "k_4": 33, "k_5": 33, "k_6": 33, "k_7": 33, "k_8": [33, 70, 78], "k_9": 33, "k_a": [15, 33, 70, 78, 89], "k_ac_back": 33, "k_ampersand": 33, "k_asterisk": 33, "k_at": 33, "k_b": 33, "k_backquot": 33, "k_backslash": 33, "k_backspac": 33, "k_break": 33, "k_c": 33, "k_capslock": 33, "k_caret": 33, "k_clear": 33, "k_colon": 33, "k_comma": 33, "k_d": [15, 33], "k_delet": [33, 70, 78], "k_dollar": 33, "k_down": [33, 62, 70, 71, 72, 73, 78, 79, 80, 81, 88, 89], "k_e": 33, "k_end": 33, "k_equal": 33, "k_escap": [22, 33, 57, 58, 66], "k_euro": 33, "k_exclaim": 33, "k_f": [33, 84], "k_f1": 33, "k_f10": 33, "k_f11": 33, "k_f12": 33, "k_f13": 33, "k_f14": 33, "k_f15": 33, "k_f2": 33, "k_f3": 33, "k_f4": [33, 70], "k_f5": 33, "k_f6": 33, "k_f7": 33, "k_f8": 33, "k_f9": 33, "k_g": 33, "k_greater": 33, "k_h": 33, "k_hash": 33, "k_help": 33, "k_home": 33, "k_i": 33, "k_insert": 33, "k_j": 33, "k_k": 33, "k_kp0": 33, "k_kp1": 33, "k_kp2": 33, "k_kp3": 33, "k_kp4": 33, "k_kp5": 33, "k_kp6": 33, "k_kp7": 33, "k_kp8": 33, "k_kp9": 33, "k_kp_divid": 33, "k_kp_enter": 33, "k_kp_equal": 33, "k_kp_minu": 33, "k_kp_multipli": 33, "k_kp_period": 33, "k_kp_plu": 33, "k_l": [33, 70, 78], "k_lalt": 33, "k_lctrl": [33, 70, 78], "k_left": [33, 62, 70, 78], "k_leftbracket": 33, "k_leftparen": 33, "k_less": 33, "k_lmeta": 33, "k_lshift": 33, "k_lsuper": 33, "k_m": 33, "k_menu": 33, "k_minu": 33, "k_mode": 33, "k_n": 33, "k_numlock": 33, "k_o": 33, "k_p": 33, "k_pagedown": 33, "k_pageup": 33, "k_paus": 33, "k_period": 33, "k_plu": 33, "k_power": 33, "k_print": 33, "k_q": 33, "k_question": 33, "k_quot": 33, "k_quotedbl": 33, "k_r": 33, "k_ralt": 33, "k_rctrl": 33, "k_return": 33, "k_right": [33, 62, 70, 78], "k_rightbracket": 33, "k_rightparen": 33, "k_rmeta": 33, "k_rshift": 33, "k_rsuper": 33, "k_scrollock": 33, "k_semicolon": 33, "k_slash": 33, "k_space": 33, "k_sysreq": 33, "k_t": [33, 84], "k_tab": 33, "k_u": 33, "k_underscor": 33, "k_up": [33, 62, 70, 71, 72, 73, 78, 79, 80, 81, 88, 89], "k_v": 33, "k_w": [15, 33], "k_x": 33, "k_y": 33, "k_z": [33, 89], "k_\uc2dc\ub9ac\uc988\uc774\ub2e4": 78, "kanji": 29, "kant": 74, "kb": 16, "kde": 44, "keep": [12, 17, 25, 32, 50, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 84, 88], "kei": [15, 17, 20, 22, 23, 25, 26, 33, 34, 35, 45, 51, 55, 57, 58, 61, 62, 63, 64, 66, 70, 71, 72, 73, 74, 78, 79, 80, 81, 84, 88, 89], "kern": [28, 29], "key_cod": 33, "key_rect": 45, "keyboard": [15, 25, 26, 34, 39, 62, 63, 67, 70, 84, 85], "keycod": 33, "keydown": [22, 25, 33, 34, 57, 58, 62, 66, 70, 71, 72, 73, 78, 79, 80, 81, 88, 89], "keydown\uc740": 78, "keydown\uc774": 80, "keymap": 25, "keymapchang": 25, "keypad": 33, "keypress": 84, "keyup": [25, 33, 34, 70, 88, 89], "keyup\uc774\ub77c\ub294": 78, "keyword": [15, 18, 23, 24, 25, 26, 29, 31, 35, 38, 44, 45, 48, 51, 53, 56], "key\ub294": 78, "khaki": 21, "khaki1": 21, "khaki2": 21, "khaki3": 21, "khaki4": 21, "khz": 49, "kick": 61, "kill": [50, 53, 64], "kind": [25, 37, 43, 51, 61, 65, 67, 84], "kmod_alt": 33, "kmod_cap": 33, "kmod_ctrl": 33, "kmod_lalt": 33, "kmod_lctrl": 33, "kmod_lmeta": 33, "kmod_lshift": 33, "kmod_meta": 33, "kmod_mod": 33, "kmod_non": 33, "kmod_num": 33, "kmod_ralt": 33, "kmod_rctrl": 33, "kmod_rmeta": 33, "kmod_rshift": 33, "kmod_shift": 33, "know": [18, 28, 31, 33, 46, 57, 58, 62, 64, 65, 67, 68, 69, 70, 73, 87, 88, 89], "knowledg": [74, 84], "known": [33, 84], "korean": 15, "kwarg": [35, 48, 50, 51], "kwd": 53, "l": [18, 20, 32, 33], "l_f4\ub4f1\uc774": 78, "l_margin": [73, 81], "lack": 23, "laggi": 38, "laid": [29, 61], "lambda": 45, "landscap": [62, 84], "languag": [25, 26, 28, 33, 62, 63, 84], "lantinga": 63, "laplacian": 56, "larg": [15, 29, 36, 43, 45, 51, 61, 64, 65, 84, 87], "larger": [23, 38, 51, 56, 58, 65, 67, 84], "largest": [35, 57, 59, 63, 65], "last": [15, 24, 29, 30, 32, 50, 51, 52, 54, 58, 62, 63, 64, 65, 68, 84], "lastli": [22, 50, 58, 59, 62, 64, 65], "late": 84, "latenc": [37, 38, 84], "later": [18, 23, 30, 33, 37, 38, 42, 46, 50, 57, 58, 59, 60, 62, 63, 64, 65, 68, 84, 86, 89], "latest": [29, 70], "latin1": [28, 29], "latter": 25, "lavend": 21, "lavenderblush": 21, "lavenderblush1": 21, "lavenderblush2": 21, "lavenderblush3": 21, "lavenderblush4": 21, "lawngreen": 21, "layer": [16, 50, 63, 64], "layer1": 50, "layer1_nr": 50, "layer2": 50, "layer2_nr": 50, "layer_nr": 50, "layereddirti": 50, "layeredupd": 50, "layout": [17, 25, 28, 29, 43, 52, 88], "lbm": 31, "lead": 36, "leak": 62, "learn": [26, 51, 59, 62, 63, 65, 67, 68, 69, 70, 73, 74, 84], "learnt": 89, "least": [13, 24, 30, 35, 43, 51, 55, 61, 65, 67, 70, 84], "leav": [24, 28, 29, 33, 36, 45, 51, 56, 65], "left": [20, 23, 24, 25, 29, 33, 35, 38, 39, 44, 45, 47, 50, 51, 57, 58, 62, 63, 66, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89], "leftclick": 26, "leftmost": 47, "legaci": [25, 27, 33, 67, 75], "legacy_logo": 16, "lemonchiffon": 21, "lemonchiffon1": 21, "lemonchiffon2": 21, "lemonchiffon3": 21, "lemonchiffon4": 21, "len": [17, 20, 22, 24, 30, 50, 64, 65], "length": [1, 3, 8, 17, 19, 20, 23, 24, 25, 30, 32, 33, 36, 38, 42, 47, 51, 58, 68, 87], "length_squar": 36, "leonidovich": 74, "lerp": [20, 36], "less": [24, 27, 28, 33, 35, 37, 38, 54, 55, 56, 67, 85], "lesson": 84, "let": [9, 20, 21, 26, 44, 51, 58, 59, 61, 63, 65, 69, 70, 71, 84, 88], "letter": [19, 28, 51], "level": [0, 15, 17, 20, 23, 28, 32, 36, 37, 38, 40, 41, 51, 53, 56, 63, 64, 65, 67, 70], "lgpl": 15, "li": 84, "liabil": 18, "liabl": 18, "lib": 26, "librari": [15, 18, 26, 28, 29, 30, 31, 32, 37, 38, 44, 47, 59, 62, 63, 67], "libsdl": 39, "licens": [15, 86, 89], "lie": [30, 45], "life": [57, 59], "lifetim": [12, 42, 52, 65], "lightblu": 21, "lightblue1": 21, "lightblue2": 21, "lightblue3": 21, "lightblue4": 21, "lightcor": 21, "lightcyan": 21, "lightcyan1": 21, "lightcyan2": 21, "lightcyan3": 21, "lightcyan4": 21, "lightgoldenrod": 21, "lightgoldenrod1": 21, "lightgoldenrod2": 21, "lightgoldenrod3": 21, "lightgoldenrod4": 21, "lightgoldenrodyellow": 21, "lightgrai": 21, "lightgreen": 21, "lightgrei": 21, "lightpink": 21, "lightpink1": 21, "lightpink2": 21, "lightpink3": 21, "lightpink4": 21, "lightsalmon": 21, "lightsalmon1": 21, "lightsalmon2": 21, "lightsalmon3": 21, "lightsalmon4": 21, "lightseagreen": 21, "lightskyblu": 21, "lightskyblue1": 21, "lightskyblue2": 21, "lightskyblue3": 21, "lightskyblue4": 21, "lightslateblu": 21, "lightslategrai": 21, "lightslategrei": 21, "lightsteelblu": 21, "lightsteelblue1": 21, "lightsteelblue2": 21, "lightsteelblue3": 21, "lightsteelblue4": 21, "lightweight": [50, 84], "lightyellow": 21, "lightyellow1": 21, "lightyellow2": 21, "lightyellow3": 21, "lightyellow4": 21, "like": [1, 9, 15, 18, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 38, 39, 40, 42, 44, 45, 47, 50, 51, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 70, 72, 73, 74, 84, 85, 86, 87, 88, 89], "lime": 21, "limegreen": 21, "limit": [15, 18, 19, 23, 24, 25, 29, 31, 32, 38, 40, 51, 52, 53, 54, 60, 63, 64, 65, 73], "line": [15, 24, 26, 28, 30, 31, 32, 45, 48, 53, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 84, 87, 89], "line_height": 32, "line_spac": 29, "linear": [20, 23, 36, 50, 65], "linearli": [36, 51], "linen": 21, "link": [28, 29, 31, 38, 44], "linux": [18, 23, 37, 40, 44, 57, 63, 84], "liquid": [26, 62], "list": [15, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 37, 41, 44, 45, 46, 50, 51, 53, 58, 59, 64, 65, 68, 70, 84], "list_camera": [18, 57], "list_mod": [23, 59], "list_of_double_tupl": 45, "list_of_list": 45, "list_of_object_with_callable_rect": 45, "list_of_object_with_rect_attribut": 45, "list_of_rect": 45, "list_of_tupl": 45, "listen": 39, "littl": [17, 22, 26, 44, 50, 54, 58, 59, 61, 62, 64, 65, 68, 84, 87, 88], "live": [15, 26, 65], "ll": [22, 36, 58, 61, 62, 63, 64, 65, 84, 85, 87, 88, 89], "load": [7, 15, 18, 22, 26, 30, 31, 40, 50, 59, 61, 62, 63, 65, 66, 75, 76, 77, 78, 79, 80, 81, 84, 87, 89], "load_background_imag": 62, "load_bas": 31, "load_extend": 31, "load_imag": [58, 66], "load_player_imag": 62, "load_png": [86, 87, 88, 89], "load_sound": [58, 66, 86], "load_xbm": 22, "local": [8, 15, 25, 33, 34, 38, 39, 44, 57, 58, 60, 61, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85, 86, 89], "localechang": 25, "locals\ub85c\ubd80\ud130": 78, "locat": [25, 26, 34, 35, 39, 41, 58, 68, 69, 70, 71, 72, 73, 84], "lock": [12, 17, 24, 25, 27, 33, 42, 43, 50, 51, 52], "lockobj": 12, "logger": 26, "logic": [48, 58, 61, 63, 65, 68, 69, 70, 71, 84, 86], "logical_s": 48, "logo": [15, 23], "loki": 63, "long": [6, 25, 27, 29, 35, 38, 40, 44, 50, 53, 63, 64, 84, 88], "longer": [19, 28, 32, 38, 47, 87, 88], "longest": [38, 84], "look": [22, 24, 28, 36, 37, 44, 46, 51, 52, 56, 57, 58, 62, 63, 64, 65, 68, 69, 70, 72, 84, 85, 86, 87, 88, 89], "lookout": 26, "lookup": [23, 25, 50, 58, 65], "loop": [15, 24, 25, 32, 38, 40, 54, 57, 61, 62, 63, 64, 66, 68, 84, 86, 87, 88, 89], "lose": [19, 23, 25, 33, 56], "loss": [18, 23, 58, 59, 84], "lost": [25, 40, 46, 62], "lostsprit": 64, "lot": [26, 32, 51, 54, 56, 58, 59, 61, 62, 63, 64, 65, 67, 73, 84, 85, 87, 89], "loud": 38, "love": 84, "low": [25, 32, 63, 67], "low_frequ": [32, 47], "lower": [19, 23, 32, 36, 37, 38, 41, 52, 84], "lowercas": 28, "lowest": 37, "lowli": 84, "lt": 32, "luck": 65, "luckili": 84, "luma": 18, "luminos": [20, 56], "m": [15, 20, 26, 32, 33, 37, 39, 47, 53, 58, 65], "mac": [18, 23, 26, 37, 40, 44, 46, 63, 84], "machin": [26, 56, 64, 65], "maco": 22, "macro": [3, 4, 5, 6, 7, 8, 11, 13], "made": [5, 6, 11, 15, 23, 25, 49, 62, 71, 73, 74, 84, 87], "magazin": [15, 71], "magenta": 21, "magenta1": 21, "magenta2": 21, "magenta3": 21, "magenta4": 21, "magic": [62, 84, 87], "magnifi": 26, "magnitud": 36, "magnitude_squar": 36, "mai": [1, 2, 18, 19, 20, 22, 23, 25, 27, 28, 29, 30, 31, 32, 33, 36, 37, 38, 40, 43, 44, 45, 46, 51, 53, 56, 57, 59, 62, 63, 64, 65, 84], "mail": [62, 84], "main": [18, 25, 26, 27, 32, 39, 41, 46, 50, 53, 56, 57, 61, 62, 64, 65, 66, 71, 72, 73, 79, 80, 81, 85, 86, 87, 88, 89], "main_dir": [58, 66], "mainli": [28, 51, 62, 64, 65], "maintain": [28, 44, 50, 61, 62, 84], "main\ud568\uc218\ub97c": 79, "main\ud568\uc218\uc5d0": 79, "major": [13, 23, 28, 31, 38, 44, 63], "make": [0, 15, 20, 22, 24, 25, 26, 27, 31, 33, 38, 42, 44, 45, 50, 51, 52, 54, 56, 57, 58, 59, 60, 63, 64, 65, 67, 68, 70, 71, 72, 73, 74, 84, 85, 86, 87, 88, 89], "make_sound": [38, 49], "make_surfac": [42, 43, 52], "maker": [67, 74], "malform": 29, "man": 67, "manag": [15, 19, 25, 32, 38, 42, 50, 51, 59, 63, 64], "mani": [19, 23, 24, 25, 28, 29, 33, 36, 38, 39, 40, 44, 45, 50, 51, 54, 56, 58, 59, 62, 63, 84], "manifest": 84, "manipul": [15, 31, 42, 45, 51, 63, 65, 84, 87], "manner": [59, 87], "manual": [19, 28, 29, 32, 41, 44, 51, 57, 64], "map": [17, 20, 24, 42, 43, 47, 51, 52, 59, 65], "map_arrai": [43, 52], "map_rgb": [20, 24, 42, 51, 52], "mapped_int": 51, "margin": [26, 36, 68, 71, 72, 73, 80, 81], "mario": 88, "mark": [33, 42, 57, 63], "maroon": 21, "maroon1": 21, "maroon2": 21, "maroon3": 21, "maroon4": 21, "mask": [22, 23, 26, 29, 42, 50, 51, 59, 84], "maskfil": 22, "mass": 35, "master": [65, 67], "match": [17, 23, 28, 29, 31, 32, 33, 37, 38, 42, 43, 51, 52, 56, 58, 59, 62, 63, 64, 65, 68], "match_font": 28, "materi": [18, 84], "math": [24, 30, 35, 36, 86, 87, 89], "mathemat": [51, 61, 65], "matter": [31, 51, 62, 64, 84, 87], "max": [24, 32, 35, 36, 57, 71], "max_i": 29, "max_length": 36, "max_level": 36, "max_x": 29, "maxhp": [71, 72, 73, 79, 80, 81], "maxi": 28, "maxim": [25, 48], "maximum": [23, 28, 29], "maxtim": 38, "maxx": 28, "mayb": [62, 64, 70, 84, 86], "mb": 16, "me": [59, 63, 65, 84], "mean": [15, 20, 22, 23, 24, 29, 32, 33, 36, 37, 38, 39, 40, 43, 47, 48, 50, 56, 57, 58, 59, 62, 63, 64, 68, 69, 70, 71, 72, 84, 88], "meant": [44, 48, 64], "measur": [24, 37, 84, 87], "mechanim": 67, "mechanin": 75, "mediev": 63, "medium": [24, 32], "mediumaquamarin": 21, "mediumblu": 21, "mediumorchid": 21, "mediumorchid1": 21, "mediumorchid2": 21, "mediumorchid3": 21, "mediumorchid4": 21, "mediumpurpl": 21, "mediumpurple1": 21, "mediumpurple2": 21, "mediumpurple3": 21, "mediumpurple4": 21, "mediumseagreen": 21, "mediumslateblu": 21, "mediumspringgreen": 21, "mediumturquois": 21, "mediumvioletr": 21, "meet": [45, 84], "mega_jc": 16, "megabyt": 23, "member": [25, 50, 59, 64, 68, 84], "membership": [50, 64], "memori": [9, 17, 23, 24, 25, 29, 31, 38, 51, 52, 62, 67], "memoryview": 31, "mental": 74, "mention": [54, 58, 62, 64, 68, 70, 84], "menu": [23, 33, 61], "merchant": 18, "mercuri": 44, "mere": [37, 84], "merg": 64, "merrili": 89, "mess": 15, "messag": [25, 27, 37, 44, 53, 58, 84], "messi": 64, "messier": 84, "met": [18, 22], "meta": [25, 33], "method": [8, 9, 11, 14, 15, 17, 19, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 35, 36, 38, 39, 40, 42, 44, 45, 46, 49, 50, 51, 52, 54, 58, 59, 61, 62, 63, 64, 65, 71, 73, 84, 85, 86, 87, 89], "metric": [28, 29], "mice": 39, "micro": 44, "microsoft": 23, "midbottom": 45, "middl": [39, 47, 57, 62, 65], "midi": [25, 26], "midi_ev": 37, "midi_event_list": 37, "midi_not": 37, "midi_output": 37, "midi_to_ansi_not": 37, "midi_to_frequ": 37, "midiexcept": 37, "midiin": [25, 37], "midiout": [25, 37], "midis2ev": 37, "midisport": 37, "midleft": [45, 88, 89], "midnightblu": 21, "midpoint": 36, "midright": [45, 88, 89], "midtop": 45, "might": [20, 23, 25, 26, 29, 36, 44, 46, 60, 61, 62, 64, 65, 84, 86, 87, 89], "mighti": 62, "migrationguid": 39, "milli": 54, "millisecond": [25, 33, 37, 38, 40, 50, 54, 63], "mime": 46, "mimic": 29, "min": [24, 36, 57], "min_alpha": 51, "min_i": 29, "min_length": 36, "min_x": 29, "mind": [39, 54, 60, 65, 84], "minhp": 73, "mini": 28, "miniatur": 32, "minim": [23, 25, 48, 64], "minimum": [23, 24, 28, 30, 31, 35, 51], "minor": [13, 28, 31, 38, 44, 63], "mintcream": 21, "minu": 33, "minx": 28, "mirror": [23, 58], "miss": [58, 66, 84], "missingmodul": [49, 52], "mistyros": 21, "mistyrose1": 21, "mistyrose2": 21, "mistyrose3": 21, "mistyrose4": 21, "misunderstand": 84, "misunderstood": 84, "miter": 24, "mix": [28, 38, 51, 63, 65, 84], "mix_chunk": 7, "mix_setmusicposit": 40, "mixer": [0, 15, 26, 38, 44, 49, 58, 66], "mizuno": 18, "mmsystem": 37, "mmx": [26, 56], "moccasin": 21, "mod": [25, 33, 34, 40, 67], "mod_": 34, "modal": 48, "mode": [15, 20, 23, 25, 26, 28, 29, 30, 33, 38, 39, 44, 46, 48, 50, 51, 53, 58, 63, 65], "mode_ok": [23, 59], "model": 51, "modern": 84, "modest": 84, "modif": [12, 18, 51, 86], "modifi": [25, 29, 30, 33, 34, 37, 39, 45, 48, 49, 50, 51, 64, 65, 85], "modnam": 1, "modul": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 17, 21, 26, 34, 41, 42, 48, 51, 53, 59, 60, 61, 62, 65, 66, 68, 84, 87, 89], "modulu": 20, "moment": [46, 49, 84, 87], "momma": 62, "monitor": [23, 34, 59, 63, 68], "monkei": [58, 63, 66], "mono": [29, 38, 49], "monochrom": 29, "monster": 64, "month": 63, "more": [15, 18, 19, 20, 23, 24, 25, 26, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 42, 43, 44, 47, 50, 51, 54, 57, 58, 59, 60, 61, 63, 64, 70, 71, 73, 74, 84, 85, 86, 87, 88, 89], "most": [15, 19, 23, 24, 26, 28, 29, 31, 38, 39, 40, 44, 45, 50, 51, 54, 57, 59, 60, 61, 63, 64, 65, 74, 84, 85], "mostli": 70, "motion": [32, 39, 48, 63, 87, 89], "motiv": 70, "mous": [15, 22, 23, 25, 26, 32, 46, 48, 58, 61, 62, 63, 66, 67, 70, 72, 84, 85, 88], "mousebuttondown": [22, 25, 27, 39, 58, 66, 84], "mousebuttonup": [25, 39, 58, 66, 72, 73, 80, 81, 84], "mousebuttonup\uc774": 80, "mousemot": [25, 39], "mousewheel": [25, 39], "movabl": [62, 88, 89], "move": [15, 25, 26, 36, 39, 44, 45, 50, 51, 56, 58, 61, 63, 65, 66, 69, 70, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "move_and_draw_all_game_object": 62, "move_ip": [45, 58, 66], "move_to_back": 50, "move_to_front": 50, "move_toward": 36, "move_towards_ip": 36, "movedown": [70, 78, 88, 89], "moveit": [26, 62], "movement": [32, 39, 50, 58, 87, 88], "movepo": [88, 89], "moveright": [69, 70, 77, 78], "moveup": [69, 77, 88, 89], "movi": 74, "mozart": 40, "mp3": 40, "msg": 37, "msmf": 18, "much": [18, 24, 26, 28, 31, 54, 56, 58, 59, 60, 61, 62, 63, 65, 67, 68, 71, 73, 74, 84, 85, 87, 89], "multi_thread": 53, "multicolor": 26, "multidimension": 65, "multigestur": 25, "multimedia": [33, 63, 84], "multipl": [1, 6, 18, 19, 22, 23, 24, 25, 26, 28, 32, 33, 36, 38, 41, 44, 45, 50, 51, 56, 57, 61, 63, 64, 65, 69], "multipli": [20, 24, 29, 33, 36, 42, 45, 48, 51, 56, 65], "multisampl": 23, "multithread": 27, "music": [7, 15, 26, 37, 38, 40, 63, 74, 86], "must": [1, 2, 3, 17, 18, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 56, 58, 59, 60, 63, 64, 65, 68, 72, 84], "mustlock": 51, "mutabl": 25, "mute": 38, "my": [26, 61, 62, 63, 65, 84, 86, 89], "my_appl": 44, "my_data_typ": 46, "mygroup": 64, "myscreen": [68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "mysprit": 64, "mysurf": 51, "mytext": [68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "mytextarea": [68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "mytextarea\ub294": 76, "mytextarea\ub77c\ub294": 76, "mytextfont": [68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "mytextfont\uac1d\uccb4\uc758": 76, "mytextfont\ub77c\ub294": 76, "mytext\uac1d\uccb4\uc758": 76, "mytext\ub77c\ub294": 76, "n": [28, 33, 38, 65], "name": [1, 9, 18, 19, 20, 23, 25, 26, 28, 29, 32, 33, 36, 37, 44, 45, 47, 50, 51, 53, 58, 59, 61, 62, 63, 64, 65, 66, 68, 70, 78, 86, 88, 89], "name_forindex": 47, "name_of_environment_vari": 44, "namehint": [31, 40], "namespac": [34, 58, 60], "nano": 31, "nasti": 86, "nativ": [15, 18, 22, 44, 57, 84], "natur": [40, 65, 74, 84, 86], "navajowhit": 21, "navajowhite1": 21, "navajowhite2": 21, "navajowhite3": 21, "navajowhite4": 21, "navi": 21, "navyblu": 21, "ndigit": 36, "ndim": 42, "nearest": [24, 38], "nearli": [36, 64, 67], "neat": 84, "neater": 88, "nebul": 84, "necessari": [9, 23, 25, 27, 29, 38, 50, 51, 60, 61, 64, 68, 84, 85, 87, 88, 89], "necessarili": 63, "need": [10, 15, 18, 19, 20, 22, 23, 25, 26, 27, 28, 31, 32, 33, 35, 42, 44, 45, 46, 50, 51, 53, 57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 85, 86, 87, 88, 89], "needless": [68, 71, 73, 84], "needn": [26, 61, 89], "neg": [8, 9, 17, 25, 29, 35, 36, 38, 39, 40, 42, 45, 51, 56, 65, 89], "neglig": 18, "neither": [50, 69, 70], "nest": 51, "network": [61, 86], "never": [29, 32, 36, 38, 39, 40, 51, 54, 57, 63, 65, 84], "new": [2, 3, 4, 5, 6, 7, 8, 11, 12, 13, 17, 18, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 58, 59, 61, 62, 63, 64, 65, 69, 71, 72, 73, 74, 84, 85, 86, 87, 89], "new_height": 26, "new_lay": 50, "new_mask": 35, "new_width": 26, "newarrai": 42, "newbi": 15, "newcom": 84, "newer": [23, 40], "newest": 68, "newli": [20, 35], "newlin": 28, "newpo": [58, 66, 87, 88, 89], "newrect": 64, "newtonian": 63, "next": [15, 18, 27, 29, 30, 35, 36, 38, 41, 46, 58, 64, 68, 73, 84, 85, 88, 89], "nice": [31, 58, 61, 62, 65, 84, 87], "nirav": 57, "node": 44, "noevent": [25, 27, 38, 40, 84], "nofram": 23, "nois": [32, 35, 57], "nomin": 29, "non": [17, 19, 23, 24, 26, 29, 31, 33, 41, 42, 44, 50, 51, 56, 58, 64, 84, 86], "none": [17, 18, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 56, 58, 60, 64, 65, 66, 85, 86, 87, 89], "nonesound": [58, 66], "nonetheless": 26, "nonetyp": [30, 35, 50], "nonlinear": 51, "nonzero": [35, 45], "nor": [50, 70], "normal": [8, 11, 19, 20, 25, 28, 29, 35, 36, 44, 45, 50, 51, 53, 55, 58, 64, 65, 71, 87, 89], "normalize_ip": 36, "north": 22, "northeast": 22, "northwest": 22, "nosubprocess": 53, "notabl": [23, 40, 87], "notat": 39, "notdef": 29, "note": [18, 19, 20, 23, 24, 25, 26, 27, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 44, 45, 47, 51, 54, 56, 57, 58, 59, 60, 62, 64, 66, 84, 86, 87, 89], "note1": 35, "note2": 35, "note_off": 37, "note_on": 37, "noteworthi": 63, "noth": [19, 23, 24, 30, 32, 50, 58, 59, 63, 64, 84, 85, 86], "notic": [18, 36, 45, 62, 68, 69, 70, 72, 84, 87, 88, 89], "notifi": 64, "notimplementederror": [31, 40], "novel": 74, "now": [23, 24, 26, 27, 28, 29, 32, 33, 35, 36, 37, 38, 43, 44, 46, 56, 57, 58, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 84, 85, 87, 89], "nowadai": [63, 84], "nrp": 57, "nuanc": 84, "null": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 14, 28], "num": [33, 72, 73, 80, 81], "num_button": 39, "num_devic": 37, "num_ev": 37, "num_fing": 25, "num_threshold_pixel": 56, "num_track": 19, "number": [1, 7, 10, 17, 18, 19, 20, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 40, 42, 44, 47, 49, 50, 51, 52, 53, 54, 55, 56, 62, 63, 64, 65, 69, 73, 74, 84], "numbit": 35, "numer": [36, 65], "numev": [25, 38], "numfail": 44, "numlock": 33, "numpass": 44, "numpi": [1, 15, 26, 31, 38, 42, 43, 49, 51, 52, 63], "o": [6, 9, 18, 22, 23, 25, 26, 31, 32, 33, 37, 44, 45, 46, 58, 62, 66, 84, 86, 89], "obj": [1, 2, 3, 4, 7, 8, 9, 44, 45], "obj_list": 45, "object": [1, 2, 3, 4, 5, 6, 8, 9, 11, 12, 15, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 38, 39, 40, 43, 44, 46, 47, 48, 49, 52, 54, 56, 57, 59, 61, 62, 63, 64, 65, 66, 68, 69, 70, 84, 86, 89], "objectwithcallablerectattribut": 45, "objectwithrectattribut": 45, "objectwithsomrectattribut": 45, "obliqu": 29, "obscur": [61, 84], "obsolet": [23, 44, 51], "obtain": [29, 37, 51], "obviou": 61, "obvious": [64, 85], "occasion": 64, "occupi": 85, "occur": [25, 29, 33, 37, 38, 42, 62, 68], "octob": 63, "odd": [24, 65, 89], "ofcod": 89, "off": [18, 24, 25, 26, 29, 32, 37, 38, 40, 45, 56, 58, 61, 62, 64, 84, 89], "offcourt": 89, "offer": [22, 23, 25, 47, 57], "offici": [62, 84], "offset": [17, 24, 26, 28, 29, 30, 35, 37, 40, 42, 45, 50, 51, 58], "often": [19, 31, 36, 51, 61, 63, 65, 69, 84, 86], "ogg": [38, 40], "oh": 89, "ok": [65, 84, 85, 87], "okai": [69, 72], "old": [23, 39, 44, 50, 51, 54, 58, 62, 64, 74, 84, 88], "older": [23, 25, 27, 32, 40, 44, 59, 64, 84], "oldest": 68, "oldlac": 21, "oliv": 21, "olivedrab": 21, "olivedrab1": 21, "olivedrab2": 21, "olivedrab3": 21, "olivedrab4": 21, "omit": [29, 37, 42, 53], "onc": [15, 18, 19, 23, 25, 28, 29, 31, 32, 33, 35, 37, 38, 40, 42, 44, 47, 51, 53, 54, 58, 60, 62, 63, 64, 65, 84, 85, 86, 87, 89], "one": [15, 17, 18, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 38, 39, 40, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 73, 84, 85, 86, 87, 88, 89], "ones": [29, 40, 51, 60, 64, 87, 88], "onli": [17, 18, 19, 20, 23, 24, 25, 28, 29, 31, 32, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 72, 73, 74, 84, 85, 86, 87, 89], "onlin": 84, "ons": 63, "onscreen": 23, "onto": [11, 20, 27, 28, 29, 30, 35, 36, 50, 51, 58, 62, 63, 65, 68, 84, 85, 87], "opac": [48, 84], "opaqu": [20, 26, 29, 35, 43, 48, 51, 52, 65, 84], "open": [1, 6, 9, 15, 18, 19, 23, 32, 33, 37, 44, 46, 47, 57, 58, 63, 85, 86], "opencv": [18, 44, 57], "opengl": [23, 26], "opentyp": 29, "oper": [14, 20, 22, 23, 24, 25, 27, 33, 36, 42, 44, 45, 48, 50, 51, 52, 56, 64, 65, 84, 85], "operand": 65, "oppos": 29, "opposit": 25, "optim": [23, 26, 28, 51, 56, 64, 65, 84], "optimis": 84, "option": [1, 17, 18, 20, 23, 24, 26, 28, 29, 30, 32, 35, 37, 38, 39, 40, 43, 44, 50, 51, 53, 54, 56, 57, 58, 60, 62, 65], "orang": 21, "orange1": 21, "orange2": 21, "orange3": 21, "orange4": 21, "orangered1": 21, "orangered2": 21, "orangered3": 21, "orangered4": 21, "orchid": 21, "orchid1": 21, "orchid2": 21, "orchid3": 21, "orchid4": 21, "order": [1, 18, 23, 28, 29, 33, 37, 38, 40, 42, 44, 50, 51, 53, 54, 58, 62, 65, 68, 84, 89], "orderedupd": 50, "ordinari": 64, "org": [39, 58, 59, 60, 62, 63, 64, 65, 70, 78, 86, 89], "organ": [15, 50, 64, 84], "organis": 61, "organiz": 84, "orient": [29, 35, 62], "origin": [20, 22, 23, 24, 26, 29, 31, 32, 33, 35, 42, 45, 47, 48, 50, 51, 56, 58, 62, 63, 64, 65, 66], "original_color": 56, "original_dest_color": 56, "orthogon": 35, "oss": 37, "osx": [23, 63], "other": [0, 15, 18, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 38, 40, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 68, 70, 71, 84, 85, 86, 87, 88, 89], "otherarrai": 42, "othermask": 35, "othersurfac": 35, "otherwis": [1, 2, 5, 9, 12, 17, 18, 22, 23, 24, 25, 26, 28, 29, 31, 35, 36, 37, 38, 39, 43, 45, 46, 50, 51, 52, 56, 62, 72, 73, 89], "ouput": 76, "our": [58, 62, 63, 65, 66, 74, 85], "out": [15, 18, 24, 29, 32, 35, 36, 37, 38, 40, 44, 57, 58, 59, 61, 62, 64, 65, 84, 85, 86, 87, 88, 89], "outdat": 67, "outer": 72, "outgo": 37, "outlin": [24, 29, 35, 57], "outpng": 26, "output": [18, 25, 26, 28, 32, 35, 56, 67, 69, 70, 73, 74, 84, 88], "outputimag": 26, "outsid": [24, 28, 30, 33, 35, 36, 39, 45, 51, 56, 58, 62, 63, 68], "over": [22, 23, 25, 29, 31, 33, 35, 36, 38, 40, 42, 44, 47, 50, 57, 60, 62, 63, 64, 65, 66, 84, 86], "overal": 65, "overboard": 86, "overcom": 67, "overflow": 65, "overhead": [51, 64], "overlai": 64, "overlap": [35, 45, 50, 62, 64, 84, 87, 89], "overlap_area": 35, "overlap_mask": 35, "overlin": 29, "overrid": [18, 29, 35, 44, 50, 51, 63, 84], "overridden": [23, 29, 35, 38, 45], "overshoot": 36, "overwrit": [17, 24, 26, 32, 47], "overwritten": [24, 32, 47, 51], "own": [12, 16, 22, 25, 26, 27, 37, 38, 46, 48, 59, 61, 63, 65, 84, 86], "own_data_typ": 46, "owner": [11, 12], "ownership": 46, "p": [26, 31, 32, 33, 43, 62], "p1": 48, "p2": 48, "pac": 64, "pack": [23, 51, 52, 59], "packag": [15, 26, 28, 29, 30, 31, 49, 52, 58, 60, 63, 65], "pacman": 64, "pad": [29, 32, 56], "page": [33, 39, 46, 65], "pai": 70, "painless": 86, "paint": [50, 85], "pair": [12, 23, 24, 28, 29, 31, 32, 45, 51], "pajitnov": 74, "palegoldenrod": 21, "palegreen": 21, "palegreen1": 21, "palegreen2": 21, "palegreen3": 21, "palegreen4": 21, "palett": [23, 28, 31, 35, 51, 52, 56], "palette_color": [35, 56], "paleturquois": 21, "paleturquoise1": 21, "paleturquoise2": 21, "paleturquoise3": 21, "paleturquoise4": 21, "palevioletr": 21, "palevioletred1": 21, "palevioletred2": 21, "palevioletred3": 21, "palevioletred4": 21, "papayawhip": 21, "paper": 84, "paradigm": 84, "param": 24, "paramet": [18, 20, 23, 24, 25, 26, 29, 30, 31, 33, 35, 36, 37, 38, 39, 40, 44, 45, 46, 51, 55, 56, 57, 70, 71], "parametr": 36, "parent": [2, 12, 17, 48, 50, 51], "parenthesi": 33, "pars": [84, 87], "part": [23, 24, 25, 29, 30, 35, 51, 53, 56, 62, 63, 64, 65, 68, 69, 70, 72, 86], "parti": [18, 63], "partial": [23, 24, 37, 51, 84], "particular": [18, 29, 40, 42, 49, 50, 53, 54, 59, 84], "particularli": 37, "pass": [8, 9, 14, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 44, 46, 50, 51, 53, 54, 56, 58, 59, 62, 63, 64, 66, 84, 86], "past": [24, 36, 46, 58, 63, 84], "patch": [13, 26, 28, 31, 38, 44, 63], "patel": 57, "path": [6, 9, 15, 25, 26, 28, 29, 31, 36, 38, 44, 53, 57, 58, 66, 84, 86, 89], "pathlib": [28, 29, 31, 38], "pathlib_path": 38, "pathnam": [38, 58, 86], "pattern": [29, 40], "paus": [19, 26, 33, 38, 40, 54, 86], "pbm": [31, 46], "pc": [17, 37], "pcf": 29, "pci": 65, "pcx": 31, "peachpuff": 21, "peachpuff1": 21, "peachpuff2": 21, "peachpuff3": 21, "peachpuff4": 21, "peek": 25, "pellet": 64, "penalti": [51, 59, 64], "pend": 37, "peopl": [15, 31, 58, 61, 62, 63], "per": [15, 24, 25, 29, 30, 31, 32, 35, 49, 50, 51, 52, 53, 54, 58, 59, 62, 63, 65, 69, 77, 84, 88, 89], "percentag": 36, "perfect": 35, "perform": [11, 23, 25, 26, 28, 29, 30, 35, 36, 38, 42, 50, 51, 52, 56, 57, 59, 64, 65, 84, 85, 88], "perhap": [62, 64, 84, 86], "period": [33, 40, 84], "permiss": 23, "permit": [14, 18, 29], "person": [62, 63, 88], "perspect": [26, 63], "peru": 21, "pete": [58, 59, 60, 62, 63, 64, 65, 84], "pfr": 29, "pg": [22, 58, 66], "pg_buffer": [1, 2], "pg_encodefilepath": 9, "pg_encodestr": 9, "pg_floatfromobj": 1, "pg_floatfromobjindex": 1, "pg_getdefaultwindow": 1, "pg_getdefaultwindowsurfac": 1, "pg_intfromobj": 1, "pg_intfromobjindex": 1, "pg_major_vers": 13, "pg_minor_vers": 13, "pg_mod_autoinit": 1, "pg_mod_autoquit": 1, "pg_patch_vers": 13, "pg_registerquit": 1, "pg_rgbafromobj": [1, 10], "pg_setdefaultwindow": 1, "pg_setdefaultwindowsurfac": 1, "pg_twofloatsfromobj": 1, "pg_twointsfromobj": 1, "pg_uintfromobj": 1, "pg_uintfromobjindex": 1, "pg_version_atleast": 13, "pg_versionnum": 13, "pg_view_p": 1, "pgbuffer_asarrayinterfac": 1, "pgbuffer_asarraystruct": 1, "pgbuffer_releas": 1, "pgbufproxy_check": 2, "pgbufproxy_getpar": 2, "pgbufproxy_new": 2, "pgbufproxy_trip": 2, "pgbufproxy_typ": 2, "pgchannel_asint": 7, "pgchannel_check": 7, "pgchannel_new": 7, "pgchannel_typ": 7, "pgchannelobject": 7, "pgcolor_check": 3, "pgcolor_new": 3, "pgcolor_newlength": 3, "pgcolor_typ": 3, "pgdict_asbuff": 1, "pgevent_check": 5, "pgevent_filluserev": 5, "pgevent_new": 5, "pgevent_new2": 5, "pgevent_typ": 5, "pgeventobject": 5, "pgexc_buffererror": 1, "pgexc_sdlerror": 1, "pgfont_check": 6, "pgfont_is_al": 6, "pgfont_new": 6, "pgfont_typ": 6, "pgfontobject": 6, "pglifetimelock_check": 12, "pglifetimelock_typ": 12, "pglifetimelockobject": 12, "pgm": 31, "pgobject_getbuff": 1, "pgrect_asrect": 8, "pgrect_check": 8, "pgrect_fromobject": 8, "pgrect_new": 8, "pgrect_new4": 8, "pgrect_norm": 8, "pgrect_typ": 8, "pgrectobject": 8, "pgrwops_fromfileobject": 9, "pgrwops_fromobject": 9, "pgrwops_isfileobject": 9, "pgrwops_releaseobject": 9, "pgsound_aschunk": 7, "pgsound_check": 7, "pgsound_new": 7, "pgsound_typ": 7, "pgsoundobject": 7, "pgsurface_assurfac": 11, "pgsurface_blit": 11, "pgsurface_check": 11, "pgsurface_lock": 12, "pgsurface_lockbi": 12, "pgsurface_locklifetim": 12, "pgsurface_new": 11, "pgsurface_new2": 11, "pgsurface_prep": 12, "pgsurface_typ": 11, "pgsurface_unlock": 12, "pgsurface_unlockbi": 12, "pgsurface_unprep": 12, "pgsurfaceobject": [1, 11, 12], "pgvidinfo_asvidinfo": 4, "pgvidinfo_check": 4, "pgvidinfo_new": 4, "pgvidinfo_typ": 4, "pgvidinfoobject": 4, "phase": [69, 70], "phi": 36, "photo": 44, "photograph": 56, "photoshop": 16, "physic": [15, 17, 23, 25, 32, 61, 63, 86, 88, 89], "pi": [24, 89], "pick": [23, 44, 56, 59, 62, 64], "pictur": [31, 63], "pie": 30, "piec": [29, 84], "pil": 31, "pile": 26, "pinch": 25, "pink": 21, "pink1": 21, "pink2": 21, "pink3": 21, "pink4": 21, "pip": 15, "pip3": 15, "pipe": 23, "pitch": [37, 51, 62], "pitch_bend": 37, "pixel": [15, 17, 18, 20, 22, 23, 24, 26, 28, 29, 30, 31, 35, 41, 45, 48, 51, 56, 58, 59, 63, 65, 85, 88, 89], "pixel2d": 65, "pixel3d": 65, "pixel_arrai": 42, "pixelarrai": [15, 26, 42, 51], "pixelcopi": [26, 42, 43, 52], "pixelformat": 18, "pixels2d": [52, 65], "pixels3d": [52, 65], "pixels_alpha": [52, 65], "pixels_blu": 52, "pixels_green": 52, "pixels_r": 52, "pixels_within_threshold": 56, "place": [1, 23, 25, 27, 29, 34, 36, 37, 39, 42, 44, 45, 46, 50, 51, 52, 53, 58, 62, 63, 64, 65, 68, 84, 87, 88], "placehold": 44, "placement": 44, "plai": [7, 15, 19, 23, 26, 32, 36, 37, 40, 47, 58, 62, 63, 64, 66, 70, 74, 84, 86], "plain": 46, "plan": [51, 63], "plane": [41, 51], "plant": 65, "plateau": 84, "platform": [15, 18, 23, 24, 25, 30, 33, 35, 37, 38, 41, 44, 46, 51, 53, 54, 57, 58, 59, 63, 84, 86, 88], "playabl": 49, "playback": [19, 26, 38, 40, 63], "player": [26, 50, 58, 61, 62, 64, 68, 70, 73, 74, 84, 88, 89], "player1": [61, 89], "player2": 89, "player_po": 15, "playerimag": 62, "playerpo": 62, "playersprit": 89, "playmu": 26, "play\ub77c\ub294": 78, "play\ud55c\ub2e4": 78, "pleas": [16, 23, 28, 33, 36, 44, 45, 51, 56, 84], "plenti": [61, 84], "plot": 61, "plu": [28, 29, 33, 37, 44, 64], "plug": [25, 32], "plum": 21, "plum1": 21, "plum2": 21, "plum3": 21, "plum4": 21, "pm_recommended_input_devic": 37, "pm_recommended_output_devic": 37, "pmdeviceid": 37, "png": [16, 26, 31, 58, 63, 65, 66, 75, 76, 77, 78, 84, 86, 87, 88, 89], "pnm": 31, "po": [25, 27, 35, 40, 50, 58, 62, 66, 72, 73, 80, 81], "point": [14, 17, 19, 20, 22, 24, 26, 29, 30, 35, 36, 37, 45, 48, 50, 56, 57, 62, 63, 64, 65, 67, 72, 84, 89], "pointer": [8, 9, 11, 58], "polar": 36, "polish": [22, 63], "poll": [15, 25, 27, 37, 84], "polygon": [24, 30], "pong": [61, 86, 89], "poor": 61, "poorli": [61, 84], "pop": 44, "popul": 9, "popular": [26, 32, 66], "port": [18, 26, 37, 44, 48], "portabl": [33, 37, 63], "portion": [11, 23, 45, 48, 51, 56, 63, 64, 84], "portmidi": 37, "posit": [1, 8, 17, 19, 22, 23, 24, 25, 26, 28, 29, 30, 32, 33, 35, 38, 39, 40, 44, 45, 48, 50, 51, 53, 55, 57, 58, 62, 63, 64, 66, 68, 69, 84, 85, 87, 88, 89], "possibl": [18, 23, 25, 28, 29, 31, 32, 36, 44, 50, 51, 57, 58, 63, 65, 67, 68], "possibli": 61, "post": [25, 27, 38, 47, 54], "pos\ub294": 80, "potenti": [31, 64, 84, 88], "powderblu": 21, "power": [18, 32, 33, 38, 64, 74, 84], "power_level": 32, "ppem": 29, "ppm": [31, 46], "pr": 39, "practic": [84, 86], "pre": [20, 29, 51], "pre_init": 38, "prealloc": 51, "prebuilt": 15, "preced": [35, 68], "precis": [23, 38, 51, 65, 84], "precise_i": 25, "precise_x": 25, "precisei": 25, "precisex": 25, "predecessor": 84, "predefin": [25, 46], "predomin": 43, "prefer": [23, 25, 30, 31, 33], "prefix": 58, "prematur": 84, "premul_alpha": [20, 51], "prepar": [26, 40, 66], "present": [44, 48, 50, 60, 62, 63, 64, 84], "preserv": [20, 44, 45, 51], "preset": [22, 38], "press": [25, 26, 32, 33, 39, 47, 57, 58, 62, 70, 71, 84], "pressur": 55, "pretend": 62, "pretti": [59, 62, 64, 65, 84, 85, 88, 89], "prettier": 33, "prevar": 84, "prevent": [25, 84, 89], "prevent_display_stretch": 23, "previou": [1, 23, 29, 38, 39, 48, 50, 54, 56, 58, 62, 64, 65, 69, 70, 72, 73, 84, 85, 89], "previous": [18, 20, 25, 28, 29, 31, 38, 40, 44, 59, 62, 85], "primari": 39, "primarili": [25, 29, 44, 48], "prime": 48, "primer": 65, "primit": 30, "principl": [88, 89], "print": [25, 28, 32, 33, 36, 39, 44, 45, 46, 57, 58, 59, 62, 66, 68, 70, 71, 72, 73, 76, 84, 86, 89], "printboard": [73, 81], "printf": [67, 75], "print\ud568\uc218\ub098": 76, "prior": [23, 59], "prioriti": 18, "privat": [51, 58], "probabl": [26, 36, 62, 64, 68, 84, 85], "problem": [28, 43, 44, 58, 61, 69, 84, 86], "proce": 84, "procedur": [67, 68], "process": [18, 25, 26, 27, 32, 46, 52, 53, 54, 60, 62, 67, 68, 70, 72, 73, 77, 84], "processor": [54, 56], "procur": 18, "produc": [29, 51, 84], "product": [36, 61], "profil": [23, 25, 84], "profit": 18, "program": [15, 19, 23, 25, 27, 32, 33, 37, 38, 41, 44, 45, 53, 54, 58, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 84, 85, 86, 87, 88], "programm": [63, 64, 84, 86, 87], "programmat": 44, "progress": 43, "project": [16, 26, 36, 61, 63, 67, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 86, 87, 89], "prolif": 84, "prolog": 73, "promis": [58, 62], "prompt": [44, 63, 65], "proper": [29, 33, 38, 39, 64, 65], "properli": [9, 15, 25, 39, 53, 58, 62, 64, 65, 84], "properti": [17, 25, 29, 39, 50, 52], "propos": 63, "protect": [26, 58], "protocol": [2, 15, 31, 43, 51], "proud": 62, "provid": [15, 18, 20, 23, 26, 27, 28, 29, 33, 35, 36, 38, 41, 42, 44, 45, 50, 51, 53, 54, 56, 57, 58, 67, 86, 87], "proxi": [2, 17], "ps4": 32, "pseudo": 87, "pt": 56, "public": [26, 86, 89], "pull": [18, 58, 66], "pummel": [58, 66], "pump": [25, 27, 32, 88, 89], "punch": [58, 63, 66], "punch_sound": [58, 66], "punchabl": 58, "punctuat": 28, "pure": [44, 51], "purpl": [15, 21, 84], "purple1": 21, "purple2": 21, "purple3": 21, "purple4": 21, "purpos": [1, 18, 48, 57, 61], "push": [33, 71, 88], "pushabl": 32, "put": [15, 16, 32, 46, 54, 60, 61, 63, 64, 66, 68, 85, 86, 87], "puyopuyo": [67, 75], "pxarrai": 42, "py": [15, 23, 26, 44, 46, 53, 56, 61, 62, 65], "py_buff": [1, 17], "pybuf": 1, "pybuffer_releaseproc": 1, "pycdio": 19, "pygam": [10, 14, 21, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 89], "pygame2": 25, "pygame_blend_add": 11, "pygame_blend_alpha_sdl2": [11, 44], "pygame_blend_max": 11, "pygame_blend_min": 11, "pygame_blend_mult": 11, "pygame_blend_premultipli": 11, "pygame_blend_rgba_add": 11, "pygame_blend_rgba_max": 11, "pygame_blend_rgba_min": 11, "pygame_blend_rgba_mult": 11, "pygame_blend_rgba_sub": 11, "pygame_blend_sub": 11, "pygame_bufferproxi": 2, "pygame_camera": 44, "pygame_displai": 44, "pygame_force_scal": 44, "pygame_freetyp": [6, 28, 44], "pygame_hide_support_prompt": 44, "pygame_lofi": 16, "pygame_logo": 16, "pygame_mix": 7, "pygame_pow": 16, "pygame_powered_lowr": 16, "pygame_tini": 16, "pygameapi_base_numslot": 10, "pygamevers": 44, "pyobject": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12], "pyopengl": [26, 31, 63], "pypi": 42, "pyportmidi": 37, "pysdl": [63, 84], "pythagorean": [36, 69], "python": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 17, 18, 19, 22, 26, 28, 29, 31, 38, 43, 44, 45, 50, 51, 53, 58, 60, 61, 62, 64, 65, 66, 67, 68, 85, 86, 87], "python2": 26, "python26": 26, "python3": 18, "pytypeobject": [2, 3, 4, 7, 8, 11, 12], "pyunicode_asencodedstr": [9, 44], "pyzin": 63, "q": 33, "qce": 18, "quadrant": 24, "quadruplet": [24, 30], "quake3": 63, "qualiti": [26, 44, 58, 63], "quaternion": 67, "queri": [23, 37, 46, 59], "query_imag": [18, 57], "question": [15, 33, 62, 84], "queu": [25, 38, 40], "queue": [5, 18, 23, 32, 33, 34, 37, 38, 39, 40, 47, 54, 58, 63, 84, 88], "quick": [25, 26, 32, 51, 53, 60, 64, 65, 88], "quicker": [50, 51, 56, 64], "quickest": 51, "quickli": [31, 58, 59, 62, 63, 64, 65], "quietli": 25, "quit": [1, 15, 19, 22, 23, 24, 25, 26, 28, 29, 32, 37, 38, 39, 44, 47, 51, 53, 57, 58, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 88, 89], "quit\uac19\uc740": 76, "quit\ub77c\ub294": 76, "quiz": 73, "quot": 33, "quotedbl": 33, "r": [8, 20, 24, 26, 30, 32, 33, 36, 42, 43, 45, 51, 56, 64, 65, 71, 72, 73, 79, 80, 81], "r_margin": [72, 73, 80, 81], "radial": [36, 65], "radian": [24, 30, 36, 87, 89], "radii": [24, 30], "radiu": [24, 30, 50], "radom": 74, "rais": [1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 17, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 33, 35, 36, 37, 38, 40, 43, 44, 45, 46, 48, 49, 50, 51, 52, 56, 57, 59, 60, 65, 84, 86, 89], "ramp": 23, "ran": 85, "rand": 89, "randint": [73, 81, 89], "random": [53, 61, 73, 74, 81, 86, 89], "randomli": 73, "rang": [20, 23, 24, 25, 28, 29, 32, 33, 35, 36, 37, 38, 42, 44, 47, 50, 57, 62, 63, 65, 68, 71, 72, 73, 79, 80, 81], "rank": 84, "rapid": 32, "rapidli": [33, 62], "rare": [36, 44, 51, 84], "rate": [38, 40, 50, 69, 84], "rather": [24, 28, 29, 30, 33, 44, 50, 54, 56, 69, 85, 87], "ratio": [45, 50], "raw": [17, 31, 42, 43, 51, 52, 65], "rb": 46, "re": [22, 26, 29, 36, 44, 56, 59, 61, 62, 64, 71, 84, 85, 87, 88], "reach": [63, 84], "read": [9, 17, 20, 25, 26, 29, 37, 39, 46, 48, 50, 51, 61, 62, 65, 84], "readabl": [20, 38, 68, 86], "readi": [18, 22, 32, 48, 53, 57, 58, 62, 63], "readlin": 22, "readm": 15, "readonli": 50, "real": [25, 28, 29, 37, 38, 51, 57, 59, 62, 64, 70, 84], "realist": [86, 89], "realiti": [59, 84], "realiz": [63, 84], "realli": [23, 41, 56, 58, 59, 62, 63, 64, 65, 70, 85, 87, 88], "realtim": [51, 63, 65], "reason": [23, 27, 28, 29, 38, 39, 51, 56, 57, 61, 62, 64, 84, 87], "rebel": 63, "rebind": 47, "recalcul": 29, "recap": 61, "receiv": [23, 27, 32, 33, 39, 47, 84], "recent": [58, 63, 64, 65], "recogn": [17, 25, 29, 32, 43, 45, 56], "recogniz": 31, "recommend": [25, 27, 28, 31, 50, 51, 62, 84], "recommended_input_devic": 37, "recommended_output_devic": 37, "recompil": 89, "reconstruct": 51, "record": [18, 29, 52, 53, 72, 84], "recreat": [26, 50, 53], "rect": [0, 15, 24, 29, 30, 33, 35, 41, 45, 48, 50, 51, 56, 57, 58, 62, 63, 64, 66, 71, 72, 73, 79, 80, 81, 86, 87, 88, 89], "rect1": 45, "rect2": 45, "rect_area_pt": 56, "rect_list": [45, 50], "rect_sequ": 45, "rectangl": [8, 15, 23, 24, 26, 28, 29, 30, 33, 45, 48, 50, 51, 56, 64, 68, 84, 85, 87, 89], "rectangle_list": 23, "rectangular": [8, 29, 30, 35, 42, 48, 50, 51, 62, 63, 71, 84], "rectstyl": 84, "red": [1, 15, 18, 20, 21, 23, 24, 31, 42, 50, 51, 52, 56, 65, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85], "red1": 21, "red2": 21, "red3": 21, "red4": 21, "redimg": 65, "redistribut": 18, "redraw": [41, 62], "redrawn": [23, 41], "reduc": [29, 38, 51, 64, 88], "reentrant": 53, "ref": [70, 78], "refcount": 9, "refer": [1, 12, 17, 23, 24, 31, 33, 35, 42, 46, 47, 49, 50, 51, 52, 57, 58, 59, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 84, 88], "referenc": [50, 52, 58, 64, 65], "reflect": 36, "reflect_ip": 36, "refresh": 84, "regard": [24, 68], "regardless": [28, 44], "region": [31, 51, 56, 84, 86], "regist": [1, 44, 46, 87], "register_quit": 44, "registri": 37, "regular": [15, 23, 40, 41, 50, 51, 56, 64], "regularli": 32, "reinit": [88, 89], "reiniti": 38, "reinitialis": 32, "rel": [25, 32, 37, 39, 40, 41, 48, 50, 61, 86], "relat": [1, 23, 25, 29, 34, 39, 43, 47, 50, 84], "relationship": 50, "relative_mous": 48, "releas": [1, 12, 17, 18, 23, 25, 29, 30, 32, 33, 37, 39, 42, 44, 46, 51, 58, 63, 84, 86, 88, 89], "release_buff": 1, "relev": [29, 84], "reli": [8, 33, 88], "reliabl": [18, 23, 37], "remain": [20, 40, 45, 51, 52, 85], "remap": [42, 47], "rememb": [25, 39, 50, 51, 59, 61, 63, 64, 65, 68, 73, 84, 86, 87], "remind": 68, "remov": [12, 17, 18, 25, 27, 28, 32, 36, 44, 48, 50, 56, 58, 62, 64, 89], "remove_intern": 64, "remove_sprites_of_lay": 50, "renam": 47, "render": [15, 23, 24, 26, 32, 41, 48, 50, 58, 63, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88], "render_device_reset": 25, "render_raw": 29, "render_raw_to": 29, "render_targets_reset": 25, "render_to": 29, "renderclear": [50, 64], "renderplain": [50, 58, 64, 66, 89], "renderupd": [26, 50, 64, 84], "renderupdatesdraw": 64, "repaint": [25, 27, 50], "repaint_rect": 50, "repcolor": 42, "repeat": [23, 33, 35, 38, 40, 44, 61, 62], "repeatedli": [50, 51, 54, 56, 58, 84], "replac": [1, 23, 27, 29, 38, 42, 43, 44, 51, 52, 54, 59, 62, 65, 68], "report": [26, 32, 44, 56, 84], "repositori": [44, 84], "repr": 44, "repres": [1, 11, 12, 13, 15, 17, 19, 20, 22, 23, 24, 25, 29, 31, 32, 33, 35, 36, 38, 39, 40, 41, 44, 45, 47, 48, 52, 54, 56, 58, 62, 63, 64, 65, 84, 87], "represent": [1, 3, 8, 15, 21, 24, 30, 42, 56, 58], "reproduc": 18, "request": [1, 23, 26, 28, 29, 35, 38, 49, 51, 52, 59], "requir": [17, 18, 23, 25, 26, 28, 33, 35, 36, 37, 38, 44, 45, 46, 47, 50, 51, 55, 56, 59, 61, 62, 63, 64, 65, 68, 70, 84, 85, 86, 88], "resampl": 38, "rescal": 26, "resembl": [26, 43, 52], "reserv": [25, 37, 38], "reset": [29, 32, 37, 38, 40, 50, 58, 89], "resist": 84, "resiz": [15, 23, 25, 26, 35, 45, 48, 56, 58, 85], "resolut": [15, 16, 23, 29, 41, 51, 54, 56, 58, 59, 63], "resolv": [46, 68, 87], "resourc": [26, 38, 40, 44, 59, 61, 66, 84, 87], "respect": [1, 22, 23, 24, 33, 35, 36, 46, 50, 51, 59], "respond": [25, 27, 62, 84], "respons": [9, 53], "rest": [19, 25, 27, 57, 58, 62, 63, 65, 84], "restart": [40, 86], "restor": [23, 25, 33, 48], "restrict": [28, 51], "result": [20, 24, 28, 29, 35, 36, 37, 40, 42, 45, 51, 53, 56, 58, 62, 63, 64, 65, 68, 69, 70, 73, 76, 84, 89], "resultscreen": [77, 78], "resum": [19, 38, 40], "retail": 63, "retain": [18, 36, 51], "retrac": 23, "retriev": [13, 36, 38, 46, 84, 88], "return": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 68, 71, 72, 73, 79, 80, 81, 84, 85, 86, 87, 88, 89], "reus": [18, 86], "reusabl": 86, "rev": 44, "revamp": 62, "revers": [1, 51, 58, 63, 65, 89], "revis": [44, 61, 63], "reward": [58, 63], "rewind": 40, "rewound": 26, "rewrit": 84, "rgb": [18, 20, 23, 24, 28, 30, 31, 41, 43, 51, 57, 58, 63, 65, 84, 85], "rgba": [1, 3, 20, 23, 24, 30, 31, 51], "rgba_premult": 31, "rgbarrai": 65, "rgbx": 31, "rich": 58, "rid": 57, "ridicul": 61, "right": [20, 23, 24, 25, 29, 30, 33, 35, 38, 42, 44, 45, 47, 50, 51, 58, 62, 63, 64, 65, 66, 67, 68, 69, 75, 76, 77, 78, 79, 80, 81, 84, 87, 88, 89], "rins": 62, "rle": 51, "rleaccel": [51, 58, 66], "rleaccelok": 51, "road": 62, "roll": [32, 39, 64], "root": [36, 53, 84], "rosybrown": 21, "rosybrown1": 21, "rosybrown2": 21, "rosybrown3": 21, "rosybrown4": 21, "rotat": [25, 26, 29, 36, 48, 50, 56, 58, 63, 66], "rotate_i": 36, "rotate_ip": 36, "rotate_ip_rad": 36, "rotate_rad": 36, "rotate_rad_ip": 36, "rotate_x": 36, "rotate_x_ip": 36, "rotate_x_ip_rad": 36, "rotate_x_rad": 36, "rotate_x_rad_ip": 36, "rotate_y_ip": 36, "rotate_y_ip_rad": 36, "rotate_y_rad": 36, "rotate_y_rad_ip": 36, "rotate_z": 36, "rotate_z_ip": 36, "rotate_z_ip_rad": 36, "rotate_z_rad": 36, "rotate_z_rad_ip": 36, "rotozoom": 56, "rough": 84, "round": [20, 24, 36, 37, 38, 88], "routin": [1, 18, 25, 28, 29, 51, 56, 58, 59, 68, 84], "row": [35, 42, 51, 65], "row1": 65, "row2": 65, "royalblu": 21, "royalblue1": 21, "royalblue2": 21, "royalblue3": 21, "royalblue4": 21, "rr": 20, "rrggbb": 20, "rrggbbaa": 20, "rt": 32, "rudder": 32, "rudimentari": 26, "ruin": 65, "rule": [59, 70, 73, 74, 89], "rumbl": [32, 47], "run": [15, 23, 25, 26, 31, 32, 38, 44, 53, 54, 56, 58, 59, 62, 63, 64, 65, 66, 67, 84, 85, 89], "run_speed_test": 26, "run_test": 53, "run_tests__test": 53, "rundown": 26, "runner": 53, "runtim": [23, 38, 54, 56, 63], "runtimeerror": [29, 44], "rw": 9, "rwobject": 0, "rx": 30, "ry": 30, "r\uac12": 76, "saddlebrown": 21, "safe": [9, 19, 23, 25, 28, 29, 32, 37, 38, 42, 44, 47, 50, 51, 57, 60], "sai": [51, 61, 62, 64, 68, 84], "said": [24, 61, 68, 70, 74, 84, 85], "sake": [84, 85], "salmon": 21, "salmon1": 21, "salmon2": 21, "salmon3": 21, "salmon4": 21, "salt": 84, "sam": 63, "same": [9, 18, 19, 20, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 35, 36, 38, 39, 42, 43, 44, 45, 47, 50, 51, 52, 54, 56, 57, 58, 59, 62, 63, 64, 65, 67, 68, 70, 71, 72, 73, 84, 85, 89], "sampl": [15, 20, 38, 40, 56, 57, 62, 64, 65, 88], "san": 29, "sandybrown": 21, "satisfactori": 86, "satisfi": 23, "satur": 18, "sauf": 35, "save": [15, 18, 31, 61, 63, 84], "save_extend": 31, "saw": [57, 58, 62], "scalabl": 29, "scalar": [36, 45, 56, 58], "scale": [18, 23, 24, 26, 29, 31, 35, 36, 44, 45, 48, 50, 56, 58, 63, 65, 66], "scale2x": 56, "scale_bi": [45, 56], "scale_by_ip": 45, "scale_to_length": 36, "scaledown": 65, "scalei": 45, "scaler": 26, "scaletest": 26, "scaleup": 65, "scalex": 45, "scan": 19, "scancod": [25, 33], "scanf": [67, 75], "scanlin": 62, "scant": 26, "scene": [31, 57], "school": 39, "scope": [42, 65], "score": [26, 50, 61, 70, 89], "scoreboard": 61, "scoreup": 61, "scrap": [15, 46], "scrap_bmp": 46, "scrap_clipboard": [26, 46], "scrap_pbm": 46, "scrap_ppm": 46, "scrap_select": 46, "scrap_text": 46, "scratch": [64, 84], "scratchi": 38, "screen": [1, 4, 15, 22, 24, 25, 26, 28, 31, 32, 33, 34, 39, 44, 45, 46, 48, 50, 51, 57, 58, 59, 61, 63, 64, 65, 66, 68, 69, 70, 71, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "screen_dim": 26, "screen_height": 23, "screen_rect": [45, 50], "screen_width": 23, "screensav": [23, 44, 69], "screenshot": 58, "script": [28, 29, 60, 63, 66], "scroll": [25, 26, 39, 51, 62, 63, 64, 84], "scrollabl": 26, "scrollock": 33, "sdl": [1, 5, 7, 9, 11, 12, 23, 25, 26, 27, 32, 38, 41, 43, 44, 47, 48, 56, 59, 63, 84], "sdl1": [19, 23, 41, 51], "sdl2": [25, 38, 39, 44, 47, 51, 55], "sdl3": 23, "sdl_appact": 25, "sdl_appinputfocu": 25, "sdl_appmousefocu": 25, "sdl_audiodriv": 44, "sdl_delai": 54, "sdl_event": 5, "sdl_gfx": 30, "sdl_hint_video_allow_screensav": 23, "sdl_imag": [31, 63], "sdl_joystick_allow_background_ev": [32, 44], "sdl_mixer": [38, 40], "sdl_mousewheel_flip": 25, "sdl_rect": [8, 11], "sdl_rwop": 9, "sdl_surfac": 11, "sdl_ttf": [28, 29, 44], "sdl_video": 51, "sdl_video_allow_screensav": 44, "sdl_video_cent": 44, "sdl_video_window_po": 44, "sdl_video_x11_net_wm_bypass_compositor": 44, "sdl_videodriv": [23, 44], "sdl_videoinfo": 4, "sdl_window": 1, "sdl_windowid": 23, "sdlerror": 40, "sdlversion": 44, "sea": 24, "seagreen": 21, "seagreen1": 21, "seagreen2": 21, "seagreen3": 21, "seagreen4": 21, "search": [15, 28, 29, 35, 45, 50, 56], "search_color": 56, "search_surf": 56, "seashel": 21, "seashell1": 21, "seashell2": 21, "seashell3": 21, "seashell4": 21, "second": [15, 17, 19, 20, 22, 24, 26, 30, 32, 36, 38, 40, 42, 49, 50, 53, 54, 58, 60, 62, 63, 65, 69, 70, 71, 72, 77, 84, 88, 89], "secondari": 84, "section": [15, 19, 51, 58, 61, 62, 64, 65, 67, 68, 86, 89], "secur": 86, "see": [9, 16, 18, 19, 20, 23, 24, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 43, 44, 45, 47, 50, 51, 52, 56, 57, 58, 60, 61, 62, 63, 64, 65, 84, 85, 87, 88, 89], "seed": [53, 65], "seek": 9, "seem": [31, 46, 61, 62, 63, 64, 68, 72, 73, 84, 85, 87], "seemingli": 61, "seen": [23, 61, 62, 65, 88, 89], "segment": [24, 30], "select": [18, 23, 25, 29, 33, 35, 37, 38, 40, 44, 46, 53, 59, 62, 67, 69, 73, 84], "self": [20, 32, 35, 36, 45, 50, 51, 56, 57, 58, 62, 64, 66, 87, 88, 89], "sell": 62, "semi": [26, 84], "semicolon": 33, "semiton": 37, "send": [25, 37, 38, 40, 89], "sens": [33, 62, 84, 87, 88, 89], "sent": [23, 25, 32, 33, 38, 40], "separ": [18, 22, 26, 28, 29, 37, 38, 44, 45, 50, 51, 52, 53, 56, 57, 61, 62, 64, 65, 72, 84, 86], "sequenc": [1, 8, 14, 22, 23, 24, 25, 29, 30, 33, 35, 37, 39, 42, 45, 50, 51, 56, 63, 64, 84, 88], "sequenti": 24, "seri": [37, 63, 70], "serv": [27, 63, 84], "server": [26, 39], "servic": 18, "session": 53, "set": [1, 6, 17, 18, 20, 22, 23, 24, 25, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 56, 58, 60, 62, 63, 65, 67, 68, 70, 71, 72, 73, 84, 85, 86, 87, 88, 89], "set_allow": 25, "set_allow_screensav": 23, "set_alpha": [43, 51], "set_at": [35, 51, 65, 84], "set_behavior": 56, "set_block": [25, 84], "set_bold": 28, "set_capt": [22, 23, 24, 32, 58, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85, 89], "set_clip": [50, 51], "set_color": 56, "set_colorkei": [43, 51, 58, 66, 84], "set_control": [18, 57], "set_cursor": [22, 39], "set_default_resolut": 29, "set_endev": [38, 40], "set_error": 44, "set_eventst": 47, "set_fullscreen": 48, "set_gamma": 23, "set_gamma_ramp": 23, "set_grab": [25, 33, 39], "set_icon": [23, 48], "set_instru": 37, "set_ital": 28, "set_keyboard_grab": 25, "set_length": 20, "set_loc": 41, "set_map": 47, "set_mask": 51, "set_mod": [1, 15, 22, 23, 24, 32, 33, 34, 39, 44, 46, 48, 51, 57, 58, 59, 62, 63, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 89], "set_modal_for": 48, "set_num_channel": 38, "set_palett": [23, 51], "set_palette_at": 51, "set_po": [39, 40], "set_repeat": 33, "set_reserv": 38, "set_script": 28, "set_shift": 51, "set_smoothscale_backend": 56, "set_strikethrough": 28, "set_text_input_rect": 33, "set_tim": 54, "set_timing_threshold": 50, "set_timing_treshold": 50, "set_underlin": 28, "set_viewport": 48, "set_vis": [39, 58, 66], "set_volum": [38, 40], "set_window": 48, "setcolor": 35, "setsurfac": 35, "settabl": 44, "setup": [15, 22, 26, 84], "sever": [15, 22, 23, 24, 26, 29, 38, 45, 50, 54, 59, 60, 62, 63, 64, 65, 84, 85, 89], "sf": 42, "sfnt": 29, "shade": 24, "shall": 18, "shallow": 35, "shape": [15, 17, 28, 42, 43, 51, 63, 65, 72, 84], "share": [15, 25, 28, 31, 32, 33, 38, 46, 48, 51, 52, 54], "sharp": 24, "she": 85, "shell": 26, "shift": [23, 25, 26, 33, 35, 42, 44, 51, 59, 65], "shinner": [58, 59, 60, 62, 63, 64, 65, 84], "shoot": 63, "short": [25, 63, 65, 84], "shortcut": [25, 64], "shorter": [23, 58, 65], "shortest": 36, "shot": 64, "should": [18, 19, 22, 23, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 42, 44, 45, 47, 50, 51, 54, 56, 58, 59, 61, 62, 63, 64, 65, 68, 69, 84, 85, 86, 87, 89], "shoulder": 47, "shouldn": 88, "show": [15, 22, 23, 26, 29, 33, 39, 41, 48, 53, 56, 57, 58, 59, 62, 65, 84, 88, 89], "show_output": 53, "showcas": [26, 84], "shown": [23, 25, 33, 41, 57, 62, 84, 87], "shrink": [45, 84, 89], "shrinkag": 56, "shrunk": 51, "shut": [23, 29, 32, 44, 69], "shutdown": [7, 63], "side": [23, 24, 42, 45, 47, 58, 61, 63, 87, 88], "sienna": 21, "sienna1": 21, "sienna2": 21, "sienna3": 21, "sienna4": 21, "sign": [17, 33, 37, 38, 39, 44], "signal": [25, 40, 85], "signific": 51, "silenc": 53, "silent": [23, 60], "silver": 21, "simd": 51, "similar": [29, 31, 32, 42, 49, 50, 51, 54, 58, 62, 64, 65, 85, 86, 88, 89], "simpl": [15, 22, 23, 24, 25, 26, 32, 42, 50, 51, 56, 57, 58, 60, 61, 62, 63, 64, 65, 66, 67, 69, 71, 72, 73, 74, 84, 85, 86, 89], "simpler": [62, 63, 64], "simplest": [57, 71], "simpli": [22, 25, 37, 41, 44, 50, 57, 58, 59, 62, 63, 65, 67, 84, 85, 87, 89], "simul": [58, 67], "simultan": [38, 67], "sin": [87, 89], "sinc": [15, 19, 20, 23, 25, 28, 31, 32, 36, 38, 39, 44, 48, 50, 51, 52, 54, 57, 58, 59, 60, 62, 63, 64, 65, 84], "singl": [17, 20, 22, 23, 24, 25, 28, 29, 30, 32, 33, 38, 40, 41, 42, 43, 45, 50, 51, 52, 53, 58, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 73, 84, 85], "sit": 84, "site": 26, "situat": [23, 36, 51, 58, 64, 65, 84], "six": [38, 40, 61, 63], "sizabl": 84, "size": [8, 17, 18, 22, 23, 24, 25, 26, 28, 29, 31, 35, 38, 39, 41, 42, 43, 45, 48, 50, 51, 52, 56, 57, 58, 59, 61, 62, 63, 65, 66, 68, 75, 76, 77, 78, 79, 80, 81, 84, 85], "sizeal": 22, "sizenesw": 22, "sizenws": 22, "sizeof": 35, "sizer_x_str": 22, "sizer_xy_str": 22, "sizer_y_str": 22, "skeleton": 84, "skew": 28, "skill": 84, "skip": [23, 35, 38, 58], "skyblu": 21, "skyblue1": 21, "skyblue2": 21, "skyblue3": 21, "skyblue4": 21, "sl": 32, "slash": [22, 33], "slateblu": 21, "slateblue1": 21, "slateblue2": 21, "slateblue3": 21, "slateblue4": 21, "slategrai": 21, "slategray1": 21, "slategray2": 21, "slategray3": 21, "slategray4": 21, "slategrei": 21, "sleep": [25, 54], "slerp": 36, "slice": [36, 42, 43, 65, 84], "slight": [58, 64], "slightli": [15, 20, 25, 26, 36, 39, 40, 51, 54, 58, 64, 84, 85], "slope": 24, "sloppi": 26, "slot": 0, "slow": [23, 48, 51, 57, 59, 62, 65, 84, 85], "slower": [50, 51, 54, 62, 84], "slowest": [44, 51, 85], "small": [18, 22, 23, 28, 29, 32, 36, 39, 44, 45, 58, 62, 63, 65, 71, 72, 84, 85, 87], "smaller": [18, 23, 35, 38, 45, 51, 71, 72, 84], "smallest": [23, 51, 59], "smart": [62, 64], "smooth": [28, 58], "smoother": 62, "smoothli": [56, 62], "smoothscal": [26, 56], "smoothscale_bi": 56, "sn9c101": 18, "snakeviz": 84, "snapshot": 57, "sndarrai": [15, 26, 38, 49, 63], "snow": 21, "snow1": 21, "snow2": 21, "snow3": 21, "snow4": 21, "so": [10, 17, 18, 20, 22, 23, 24, 26, 29, 30, 31, 32, 33, 36, 38, 42, 43, 44, 45, 50, 51, 52, 56, 58, 59, 61, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 84, 85, 86, 87, 88, 89], "socket": [86, 89], "soften": 65, "softwar": [15, 18, 23, 30, 37, 41, 50, 51, 58, 63], "solarwolf": 63, "solid": [24, 28, 30, 50, 51, 52, 56, 65, 84], "solut": [84, 85], "solv": [28, 38, 61, 67, 84], "some": [15, 18, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 34, 36, 37, 38, 39, 42, 44, 45, 46, 50, 56, 57, 58, 59, 60, 61, 63, 64, 65, 67, 68, 69, 70, 72, 84, 85, 86, 87, 88, 89], "someimag": 26, "someth": [15, 18, 24, 26, 39, 56, 57, 59, 62, 63, 64, 65, 68, 69, 70, 84, 85], "sometim": [22, 23, 27, 44, 64, 84], "somewhat": [26, 64, 84], "somewher": [64, 68], "soni": 32, "sonix": 18, "soon": [40, 44, 64, 88], "sophist": [86, 87], "sorri": [57, 59], "sort": [23, 25, 26, 27, 50, 58, 59, 62, 64, 84, 89], "sound": [7, 15, 26, 40, 58, 61, 63, 64, 66, 67, 70, 73, 84, 86], "sound_array_demo": 26, "sourc": [15, 18, 26, 31, 37, 38, 42, 43, 48, 50, 51, 56, 58, 61, 62, 63, 64, 67, 68, 69, 70, 84, 86, 89], "source_rect": 50, "sourcecod": [76, 77, 78], "south": 22, "southeast": 22, "southwest": 22, "space": [20, 28, 29, 31, 33, 37, 42, 62, 84], "sparingli": 48, "speak": [85, 86], "speaker": 38, "special": [18, 22, 23, 25, 33, 35, 38, 43, 50, 51, 56, 58, 59, 62, 64, 65, 68, 84, 85], "special_flag": [48, 50, 51], "specif": [23, 25, 28, 29, 33, 35, 36, 38, 43, 46, 50, 51, 52, 57, 58, 59, 62, 64, 65, 68, 70, 71, 72, 84], "specifi": [13, 18, 21, 23, 24, 28, 29, 31, 32, 35, 36, 37, 38, 40, 46, 47, 50, 56, 59, 65, 89], "sped": 24, "speed": [25, 26, 29, 44, 54, 62, 63, 64, 69, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "spend": 84, "spent": [63, 74, 84], "spheric": 36, "spill": 24, "spin": [26, 58, 66, 88, 89], "split": [22, 29, 58, 63, 64, 66, 84], "sport": 63, "spot": 84, "spread": 84, "spring": 56, "springgreen": 21, "springgreen1": 21, "springgreen2": 21, "springgreen3": 21, "springgreen4": 21, "sprite": [15, 26, 35, 36, 50, 61, 66, 84, 88, 89], "sprite1": 50, "sprite2": 50, "sprite_dict": 50, "sprite_height": 62, "sprite_list": 50, "sprite_width": 62, "spritecollid": [50, 64, 87], "spritecollideani": 50, "spritedict": 64, "sprites_click": 84, "sqrt": [36, 69], "squar": [24, 32, 36, 72], "squeez": 84, "sr": 32, "src": 65, "src_c": [0, 10, 13], "srcalpha": [35, 43, 51, 56], "srccolorkei": 51, "srcobj": 11, "srcrect": [11, 48], "sse": [26, 56], "stabil": 33, "stabl": 43, "stack": 25, "stage": 57, "stai": [61, 65, 70], "stand": 26, "standard": [1, 22, 25, 27, 41, 43, 44, 46, 50, 57, 58, 59, 61, 63, 64, 65, 68, 84], "star": 26, "starfield": 26, "start": [1, 17, 18, 19, 24, 25, 26, 29, 30, 32, 33, 35, 38, 39, 40, 42, 44, 45, 47, 50, 51, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 69, 74, 84, 85, 86, 88, 89], "start_angl": [24, 30], "start_index": 65, "start_po": 24, "start_text_input": 33, "startup": [7, 38], "state": [23, 25, 26, 29, 31, 32, 33, 37, 38, 39, 46, 47, 48, 50, 51, 58, 64, 66, 67, 84, 88, 89], "statement": [68, 69, 70, 71, 72, 84, 89], "statement\uc5d0": 77, "statement\uc5d0\uc11c": 77, "static": [47, 48, 50, 69], "stationari": 64, "statu": [26, 32, 37, 70], "stderr": 53, "stdin": 65, "stdout": 53, "steelblu": 21, "steelblue1": 21, "steelblue2": 21, "steelblue3": 21, "steelblue4": 21, "steep": 24, "stencil": 23, "step": [15, 30, 32, 42, 58, 65, 68, 70, 73, 88], "stereo": [23, 38, 49], "stick": [32, 47, 62, 65, 84], "still": [23, 25, 28, 31, 33, 36, 38, 39, 43, 45, 46, 47, 48, 51, 56, 57, 58, 61, 62, 63, 64, 65, 71, 72, 73, 84, 85, 88, 89], "stop": [1, 18, 19, 24, 30, 32, 33, 38, 39, 40, 44, 47, 54, 57, 62, 64, 84, 88, 89], "stop_angl": [24, 30], "stop_rumbl": [32, 47], "stop_text_input": 33, "store": [8, 22, 23, 25, 29, 30, 35, 50, 51, 52, 58, 62, 64, 68], "str": [17, 18, 20, 28, 29, 32, 33, 35, 37, 44, 47, 49, 52, 71, 72, 73, 79, 80, 81], "straight": [24, 30, 51, 62, 63, 65, 86], "straighten": 62, "straightforward": [62, 63], "strang": [23, 63], "stream": [15, 37, 38, 48, 63], "strength": [29, 32, 47], "stress": [63, 84], "stretch": [23, 26, 28, 29], "strict": [18, 61, 84], "strictli": [30, 85], "stride": [17, 42], "strike": 29, "strikethrough": 28, "string": [9, 14, 17, 18, 19, 20, 22, 23, 25, 28, 29, 31, 32, 33, 37, 38, 41, 44, 46, 48, 49, 51, 52, 53, 56, 68, 84, 86, 87], "strip": 51, "stripe": 65, "stroke": 24, "strong": 29, "strongli": [23, 50], "struct": [1, 4, 5, 6, 7, 9, 17, 29, 42, 43, 51, 52], "structur": [7, 10, 43, 85, 87, 88], "stuck": 62, "studi": [65, 87], "studio": 63, "stuff": [46, 57, 61, 62, 65], "stump": 84, "style": [25, 26, 29, 32, 42, 63, 86], "style_default": 29, "style_norm": 29, "style_obliqu": 29, "style_strong": 29, "style_underlin": 29, "style_wid": 29, "sub": [50, 84], "subarrai": 42, "subclass": [2, 3, 4, 5, 6, 7, 11, 12, 17, 20, 35, 36, 45, 50, 51], "subdirectori": [26, 58], "subgroup": 67, "subject": [23, 44], "submask": 35, "submit": 84, "submodul": [44, 53], "subpackag": 53, "subprocess": 53, "subprocess_ignor": 53, "subscript": [36, 42], "subsect": 62, "subsequ": [23, 57], "subset": [22, 23, 84], "substanti": 84, "substitut": [18, 22], "substr": 37, "subsubsurfac": 51, "subsurfac": [12, 51, 56], "subtract": 89, "subview": 42, "succe": [18, 23], "succeed": 18, "success": [1, 2, 5, 8, 9, 11, 23, 44, 84], "successfulli": [32, 47], "sudden": 63, "suffix": 45, "suggest": [42, 58, 84], "suit": [27, 51, 59], "suitabl": [23, 28, 29, 46, 63, 68, 86], "sum": [24, 74], "summari": 64, "summer": 63, "super": [25, 26, 35, 48, 64], "superclass": 36, "superior": 51, "suppli": [18, 35, 37, 38, 57, 58, 65, 85], "support": [1, 6, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 54, 56, 57, 58, 59, 63, 84, 87], "suppos": [56, 84], "supposedli": 84, "sure": [10, 50, 54, 57, 58, 62, 63, 64, 65, 69, 70, 84, 89], "surf": [22, 26, 29, 30, 42, 50, 51, 56], "surfac": [0, 1, 2, 10, 12, 15, 18, 20, 22, 23, 24, 26, 28, 29, 30, 31, 35, 36, 39, 41, 43, 46, 48, 50, 51, 57, 58, 59, 62, 63, 64, 66, 85, 89], "surface_dest": 50, "surface_to_arrai": 43, "surfarrai": [15, 17, 26, 43, 49, 51, 52, 63, 84], "surfdemo_show": 65, "surflock": 0, "surfobj": 12, "surpris": [62, 63], "surrog": [14, 29], "surround": [56, 85], "suspend": 84, "svg": [16, 31], "svgalib": 23, "swap": [23, 31, 42, 45], "swatch": 20, "swig": 63, "switch": [22, 23, 29, 44, 50, 59, 89], "switch_lay": 50, "swizzl": 36, "swsurfac": 51, "sy": [14, 44, 53, 62, 63, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 86, 89], "symbol": 33, "symmetri": 36, "sync": [23, 25], "synchron": 37, "synonym": 25, "syntax": [65, 84], "syntaxerror": 44, "synthes": 37, "sysfont": [28, 29], "sysrq": 33, "system": [14, 15, 18, 19, 22, 23, 25, 26, 27, 28, 29, 32, 33, 36, 37, 39, 40, 44, 48, 49, 51, 52, 53, 59, 68, 70, 84, 85, 88], "system_cursor_arrow": 22, "system_cursor_crosshair": 22, "system_cursor_hand": 22, "system_cursor_ibeam": 22, "system_cursor_no": 22, "system_cursor_sizeal": 22, "system_cursor_sizen": 22, "system_cursor_sizenesw": 22, "system_cursor_sizenws": 22, "system_cursor_sizew": 22, "system_cursor_wait": 22, "system_cursor_waitarrow": 22, "systemexit": [84, 86, 89], "sys\ub294": 76, "t": [9, 18, 19, 23, 24, 26, 27, 28, 29, 32, 33, 36, 40, 44, 46, 48, 49, 50, 51, 52, 54, 56, 57, 58, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 85, 86, 87, 88, 89], "ta": 61, "tab": [25, 33], "tabl": [23, 43], "tackl": 84, "tag": [40, 53], "taka": 18, "takafumi": 18, "take": [14, 22, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 40, 42, 45, 50, 51, 53, 56, 57, 58, 59, 63, 64, 65, 68, 84, 85, 86, 88, 89], "taken": [3, 21, 23, 26, 27, 36, 38, 39, 56, 87], "talk": 62, "tan": 21, "tan1": 21, "tan2": 21, "tan3": 21, "tan4": 21, "tango": 29, "tank": 84, "target": [29, 36, 42, 43, 45, 48, 50, 58, 64, 66], "target_textur": 48, "task": [59, 86], "tau": 24, "teach": 62, "teal": 21, "technic": 41, "techniqu": [50, 84], "tell": [9, 38, 39, 44, 51, 58, 59, 62, 64, 84, 88], "temp": 8, "templat": 84, "temporari": [56, 65], "temporarili": [19, 24, 33, 38, 40, 51, 52], "tempt": 84, "temptat": 84, "ten": 54, "tenni": [88, 89], "term": [62, 64, 85], "termin": [1, 25, 37, 44, 58, 62, 68], "terminologi": [39, 84], "terrain1": 62, "terrain2": 62, "test": [15, 19, 23, 25, 26, 28, 29, 31, 32, 33, 36, 38, 39, 41, 44, 45, 47, 50, 51, 56, 58, 59, 60, 65], "test_threshold_dest_surf_not_chang": 56, "test_util": 56, "testin": 37, "testout": 37, "testsprit": 26, "tetri": 74, "text": [25, 26, 28, 29, 32, 33, 46, 66, 68, 69, 70, 71, 84, 85, 87], "text_bitmap": 32, "text_print": 32, "textedit": [25, 33], "textinput": [25, 33], "textmarker_str": 22, "textpo": [58, 66, 85], "textprint": 32, "textur": [30, 48], "textured_polygon": 30, "textureorimag": 48, "tga": [31, 63], "than": [15, 18, 19, 23, 24, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 40, 44, 45, 47, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 69, 70, 74, 84, 85, 86, 87, 88, 89], "thank": 88, "the_arg": 11, "the_dirty_rectangl": 84, "thecorruptor": 16, "thei": [1, 10, 18, 19, 22, 23, 24, 25, 26, 28, 29, 32, 35, 36, 38, 39, 44, 45, 46, 47, 50, 51, 56, 58, 59, 62, 63, 64, 65, 68, 69, 71, 72, 84, 86, 87, 88, 89], "them": [15, 16, 22, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 38, 42, 44, 45, 49, 50, 51, 52, 57, 58, 59, 62, 63, 65, 71, 84, 85, 86, 87, 88, 89], "themselv": [51, 61, 64, 85], "theorem": 36, "theori": [18, 84], "therefor": [24, 30, 35, 36, 50, 62, 64, 84, 87], "theta": [35, 36], "thi": [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 72, 73, 74, 84, 85, 86, 87, 88, 89], "thick": [24, 28], "thickarrow_str": 22, "thin": 58, "thing": [19, 25, 27, 36, 40, 44, 57, 58, 59, 60, 61, 62, 63, 64, 65, 85, 87], "think": [18, 23, 36, 62, 64, 65, 70, 74, 84, 89], "third": [17, 18, 22, 30, 36, 62, 63, 65, 71, 85], "third_surfac": 56, "thirteen": 15, "thistl": 21, "thistle1": 21, "thistle2": 21, "thistle3": 21, "thistle4": 21, "thorough": 89, "those": [15, 18, 22, 23, 24, 25, 26, 28, 29, 39, 42, 43, 50, 57, 61, 62, 64, 65, 84, 86, 87, 88, 89], "though": [18, 23, 30, 37, 38, 43, 53, 61, 65, 84, 85, 86, 87, 88, 89], "thought": 84, "thousand": 49, "thread": [9, 18, 25, 26, 27, 28, 30, 38, 39, 50, 53, 57], "three": [32, 36, 38, 44, 51, 52, 64, 65, 68, 84, 85, 88], "threshold": [35, 42, 50, 56], "threshold_behavior_from_search_color": 56, "threshold_color": 56, "throttl": 32, "through": [2, 18, 25, 27, 28, 29, 31, 35, 37, 38, 44, 50, 51, 58, 61, 62, 63, 64, 74, 84, 85, 88], "throughout": 34, "throw": [40, 62, 64], "thrown": [56, 89], "thru": 63, "thu": [36, 42, 87], "thumbnail": 26, "ti": 40, "tick": [15, 22, 24, 32, 39, 54, 58, 62, 66, 69, 71, 72, 73, 77, 79, 80, 81, 84, 89], "tick_busy_loop": 54, "tick\ud568\uc218\ub294": 77, "tie": [57, 72], "tif": 31, "tiff": [31, 46], "tile": 62, "time": [13, 15, 18, 19, 22, 23, 24, 25, 26, 28, 29, 32, 34, 37, 38, 39, 40, 48, 49, 50, 51, 53, 56, 57, 58, 59, 61, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 77, 78, 79, 80, 81, 84, 85, 86, 87, 89], "time_m": 50, "time_out": 53, "time_proc": 37, "timeout": 25, "timer": [37, 54], "timer_resolut": [34, 54], "timestamp": 37, "tini": [23, 84], "tip": [15, 64, 84], "titl": [23, 48, 58, 63, 68], "tl": 89, "to_surfac": [35, 48], "tobyt": 31, "todo": 64, "togeth": [33, 38, 56, 61, 63, 65, 86], "toggl": [22, 47], "toggle_fullscreen": 23, "toler": [32, 36], "tom": [86, 89], "tomato": 21, "tomato1": 21, "tomato2": 21, "tomato3": 21, "tomato4": 21, "tomchanc": [86, 89], "tompong": [61, 89], "tone": 37, "too": [25, 27, 29, 36, 42, 45, 50, 51, 53, 58, 62, 65, 67, 68, 69, 73, 84, 85], "took": 63, "tool": [26, 64, 67, 84, 87], "toolkit": 48, "top": [15, 23, 24, 26, 28, 29, 31, 35, 38, 39, 45, 50, 51, 62, 63, 75, 76, 77, 78, 79, 80, 81, 84, 85, 86, 89], "topic": [15, 84], "topleft": [35, 42, 45, 50, 56, 58, 62, 66, 89], "toplevel": 38, "topmost": 50, "topright": [45, 89], "tort": 18, "tortur": 84, "tostr": 31, "total": [38, 43, 44, 53, 62, 68, 84, 86], "touch": [25, 32, 84], "touch_id": 25, "touchid": 55, "toward": [23, 25, 36, 61, 63, 84], "tp": [67, 75], "tprint": 32, "tr": 89, "traceback": [65, 86], "track": [15, 19, 50, 54, 57, 62, 64, 84], "trackbal": [25, 47], "tradition": 22, "trail": [53, 63], "trait": [68, 74], "transfer": [15, 46, 56, 61, 65], "transform": [15, 18, 29, 57, 58, 62, 63, 65, 66], "transform_test": 56, "translat": [8, 14, 26, 29, 33, 42], "transluc": 84, "transmiss": 37, "transpar": [23, 24, 26, 28, 29, 31, 35, 43, 48, 51, 52, 56, 58, 62, 63, 84, 86], "transpos": 42, "travel": 89, "treat": [23, 29, 32, 52, 65], "tree": 57, "trend": 63, "tri": [44, 62, 63, 84, 86], "tri_left": 22, "tri_right": 22, "trial": [74, 84], "triangl": [24, 26, 30, 32], "trick": [84, 89], "tricki": [64, 65, 84], "trickier": 65, "trigger": [25, 32, 33, 39, 40, 47, 68, 70], "trigon": 30, "trigonometri": 87, "tripl": 56, "triplet": [23, 24, 30, 58], "troubl": 84, "true": [2, 3, 4, 5, 6, 7, 9, 11, 12, 13, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 50, 51, 53, 56, 57, 58, 60, 62, 63, 64, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 89], "truetyp": [15, 28, 58, 63], "truli": 84, "truncat": [24, 30, 38, 45, 65], "truth": 64, "try": [25, 27, 29, 44, 51, 58, 61, 62, 63, 64, 65, 67, 84, 85, 86, 89], "ttf": [28, 29, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81], "ttf\ud30c\uc77c\ub85c": 76, "ttf\ud655\uc7a5\uc790\ub97c": 76, "tune": [29, 64], "tupl": [8, 17, 19, 20, 22, 23, 24, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 42, 44, 45, 47, 49, 50, 51, 52, 53, 59, 62, 65, 84], "turn": [29, 37, 56, 58, 62, 64, 66, 84], "turquois": 21, "turquoise1": 21, "turquoise2": 21, "turquoise3": 21, "turquoise4": 21, "turtl": 68, "tutori": [26, 61, 63, 66, 73, 74, 84, 85, 89], "tweak": 64, "twice": [50, 51, 56, 65], "twitch": 84, "two": [1, 15, 18, 19, 22, 23, 24, 25, 26, 28, 29, 30, 32, 33, 35, 36, 37, 38, 42, 43, 45, 47, 49, 50, 51, 53, 54, 56, 58, 62, 63, 64, 65, 70, 71, 72, 73, 84, 85, 87], "tx": 30, "ty": 30, "type": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 17, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 49, 50, 51, 52, 54, 56, 57, 58, 59, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "type1": 29, "type42": 29, "typeerror": [24, 28, 43, 45, 50, 51], "typelist": 25, "typestr": 17, "typic": [18, 23, 37, 51, 58], "u": [14, 17, 32, 33, 36, 41, 58, 62, 63, 65, 84], "u00000001": 28, "u0001": 28, "u0010ffff": 28, "u4": 17, "u_margin": [73, 81], "uc": [14, 28, 29], "ucs4": [28, 29], "uffff": [14, 28], "ufo": 63, "ufunc": 65, "ui": [26, 48], "uint": 65, "uint32": 1, "uint8": [1, 3], "uk": [86, 89], "ultim": 74, "unabl": [31, 46, 64], "unalt": [44, 84], "unari": 20, "unavail": [23, 58], "unchang": [14, 20, 23, 29], "uncommit": 44, "uncommon": 58, "uncompress": [31, 38], "undefin": [29, 65], "under": [15, 37, 39, 40, 62, 63, 86, 89], "underli": [28, 29, 32, 37, 44, 84], "underlin": [28, 29, 40], "underline_adjust": 29, "underneath": 65, "underscor": [33, 58], "understand": [15, 41, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 84, 85, 86, 88, 89], "understood": 71, "undesir": 24, "unencod": 44, "unfamiliar": [62, 84], "unfil": [30, 35], "unfilt": 56, "unfortun": [53, 84], "unicod": [14, 25, 28, 29, 33, 38, 44, 46, 51], "unicode_escap": 44, "unicodeencodeerror": [29, 44], "unicodeerror": 28, "unind": 32, "uniniti": [18, 19, 23, 28, 32, 37, 38, 44, 47], "union": [45, 64, 84], "union_ip": 45, "unional": 45, "unionall_ip": 45, "uniqu": [32, 33, 48, 50, 51, 62, 67, 72, 74], "unit": [29, 44, 51], "uniti": [67, 75], "unix": [22, 23, 63], "unknown": [23, 25, 28, 32], "unless": [6, 19, 23, 29, 31, 41, 46, 51, 54, 56, 58, 62, 64, 68, 69, 87, 88], "unlik": [15, 38, 43, 51, 65, 88], "unload": 40, "unlock": [24, 51], "unmap": 43, "unmap_rgb": [20, 24, 43, 51, 52], "unmodifi": 18, "unnorm": 67, "unnot": 33, "unpack": [20, 39], "unpaus": [19, 38, 40], "unplay": [23, 63], "unpleas": 62, "unpredict": 29, "unpunch": [58, 66], "unreal": [63, 67, 75], "unrealist": 89, "unrecogn": [28, 31], "unrel": 23, "unscal": 29, "unset": [29, 35, 51, 89], "unsetcolor": 35, "unsetsurfac": 35, "unsign": [1, 17, 20, 35, 38, 51, 65], "unspecifi": 29, "unstructur": 51, "unsupport": [18, 40], "until": [18, 23, 24, 25, 27, 32, 35, 37, 40, 47, 51, 54, 57, 62, 64, 66, 84, 85, 88], "untransform": 29, "unus": [31, 38, 50], "unwieldi": 84, "up": [15, 16, 18, 22, 23, 24, 25, 27, 28, 32, 33, 37, 38, 39, 40, 42, 44, 47, 50, 51, 58, 60, 62, 64, 65, 71, 72, 73, 80, 81, 84, 85, 86, 87, 88], "updat": [11, 15, 20, 23, 24, 25, 32, 36, 41, 44, 45, 47, 48, 50, 54, 59, 60, 61, 62, 63, 64, 66, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 84, 85, 87, 88, 89], "update_rect": 26, "update\ud568\uc218\uac00": 76, "upon": 89, "upper": [25, 29, 32, 51, 57], "upscal": 16, "us": [0, 1, 8, 9, 10, 13, 14, 15, 16, 17, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 56, 58, 59, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 85, 86, 87, 88, 89], "usabl": [20, 31, 57], "usag": [23, 27, 37, 51, 53], "usb": 37, "use_alpha": 26, "use_arraytyp": [49, 52], "use_bitmap_strik": 29, "use_compat": 33, "use_fastrendergroup": 26, "use_stat": 26, "use_valu": 45, "user": [5, 15, 18, 23, 24, 25, 27, 28, 33, 37, 44, 46, 51, 53, 58, 61, 63, 64, 65, 67, 72, 84, 85, 87], "userev": [25, 38], "usr": [26, 28, 66, 85, 86, 87], "usual": [23, 25, 28, 29, 32, 38, 44, 50, 51, 58, 59, 60, 62, 63, 64, 65, 70, 86], "utf": [14, 28, 29, 46], "utf8_str": 46, "util": [63, 74, 84], "uxxxxxxxx": [14, 29], "uyvy_overlai": 41, "v": [17, 20, 25, 33, 36, 41], "v1": 44, "v2": [1, 40], "v3": 17, "v4l2": [18, 57], "val": [1, 65], "val1": 1, "val2": 1, "valid": [17, 23, 28, 38, 39, 41, 45, 51, 57, 64], "valu": [1, 3, 8, 9, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 58, 59, 62, 64, 65, 68, 69, 71, 72, 84, 85, 88, 89], "value_to_set": 44, "valueerror": [17, 22, 24, 26, 29, 30, 31, 33, 35, 36, 43, 46, 49, 51, 52, 57, 65], "vari": 71, "variabl": [17, 22, 23, 28, 32, 37, 40, 44, 57, 58, 62, 63, 64, 65, 69, 70, 71, 74, 89], "variant": 22, "variat": 84, "varieti": [25, 63, 70, 84], "variou": [1, 15, 22, 25, 26, 34, 36, 89], "vast": 31, "ve": [15, 24, 32, 58, 61, 62, 63, 64, 65, 84, 85, 87, 88, 89], "vec": 36, "vector": [24, 30, 35, 61, 89], "vector2": [15, 24, 30, 35, 36, 45], "vector3": 36, "vectorelementwiseproxi": 36, "veloc": [37, 69], "ver": 44, "vera": [28, 29], "veri": [18, 23, 26, 37, 54, 58, 60, 62, 63, 64, 65, 70, 84, 87, 88, 89], "verifi": 23, "vernum": 44, "versatil": [47, 56], "version": [0, 11, 17, 20, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 38, 40, 43, 45, 47, 50, 51, 56, 57, 58, 59, 60, 61, 62, 63, 64, 84, 86, 89], "vertic": [18, 23, 24, 25, 26, 29, 30, 31, 39, 42, 56, 58, 62, 65], "vflip": [18, 57], "vgl": 23, "vgrade": [26, 65], "via": [18, 23, 25, 29, 35, 57, 84], "vidcaptur": 44, "video": [15, 23, 25, 26, 32, 44, 48, 51, 57, 59, 64], "video0": [18, 57], "video_mem": [23, 59], "videocaptur": [18, 57], "videoexpos": [23, 25], "videoinfo": [23, 59], "videores": [23, 25], "vidinfo": [23, 59], "view": [1, 2, 15, 42, 51], "view_p": 1, "violet": 21, "violetr": 21, "violetred1": 21, "violetred2": 21, "violetred3": 21, "violetred4": 21, "virtual": [23, 26, 37, 39, 45, 59], "visibl": [17, 22, 23, 39, 41, 50, 58, 59, 62, 63], "vision": [15, 18], "visit": 63, "vista": 23, "visual": [70, 71, 72, 73, 84], "visualis": 87, "vline": 30, "vm": 25, "vnc": 25, "void": [1, 8, 12], "volatil": 51, "volum": [15, 38, 40, 63], "vsync": [23, 48, 59], "w": [8, 25, 26, 30, 33, 35, 36, 42, 43, 45, 56], "wa": [9, 15, 16, 19, 20, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 35, 37, 38, 39, 40, 42, 43, 44, 47, 54, 57, 58, 62, 63, 64, 65, 68, 70, 73, 84, 86, 88], "wai": [18, 23, 24, 25, 26, 28, 29, 31, 32, 33, 36, 38, 39, 43, 44, 47, 48, 50, 51, 53, 58, 60, 61, 62, 63, 64, 65, 71, 85, 87, 88, 89], "wait": [22, 23, 25, 27, 32, 37, 39, 54, 58, 84], "waitarrow": 22, "walk": [58, 66], "wall": [36, 57, 89], "want": [9, 18, 20, 23, 25, 27, 31, 32, 33, 36, 44, 50, 51, 54, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 84, 85, 86, 87, 88, 89], "warn": [44, 48, 58, 66], "warranti": 18, "warrior": 63, "was_init": 29, "wasn": 63, "wast": [84, 87], "watch": [15, 57, 61, 85], "wav": [38, 40, 46, 58, 66], "wave": 49, "wavelength": [20, 56], "wayland": [22, 23], "we": [22, 24, 26, 32, 51, 56, 57, 58, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 84, 85, 86, 87, 88, 89], "weak": [12, 17, 84], "weakdirtysprit": 50, "weakli": 50, "weaksprit": 50, "web": [26, 66], "webcam": 18, "webp": 31, "websit": 63, "week": 63, "weight": [20, 36, 42, 56, 65], "weird": 33, "welcom": [15, 44], "well": [1, 2, 12, 22, 24, 27, 30, 31, 33, 39, 42, 44, 47, 51, 56, 61, 62, 63, 64, 65, 84, 89], "were": [1, 10, 23, 25, 28, 33, 50, 53, 62, 63, 64, 69, 84, 86, 89], "west": 22, "what": [15, 20, 22, 23, 24, 25, 26, 28, 32, 38, 39, 44, 46, 50, 57, 58, 59, 60, 61, 63, 64, 65, 68, 69, 71, 74, 85, 86, 88, 89], "whatev": [50, 57, 58, 61, 64, 84], "wheat": 21, "wheat1": 21, "wheat2": 21, "wheat3": 21, "wheat4": 21, "wheel": [25, 39], "when": [11, 12, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 49, 50, 51, 52, 53, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 84, 85, 87, 88, 89], "whenev": [23, 25, 38, 39, 44, 59, 65], "where": [10, 15, 17, 19, 20, 22, 23, 24, 26, 29, 30, 33, 35, 36, 38, 39, 40, 41, 42, 44, 46, 47, 50, 51, 53, 56, 57, 58, 62, 64, 65, 68, 84, 85, 88, 89], "wherea": [23, 45, 87], "wherev": [16, 25, 65], "whether": [18, 23, 25, 26, 28, 29, 35, 38, 39, 45, 46, 48, 56, 61, 62, 71, 72, 84, 85], "which": [1, 12, 14, 15, 17, 18, 20, 22, 23, 24, 25, 26, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 85, 86, 87, 88, 89], "whiff": [58, 66], "whiff_sound": [58, 66], "while": [0, 15, 17, 19, 22, 24, 25, 28, 30, 31, 32, 36, 38, 39, 40, 42, 43, 44, 49, 50, 51, 54, 57, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84, 85, 87, 89], "whilst": 23, "white": [21, 22, 24, 32, 35, 42, 57, 58, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80, 81, 85], "whitesmok": 21, "whitespac": 61, "who": [15, 48, 61, 62, 64, 86, 89], "whole": [23, 24, 37, 44, 62, 84, 87], "whoop": 62, "whose": [24, 55], "why": [44, 63, 70, 74, 84, 87], "wide": [24, 28, 29, 35, 44, 62, 63], "wider": [18, 33], "widget": 26, "width": [8, 18, 22, 23, 24, 26, 28, 29, 31, 32, 35, 41, 42, 45, 48, 50, 51, 56, 59, 62, 63, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 84], "wiki": 39, "wikipedia": 36, "win": [1, 58, 66, 73], "win32": 37, "windib": 23, "window": [1, 4, 15, 18, 25, 26, 27, 28, 29, 32, 33, 34, 37, 39, 44, 46, 48, 51, 58, 59, 62, 63, 68, 69, 84, 85, 88], "window_surfac": 23, "windowclos": 25, "windowdisplaychang": 25, "windowent": 25, "windowev": [23, 25], "windowevent_minim": 23, "windowexpos": 25, "windowfocusgain": 25, "windowfocuslost": 25, "windowhidden": 25, "windowhittest": 25, "windowiccprofchang": 25, "windowleav": 25, "windowmaxim": 25, "windowminim": 25, "windowmov": 25, "windowpos_cent": 48, "windowpos_undefin": 48, "windowres": 25, "windowrestor": 25, "windowshown": 25, "windowsizechang": 25, "windowtakefocu": 25, "wipe": 15, "wire": 32, "wireless": 32, "wisdom": 65, "wise": [29, 65], "wish": 86, "within": [12, 17, 22, 29, 33, 35, 36, 44, 50, 51, 56, 62, 68, 74, 84], "without": [18, 22, 23, 24, 29, 32, 36, 38, 42, 45, 48, 50, 51, 52, 58, 59, 62, 63, 64, 65, 69, 70, 72, 84, 88, 89], "wm": [23, 59], "won": [40, 50, 58, 61, 62, 64, 65, 84, 86, 87, 88], "wonder": [57, 84], "word": [28, 29, 46, 54, 62, 64, 88], "word_wrap": 29, "work": [15, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 37, 38, 42, 43, 45, 46, 50, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 85, 87, 89], "world": [57, 68, 69, 70, 74, 76, 77, 78, 84], "world\uac00": [77, 78], "world\ub294": 77, "world\uc758": 77, "worri": [26, 48, 50, 64, 65, 74, 84], "wors": 58, "worst": 61, "worth": [51, 54], "would": [17, 22, 24, 25, 28, 35, 36, 38, 44, 50, 51, 56, 57, 58, 60, 61, 62, 63, 64, 65, 84, 85, 87, 88, 89], "wow": 68, "wrap": [2, 4, 9, 17, 22, 28, 30, 42, 51, 63], "wrapper": [9, 84], "wrestl": 26, "writabl": 17, "write": [1, 9, 17, 26, 37, 51, 61, 63, 67, 84, 86, 87], "write_short": 37, "write_sys_ex": 37, "written": [15, 24, 28, 60, 61, 63, 64, 84, 86, 87], "wrong": [38, 65, 84], "wrote": 84, "www": [70, 78], "x": [2, 4, 5, 6, 7, 8, 11, 12, 15, 16, 22, 23, 24, 25, 26, 28, 29, 30, 33, 35, 36, 37, 39, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 56, 58, 59, 62, 68, 69, 70, 72, 73, 76, 77, 78, 80, 81, 84, 85, 87, 89], "x00": [14, 28], "x1": [24, 30, 45], "x10": 37, "x11": [23, 25, 37, 39, 44, 46, 48], "x12": 37, "x13": 37, "x2": [24, 30, 45], "x3": [24, 30], "x7d": 37, "x86": 56, "x_offset": 35, "x_scale": 48, "xbm": 22, "xbox": 47, "xf0": 37, "xf7": 37, "xfade": 65, "xor": 22, "xormask": [22, 39], "xpm": 31, "xx": 22, "xxx": 22, "xxxx": 22, "xxxx_test": 53, "xxxxx": 22, "xy": [36, 89], "x\uac12\uacfc": 80, "x\uc88c\ud45c\uac00": 76, "y": [8, 15, 20, 22, 23, 24, 25, 28, 29, 30, 32, 33, 35, 36, 39, 41, 42, 44, 45, 47, 48, 49, 50, 51, 52, 55, 56, 58, 59, 62, 66, 68, 69, 70, 72, 73, 77, 78, 80, 81, 84, 85, 87, 88, 89], "y1": [24, 30, 45], "y2": [24, 30, 45], "y3": [24, 30], "y_offset": 35, "y_scale": 48, "ye": [65, 67, 85], "yeah": 71, "year": [63, 74, 84], "yellow": 21, "yellow1": 21, "yellow2": 21, "yellow3": 21, "yellow4": 21, "yellowgreen": 21, "yet": [29, 48, 65, 84, 88], "you": [9, 10, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 41, 42, 44, 46, 50, 51, 54, 56, 57, 58, 59, 60, 61, 63, 64, 65, 67, 68, 71, 72, 74, 85, 86, 87, 88, 89], "your": [15, 16, 18, 19, 22, 23, 25, 26, 27, 28, 30, 31, 33, 37, 38, 44, 46, 54, 56, 57, 58, 59, 60, 61, 63, 65, 67, 70, 85, 86, 87, 88, 89], "yourself": [28, 50, 58, 63, 65, 86, 87], "yup": 84, "yuv": [18, 41, 57], "yuy2_overlai": 41, "yv12_overlai": 41, "yvyu_overlai": 41, "y\uac12\uc744": 80, "y\uc131\ubd84\uc744": 76, "y\uc88c\ud45c\uac00": 76, "z": [33, 36, 87, 89], "zero": [19, 20, 24, 28, 29, 36, 37, 38, 45, 51, 59, 64, 65, 89], "zip": [16, 84], "zl": 32, "zoom": 26, "zr": 32, "\uac00": [76, 77, 81], "\uac00\ub2a5": 80, "\uac00\ub3c5\uc131\uc744": 76, "\uac00\ubcf4\uc790": 78, "\uac00\uc18d": 77, "\uac00\uc7a5": [76, 79, 82], "\uac00\uc815\ud558\uba74": 76, "\uac00\uc815\ud574": 75, "\uac00\uc9c0\uace0": [75, 78, 81], "\uac00\uc9c0\uae30": 76, "\uac00\uc9c0\ub294": 76, "\uac00\uc9c0\ubbc0\ub85c": [76, 79], "\uac00\uc9c4\ub2e4": [76, 80], "\uac00\uc9c4\ub2e4\ub294": 75, "\uac00\uc9c8": 82, "\uac01\uac01": 79, "\uac01\uac01\uc758": 79, "\uac04\ub2e8\ud558\ub2e4": 81, "\uac10\uc18c\uc2dc\ud0a4\ub294": 80, "\uac10\uc18c\ud55c\ub2e4": 81, "\uac12": 79, "\uac12\ub9cc\uc774": 79, "\uac12\uc740": [76, 79], "\uac12\uc744": [76, 79, 80], "\uac12\uc774": 79, "\uac12\uc774\uace0": 79, "\uac16\ub294": [81, 82], "\uac19\ub2e4": [75, 76, 82], "\uac19\uc544": [77, 81], "\uac19\uc740": [76, 77, 82], "\uac19\uc74c": 81, "\uac19\uc774": [76, 79], "\uac1c\ub150\uc740": 82, "\uac1c\ub150\uc744": 77, "\uac1c\ub150\uc774\ub2e4": 82, "\uac1c\ubc1c\uc790\uac00": 75, "\uac1c\uc120\uc758": 81, "\uac1c\uc218\ub97c": 81, "\uac1c\uc218\ub9cc\ud07c": 79, "\uac1d\uccb4": [76, 78], "\uac1d\uccb4\uc5d0": 76, "\uac1d\uccb4\uc758": 76, "\uac70\uc758": [75, 76], "\uac71\uc815\ud558\uc9c0": 82, "\uac78\ub9ac\ub294": 76, "\uac80\uc740": [79, 81], "\uac80\uc740\ube14\ub85d": 15, "\uac83": [75, 76, 78], "\uac83\uacfc": [80, 82], "\uac83\ub4e4\uc774": 81, "\uac83\ub4e4\uc774\ub2e4": 76, "\uac83\ubcf4\ub2e4": 79, "\uac83\uc5d0": [75, 77, 78], "\uac83\uc5d0\ub9cc": 75, "\uac83\uc740": [76, 77, 78, 79, 80, 82], "\uac83\uc744": 79, "\uac83\uc774": [75, 76, 79, 80], "\uac83\uc774\uae30": 78, "\uac83\uc774\ub2e4": [75, 76, 77, 78, 79, 80, 81, 82], "\uac83\uc778\uac00": 80, "\uac83\ucc98\ub7fc": [77, 80], "\uac8c\uc784": [75, 76, 77, 78, 81, 82], "\uac8c\uc784\ub3c4": [75, 82], "\uac8c\uc784\ub9cc\uc758": 82, "\uac8c\uc784\uc5d0\uc11c\uc758": 79, "\uac8c\uc784\uc5d0\uc120": 77, "\uac8c\uc784\uc5d4\uc9c4": 75, "\uac8c\uc784\uc5d4\uc9c4\uc774\ub098": 75, "\uac8c\uc784\uc740": [75, 78, 81], "\uac8c\uc784\uc744": [75, 78, 82], "\uac8c\uc784\uc758": [75, 78], "\uac8c\uc784\uc774": [75, 77, 78, 80, 81, 82], "\uac8c\uc784\uc774\ub098": 82, "\uac8c\uc784\uc774\ub2e4": 81, "\uac8c\uc784\uc774\ub77c\uace0": 78, "\uac8c\uc784\uc774\ubbc0\ub85c": 76, "\uac8c\uc784\ud310": 83, "\uac8c\uc784\ud310\uc740": 81, "\uac8c\uc784\ud310\uc744": 81, "\uacaa\uc5b4": 82, "\uacb0\uacfc": [76, 77, 78], "\uacb0\uacfc\uac00": 77, "\uacb0\uacfc\ub294": [78, 81], "\uacb0\uacfc\ub97c": 77, "\uacb0\uacfc\ubb3c\ub4e4\uc744": 76, "\uacb0\uacfc\ubb3c\uc740": 76, "\uacb0\ub860\uc774": 82, "\uacb0\uc815\ud560": 76, "\uacbd\uc6b0": [76, 78, 79, 80], "\uacbd\uc6b0\ub97c": 78, "\uacbd\uc6b0\uc758": 82, "\uacbd\ud5d8\uc774": 78, "\uacc4\uc0b0\ub9cc": 76, "\uacc4\uc0b0\uc744": 80, "\uacc4\uc0b0\ud558\uae30": 77, "\uacc4\uc0b0\ud574\uc57c": 81, "\uacc4\uc18d": [75, 76, 78], "\uace0": 81, "\uace0\uae09": 75, "\uace0\ub824\ub418\uc5c8\uc744": 82, "\uace0\ub824\ub418\uc9c0": 77, "\uace0\ub824\ud558\uc5ec": 76, "\uace0\ub974\ub294": 81, "\uace0\uc815": 77, "\uace0\uc815\ub418\uc5b4": [76, 79], "\uace0\uc815\ub41c": 76, "\uace0\uc815\ub420": 77, "\uace0\uc815\uc2dc\ucf1c": 77, "\uace0\uc815\uc2dc\ud0a4\ub294": 77, "\uace0\uc815\uc2dc\ud0a8\ub2e4": 76, "\uace0\uc815\ud558\ub294": 77, "\uacf5\uac04": 76, "\uacf5\uac04\uc0c1\uc5d0\uc11c\uc758": 75, "\uacf5\uac04\uc744": [76, 80], "\uacf5\uc774\ub098": 77, "\uacfc": [76, 80], "\uad00\ub828": 75, "\uad6c\uc131": 76, "\uad6c\uc131\ub41c\ub2e4": 75, "\uad6c\uc131\ud560": 75, "\uad6c\uc2dd\uc774\uace0": 75, "\uad6c\uc5ed\uc744": 76, "\uad6c\uc5ed\uc758": 76, "\uad6c\uccb4\uc801\uc778": [78, 80], "\uad6c\ud604\ud55c": [81, 82], "\uad6c\ud604\ud55c\ub2e4\uba74": 81, "\uad6c\ud604\ud560": 82, "\uaddc\uce59": [78, 82], "\uaddc\uce59\uc740": 81, "\uaddc\uce59\uc744": [81, 82], "\uaddc\uce59\uc774": 82, "\uadf8": [75, 76, 77, 79, 80, 81], "\uadf8\uac83\uc740": 79, "\uadf8\uac83\uc774": [78, 82], "\uadf8\uac83\uc774\ub2e4": 76, "\uadf8\ub2e4\uc9c0": 76, "\uadf8\ub798\uc11c": [75, 76, 77], "\uadf8\ub798\ud53d": 75, "\uadf8\ub798\ud53d\uc744": 75, "\uadf8\ub7ec\ub098": [77, 78, 82], "\uadf8\ub7ec\uba74": [75, 77, 81], "\uadf8\ub7ec\ubbc0\ub85c": [76, 77, 80, 82], "\uadf8\ub7f0\uac00": 78, "\uadf8\ub807\ub2e4\uba74": [76, 77], "\uadf8\ub807\uc9c0": 80, "\uadf8\ub824\uc9c0\uace0": 76, "\uadf8\ub824\uc9c0\ub294": 76, "\uadf8\ub824\uc9c4\ub2e4": 81, "\uadf8\ub9ac\uace0": [75, 76, 77], "\uadf8\ub9ac\uae30": 75, "\uadf8\ub9ac\ub294": [76, 79], "\uadf8\ub9b0\ub2e4": [79, 80], "\uadf9\uc18c\uc218\ub9cc\uc744": 82, "\uae30\ub2a5": 77, "\uae30\ub2a5\uc744": [76, 77], "\uae30\ub85d\ud574\uc57c": 80, "\uae30\ubc18": 76, "\uae30\ubc18\uc73c\ub85c": 76, "\uae30\ubc18\ud558\uc600\uae30": 75, "\uae30\uc874\uc758": 82, "\uae38\uc774\ub97c": 76, "\uae4c\uba39\uc5b4\uc120": 76, "\uae5c\ube61\uac70\ub9ac\ub294": 75, "\uaf64": 76, "\ub049\uaca8": 75, "\ub05d\ub09c": 76, "\ub05d\uc774": 82, "\ub098": 81, "\ub098\uac8c": 82, "\ub098\ub220\uc9c8": 76, "\ub098\ub294": 78, "\ub098\uba74": 76, "\ub098\uc544\uc84c\ub2e4": 79, "\ub098\uc911\uc5d0": [76, 77, 78], "\ub098\ud0c0\ub0b8\ub2e4": 80, "\ub09c\ub2e4": 82, "\ub09c\uc218": 82, "\ub09c\uc218\uac00": 82, "\ub09c\uc218\uae4c\uc9c0": 82, "\ub0ab\ub2e4": 75, "\ub0b4\ubd80": [76, 78], "\ub0b4\ubd80\ub97c": 80, "\ub0b4\ubd80\uc5d0\uc11c": 80, "\ub0b4\ubd80\uc600\ub2e4\uba74": 80, "\ub0b4\uc5d0\uc11c": [77, 81, 82], "\ub0b4\uc6a9": 76, "\ub0b4\uc6a9\uacfc": 76, "\ub0b4\uc6a9\uc740": 80, "\ub108\ubb34": 77, "\ub118\uac8c": 82, "\ub123\ub294\ub2e4\uba74": 81, "\ub123\uc73c\uba74": 81, "\ub124\ubc88\uc9f8": 79, "\ub192\ub2e4\ub294": 75, "\ub192\uc774\uae30": 76, "\ub204\ub974\ub294": 77, "\ub204\ub974\uba74": 78, "\ub208\uc0ac\ud0dc": 82, "\ub208\uc0ac\ud0dc\ub97c": 82, "\ub20c\ub7ec": 79, "\ub20c\ub824\uc788\ub294": 78, "\ub20c\ub838\ub2e4": 78, "\ub20c\ub9ac\uc9c0": 78, "\ub20c\ub9b0": 78, "\ub294": 76, "\ub2a5\ub825\uc774\ub2e4": 82, "\ub2e4\ub8e8\uaca0\ub2e4": 78, "\ub2e4\ub8e8\uae30": 78, "\ub2e4\ub918\ub2e4": 82, "\ub2e4\ub974\uac8c": 78, "\ub2e4\ub974\ub2e4": 77, "\ub2e4\ub974\uc9c0": 79, "\ub2e4\ub978": [76, 78, 82], "\ub2e4\uc2dc": 79, "\ub2e4\uc591\uc131": 78, "\ub2e4\uc591\ud788": 81, "\ub2e4\uc74c": [76, 79, 80, 81], "\ub2e4\uc911": 77, "\ub2e4\ud589\ud788": 75, "\ub2e8": 77, "\ub2e8\uacc4\ub85c": 78, "\ub2e8\uacc4\ub97c": 76, "\ub2e8\uacc4\uc5d0\uc11c\uc758": 78, "\ub2e8\uc0c9": 76, "\ub2e8\uc21c\ud558\uae30": 81, "\ub2e8\uc21c\ud558\ub2e4": [79, 80], "\ub2e8\uc21c\ud558\uc9c0\ub9cc": 81, "\ub2e8\uc21c\ud788": [78, 81], "\ub2e8\uc5b4\ub294": 78, "\ub2e8\uc810\uc740": 77, "\ub2e8\uc810\uc774": 75, "\ub2ec\ub77c\uc9c0\ubbc0\ub85c": 77, "\ub2ec\ub77c\uc9c4\ub2e4": 81, "\ub2ec\ub77c\uc9c8": 78, "\ub2f4\uc558\ub294\ub370": 79, "\ub2f9\uc5f0\ud55c": 76, "\ub300\ub2e8\ud788": [81, 82], "\ub300\ub85c": 81, "\ub300\ubcf4\uac8c": 75, "\ub300\uc0c1\uc5d0": 82, "\ub300\uccb4\ud558\ub294\uac00": 76, "\ub300\ud55c": 76, "\ub354": [75, 78, 79, 82], "\ub370": 76, "\ub370\uc5d0\ub9cc": 80, "\ub370\uc774\ud130": [78, 81], "\ub370\uc774\ud130\uac00": 76, "\ub370\uc774\ud130\ub4e4\uc744": 79, "\ub370\uc774\ud130\ub97c": 79, "\ub3c4": [76, 78], "\ub3c4\uc6c0": 78, "\ub3c4\uc6c0\uc774": 78, "\ub3c4\uc911": 76, "\ub3c4\ud615": 75, "\ub3c4\ud615\uc744": 79, "\ub3d9\uae30\ubd80\uc5ec": 78, "\ub3d9\uc2dc\uc5d0": 75, "\ub3d9\uc77c\ud55c": [76, 79, 80], "\ub3d9\uc77c\ud574\uc57c": 80, "\ub3d9\uc791\ud558\ub3c4\ub85d": 78, "\ub3d9\uce58\uad00\uacc4\ub77c\ub294": 75, "\ub418\uae30": 78, "\ub418\ub3cc\uc544\uac00\uc57c": 76, "\ub418\uc5b4\uc57c\ub9cc": 77, "\ub418\uc5c8\ub294\uc9c0": 78, "\ub418\uc9c0": 79, "\ub41c": [78, 80], "\ub41c\ub2e4": [75, 76, 77, 78, 79, 80, 81, 82], "\ub41c\ub2e4\uba74": 82, "\ub420": [75, 77, 79, 80], "\ub450": [79, 80, 81, 82], "\ub450\uaed8": 79, "\ub450\uaed8\ub9cc": 80, "\ub450\ub294": [79, 80], "\ub450\ub294\ub370": 76, "\ub450\uba74": 77, "\ub450\ubc88\uc9f8": [78, 79], "\ub450\uc5c8\ub2e4": 76, "\ub458": 75, "\ub458\uc9f8": [75, 80], "\ub4a4\uc5d0": 78, "\ub4b7\ubd80\ubd84\uc5d0": 77, "\ub4e4\uba74": 79, "\ub4e4\uc5b4\uc11c": 76, "\ub4e4\uc744": 80, "\ub4f1": [75, 78], "\ub4f1\uc740": 82, "\ub4f1\uc758": 75, "\ub514\ub809\ud1a0\ub9ac\uc5d0": 76, "\ub514\uc2a4\ud50c\ub808\uc774": 75, "\ub514\uc790\uc778\ud560": 79, "\ub51c\ub808\ub9c8\uac00": 75, "\ub51c\ub808\ub9c8\ub97c": 75, "\ub51c\ub808\uc774": 77, "\ub530\ub77c": [77, 81], "\ub530\uc62c": 79, "\ub530\uc838\uc11c": 79, "\ub54c": [76, 77, 79, 81, 82], "\ub54c\ub9c8\ub2e4": 79, "\ub54c\ub9cc": 76, "\ub54c\ubb38\uc5d0": [75, 76, 77, 78, 81], "\ub54c\ubb38\uc774\ub2e4": [76, 77, 78, 80, 81], "\ub54c\uc758": 79, "\ub610\ub294": [75, 79, 82], "\ub610\ud55c": [77, 81], "\ub611\uac19\uc740": 78, "\ub73b\uc774\ub2e4": 78, "\ub77c\uace0": [78, 82], "\ub77c\ub294": [76, 82], "\ub77c\uc774\ube0c\ub7ec\ub9ac\uc774\uae30": 75, "\ub77c\uc774\ube0c\ub7ec\ub9ac\uc774\ub2e4": 75, "\ub80c\ub354\ub9c1": 79, "\ub85c\uc9c1\uc740": 79, "\ub85c\uc9c1\uc744": [76, 78], "\ub85c\uc9c1\uc774": 77, "\ub8e8\ud2b82": 77, "\ub97c": [76, 77, 78, 80], "\ub9c8\ub77c": 82, "\ub9c8\uc6b0\uc2a4": [75, 78, 80], "\ub9c8\uc6b0\uc2a4\uac00": 80, "\ub9c8\uc9c0\ub9c9\uc5d0": 76, "\ub9c8\uc9c0\ub9c9\uc73c\ub85c": [78, 79], "\ub9c8\ucc2c\uac00\uc9c0\uc774\ub2e4": 82, "\ub9cc": 75, "\ub9cc\ub4dc\ub294": [75, 80, 82], "\ub9cc\ub4e0": 75, "\ub9cc\ub4e0\ub2e4\uace0": 75, "\ub9cc\ub4e4": [75, 79, 82], "\ub9cc\ub4e4\uace0": 81, "\ub9cc\ub4e4\uc5b4": [79, 81], "\ub9cc\ub4e4\uc5b4\uc11c": 82, "\ub9cc\ub4e4\uc5b4\uc57c": 80, "\ub9cc\ub4e4\uc5b4\uc9c4": 81, "\ub9cc\ub4e4\uc5b4\uc9c4\ub2e4\uba74": 82, "\ub9cc\ub4e4\uc5c8\ub2e4": 79, "\ub9cc\uc57d": [76, 79, 80, 82], "\ub9cc\uc744": [76, 79], "\ub9ce\ub2e4": 81, "\ub9ce\uc73c\ubbc0\ub85c": 77, "\ub9ce\uc740": 81, "\ub9d0\uc774\ub2e4": 76, "\ub9d0\ud558\ub294": 78, "\ub9d0\ud55c": 76, "\ub9d0\ud588\ub2e4": 82, "\ub9d0\ud588\ub4ef": 76, "\ub9de\ub294\uac00": 78, "\ub9de\uc744": 76, "\ub9e4\uac1c": 79, "\ub9e4\uc6b0": [78, 82], "\uba39\ud788\ub294": 76, "\uba3c\uc800": [78, 79], "\uba54\ubaa8\ub9ac": 75, "\uba64\ubc84": 76, "\uba85\ub839\uc5b4\uac00": 78, "\uba87": 77, "\uba87\uba87": [76, 78], "\ubaa8\ub2c8\ud130": 76, "\ubaa8\ub450": [75, 76], "\ubaa8\ub4c8\ub4e4\uc744": 76, "\ubaa8\ub4e0": [75, 76, 78, 81, 82], "\ubaa9\ub85d\uc740": 78, "\ubaa9\ud45c\uac00": 75, "\ubab8\uc758": 78, "\ubb34\uc5b8\uac00\ub97c": [76, 78], "\ubb34\uc5c7\uc744": 77, "\ubb34\uc5c7\uc774\ub4e0\uc9c0": 82, "\ubb34\uc5c7\uc778\uac00": [77, 79, 82], "\ubb34\uc5c7\uc778\uc9c0\ub294": 79, "\ubb34\uc791\uc704\ub85c": 81, "\ubb34\ud55c": 76, "\ubb36\uc744": 80, "\ubb38\uad6c\uc774\ub2e4": 76, "\ubb38\uc790\uc5f4\uc740": 76, "\ubb38\uc790\uc5f4\uc774\ub2e4": 76, "\ubb38\uc7a5\ub4e4": 76, "\ubb38\uc81c\uac00": [76, 77], "\ubb38\uc81c\ub97c": 81, "\ubb3c\ub860": [75, 76, 77, 78, 79], "\ubb54\uac00": 80, "\ubbf8\uce58\uac8c": 82, "\ubc0f": 78, "\ubc14\uafb8\uace0": 77, "\ubc14\uafb8\ub294": 77, "\ubc14\uafb8\ub294\uac00": 79, "\ubc14\uafb8\uba74": 81, "\ubc14\uafbc": 78, "\ubc14\uafc0": 77, "\ubc14\uafd4": 79, "\ubc14\ub00c\ub294\uac00": 77, "\ubc14\ub00c\ub294\uc9c0\ub97c": 77, "\ubc14\ub294": 79, "\ubc14\ub85c": [77, 82], "\ubc18\ub4dc\uc2dc": [77, 78], "\ubc18\ubcf5\ubb38": 76, "\ubc18\uc601": 78, "\ubc18\ud544\uc218\uc801\uc73c\ub85c": 76, "\ubc18\ud658": 76, "\ubc18\ud658\ud55c\ub2e4": [76, 81], "\ubc1c\uc0dd": 76, "\ubc1c\uc0dd\ud558\uba74": 76, "\ubc1c\uc0dd\ud55c": 76, "\ubc1c\uc804\ub41c": 75, "\ubc1c\ud718\ub41c": 82, "\ubc29\ubc95": 78, "\ubc29\ubc95\uc740": [78, 79], "\ubc29\ubc95\uc744": [77, 78], "\ubc29\ubc95\uc774\ub2e4": 79, "\ubc29\uc2dd": 75, "\ubc29\ud5a5\uc73c\ub85c": 78, "\ubc29\ud5a5\ud0a4\ub97c": 78, "\ubc30\uc5f4\uacfc": 81, "\ubc30\uc5f4\uc5d0\uc11c": 81, "\ubc30\uc5f4\uc740": 81, "\ubc30\uc5f4\uc744": [76, 81], "\ubc30\uc5f4\uc774": 81, "\ubc30\uc5f4\ucc98\ub7fc": 81, "\ubc30\uc6b0\uace0": [78, 82], "\ubc30\uc6b0\ub294": [75, 76], "\ubc30\uc6b4\ub2e4": 78, "\ubc30\ud2c0\uc2ed": 75, "\ubc84\ud2bc": 81, "\ubc84\ud2bc\ub4e4": 83, "\ubc84\ud2bc\ub4e4\uc740": 80, "\ubc84\ud2bc\ub4e4\uc744": 80, "\ubc84\ud2bc\uc744": [77, 80, 81], "\ubc84\ud2bc\uc758": 80, "\ubc88": 77, "\ubc94\uc704\ub294": 80, "\ubc95\uc744": 81, "\ubcc0\uacbd\ud558\uba74\uc11c": 79, "\ubcc0\uc218": [76, 79, 80], "\ubcc0\uc218\uac00": [76, 77, 79, 80], "\ubcc0\uc218\ub294": [76, 79], "\ubcc0\uc218\ub3c4": 80, "\ubcc0\uc218\ub4e4\uc744": 80, "\ubcc0\uc218\ub4e4\uc774": 76, "\ubcc0\uc218\ub85c": [79, 80], "\ubcc0\uc218\ub97c": [76, 77, 79, 80], "\ubcc0\uc218\uc640": 78, "\ubcc0\uc218\uc758": 79, "\ubcc0\uc218\uc774\uace0": 79, "\ubcc0\uc218\uc774\ub2e4": 79, "\ubcc0\uc704\uac00": 77, "\ubcc0\uc704\ub294": 77, "\ubcc0\uc704\ub9cc": 77, "\ubcc0\ud55c\ub2e4\uba74": 79, "\ubcc0\ud560": 79, "\ubcf4\uace0": 75, "\ubcf4\ub2e4": 77, "\ubcf4\uba74": [77, 80], "\ubcf4\uc544\ub77c": [78, 79, 82], "\ubcf4\uc774\ub294": 80, "\ubcf4\uc774\uc9c4": 81, "\ubcf4\uc778\ub2e4": 77, "\ubcf4\uc778\ub2e4\ub294": [75, 77], "\ubcf4\uc77c": 77, "\ubcf4\uc790": [75, 82], "\ubcf4\ud1b5": 75, "\ubcf5\uc18c\uc218\uc88c\ud45c\ub97c": 75, "\ubcf5\uc7a1\ub3c4\ub294": 77, "\ubcf5\uc7a1\ub3c4\ub97c": 77, "\ubcf5\uc7a1\ud558\ub2e4": 76, "\ubcf5\uc7a1\ud55c": 75, "\ubcf8\uaca9\uc801\uc73c\ub85c": 79, "\ubcfc\ub9cc": 75, "\ubd80": 83, "\ubd80\ubd84\uc5d0\uc11c": 78, "\ubd80\ubd84\uc73c\ub85c": 76, "\ubd80\ubd84\uc758": 81, "\ubd80\uc5ec\ud560": 82, "\ubd84\uc11d\ud558\uc9c0": 77, "\ubd88\uacfc\ud558\ub2e4": 78, "\ubd88\uacfc\ud558\ubbc0\ub85c": 77, "\ubd88\ub9b4": 78, "\ubd88\uc5f0\uc18d\uc801": 79, "\ube14\ub85d": 81, "\ube14\ub85d\uc744": 81, "\ube14\ub85d\uc758": 81, "\ube44\uad50\ud558\uc5ec": 78, "\ube44\ud45c\uc900": 75, "\ube44\ud558\uba74": 76, "\ube48\ub3c4\uc5d0": 77, "\ube60\ub978": 75, "\ube60\ub97c\uae4c": 77, "\ube60\uc9c0\uba74": 75, "\ube68\uac04": [76, 79, 81], "\ube68\uac04\ube14\ub85d": 15, "\ube68\ub9ac": 75, "\ubfcc\uc694\ubfcc\uc694": 75, "\uc0ac\ub78c\ub4e4\uc774": 82, "\uc0ac\ub78c\uc774": 82, "\uc0ac\uc2e4": 81, "\uc0ac\uc6a9\ub418\uc5c8\uae30": 80, "\uc0ac\uc6a9\ub418\uc5c8\uc9c0\ub9cc": 80, "\uc0ac\uc6a9\ub41c": 79, "\uc0ac\uc6a9\uc790\uac00": [76, 80], "\uc0ac\uc6a9\ud558\uae30": 76, "\uc0ac\uc6a9\ud558\ub294": 76, "\uc0ac\uc6a9\ud558\uc5ec": 76, "\uc0ac\uc6a9\ud560": 76, "\uc0ac\uc774\uc5d0\ub294": 75, "\uc0ac\uc774\uc758": 75, "\uc0ac\uc9c4": 75, "\uc0b4\ud3b4\ubcf4\uc790": 76, "\uc0bd\uc785\ud558\uba74": 76, "\uc0c1\uc138\ud558\uac8c": 79, "\uc0c1\uc218": 80, "\uc0c1\uc218\ub4e4\uc744": 76, "\uc0c1\ud0dc": 78, "\uc0c1\ud0dc\ub97c": 75, "\uc0c1\ud638\uc791\uc6a9\uc774": 78, "\uc0c8\ub85c\uc6b4": [79, 81, 82], "\uc0c9": [75, 76, 79], "\uc0c9\uc0c1": [76, 79], "\uc0c9\uc0c1\uacfc": 76, "\uc0c9\uc0c1\uc740": [76, 81], "\uc0c9\uc0c1\uc744": [76, 81], "\uc0c9\uc0c1\uc758": 81, "\uc0c9\uc774": 76, "\uc0dd\uac01\uc77c": 80, "\uc0dd\uac01\ud574": 82, "\uc0dd\uac01\ud574\ubcf4\uba74": 78, "\uc0dd\uac01\ud574\ubd10\ub77c": 78, "\uc0dd\uacbc\ub2e4": 78, "\uc0dd\uae38": 76, "\uc0dd\uc131\ud558\uace0": 76, "\uc11c\ub85c": 76, "\uc120\uc5b8": 76, "\uc120\uc5b8\ub418\uc5b4\uc57c": 76, "\uc120\ud0dd\uc801\uc73c\ub85c": 75, "\uc120\ud0dd\uc801\uc778": 77, "\uc120\ud0dd\uc9c0\ub294": 81, "\uc120\ud589\ub418\uc5b4\uc57c": 79, "\uc124\uba85\uc774": 76, "\uc124\uba85\uc774\uc5c8\ub2e4": 76, "\uc124\uba85\ud558\ub294": 79, "\uc124\uba85\ud560": 81, "\uc124\uc815": 75, "\uc131\ubd84": 76, "\uc131\ubd84\uc774": 76, "\uc138\uace0": 81, "\uc138\ud305": 78, "\uc148\uc774\ub2e4": 76, "\uc18c\uac1c": 83, "\uc18c\ub9ac": [75, 78], "\uc18c\uc124": 82, "\uc18c\uc2a4": [75, 76, 77, 78], "\uc18c\uc2a4\ucf54\ub4dc": 76, "\uc18c\uc2a4\ucf54\ub4dc\uac00": 76, "\uc18c\uc2a4\ucf54\ub4dc\ub294": 76, "\uc18c\uc2a4\ucf54\ub4dc\ub97c": [75, 76], "\uc18c\uc2a4\ucf54\ub4dc\uc5d0": 76, "\uc18c\uc2a4\ucf54\ub4dc\uc640": 75, "\uc18c\uc2a4\ucf54\ub4dc\uc758": 76, "\uc18c\uc2a4\ud30c\uc77c\uc5d0": 75, "\uc18c\uc2a4\ud30c\uc77c\uc740": 75, "\uc18c\uc9c0\uac00": 80, "\uc18d": 76, "\uc18d\ub3c4\ub77c\ub294": 77, "\uc18d\ub3c4\ub97c": 77, "\uc18d\ub3c4\uc5d0": 77, "\uc18d\uc5d0\ub294": 77, "\uc190\uac00\ub77d": 78, "\uc190\uc744": 75, "\uc218": [75, 76, 77, 78, 79, 80, 81, 82], "\uc218\uac00": 82, "\uc218\ub294": [77, 82], "\uc218\ub3c4": 82, "\uc218\ub97c": 81, "\uc218\ub9ce\uc740": 75, "\uc218\uc815\ud558\ub294": 76, "\uc218\uc815\ud55c\ub2e4\uba74": 76, "\uc218\uc900": 75, "\uc218\ud589\ub418\uc5b4\uc57c": 76, "\uc218\ud589\ud55c\ub2e4": 76, "\uc21c\ucc28\uc801\uc73c\ub85c": 76, "\uc26c\uc6b4": 79, "\uc27d\uac8c": [77, 79], "\uc27d\ub2e4": [77, 78, 80], "\uc2a4\ud06c\ub9b0": 76, "\uc2b5\ub4dd\ud560": 82, "\uc2dc\uac01\uc801": 81, "\uc2dc\uac01\ud654": [78, 79], "\uc2dc\uac04": [77, 78, 81], "\uc2dc\uac04\ubcf4\ub2e4": 82, "\uc2dc\uac04\uc21c\uc73c\ub85c": 76, "\uc2dc\uac04\uc740": 77, "\uc2dc\uac04\uc744": [77, 82], "\uc2dc\uac04\uc774": [76, 82], "\uc2dc\uac04\uc774\ub2e4": 82, "\uc2dc\ub3c4\ud574": 75, "\uc2dc\uc2a4\ud15c\uc744": 78, "\uc2dc\uc2a4\ud15c\uc774": 76, "\uc2dc\uc791\ub418\uae30": 77, "\uc2dc\uc791\ub420": 77, "\uc2dc\uc791\ud558\ub294": 77, "\uc2dc\ud589": 82, "\uc2dd\uc758": 75, "\uc2e0\uacbd": 80, "\uc2e4\uc81c\ub85c": 76, "\uc2e4\ud589": [76, 77, 78, 81], "\uc2e4\ud589\ub418\uac70\ub098": 75, "\uc2e4\ud589\ub418\ub294": [75, 77], "\uc2e4\ud589\ub418\uba74": 76, "\uc2e4\ud589\ub418\uc57c": 77, "\uc2e4\ud589\ub418\uc5b4\uc57c": 76, "\uc2e4\ud589\ub418\uc9c0": 76, "\uc2e4\ud589\ub41c\ub2e4": 76, "\uc2e4\ud589\ub41c\ub2e4\ub294": 75, "\uc2e4\ud589\ub428": 75, "\uc2e4\ud589\ud558\ub294": 75, "\uc2eb\ub2e4\uba74": 80, "\uc2ec\ud654": 78, "\uc2ec\ud654\ub41c": 78, "\uc2f6\ub2e4\uba74": 75, "\uc2f6\uc744": 76, "\uc368\uc57c": 80, "\uc4f0\uba74": 76, "\uc4f8": 75, "\uc544\ub2c8\ub2e4": 78, "\uc544\ub2c8\ub77c": 77, "\uc544\ub2c8\ubbc0\ub85c": 77, "\uc544\ub2c8\uc9c0\ub9cc": 78, "\uc544\ub2cc": [75, 76, 77, 79, 80, 81], "\uc544\ub2cc\uc9c0\ub97c": 78, "\uc544\ub798\ub97c": 79, "\uc544\ub798\uc640": 79, "\uc544\ub798\ucabd\uc774": 76, "\uc544\ub9c8\ub3c4": 78, "\uc544\ubb34": 82, "\uc544\ubb34\ub798\ub3c4": 75, "\uc544\uc2a4\ud0a4\uc544\ud2b8\ub97c": 75, "\uc544\uc774\ub514\uc5b4\ub294": 79, "\uc544\uc774\ub514\uc5b4\ub97c": 79, "\uc544\uc774\ub514\uc5b4\uc640": 79, "\uc544\uc774\ub514\uc5b4\ucc98\ub7fc": 80, "\uc544\uc9c1": [79, 80], "\uc544\uc9c1\ub3c4": [77, 80], "\uc548\ub418\ub294": 77, "\uc548\ub41c\ub2e4": 76, "\uc54a\ub294": [76, 77], "\uc54a\ub294\ub2e4": [77, 78, 79, 81], "\uc54a\ub2e4": 79, "\uc54a\ub2e4\uba74": 80, "\uc54a\uc558\uc9c0\ub9cc": 78, "\uc54a\uc73c\uba74": 76, "\uc54a\uc744": [76, 77], "\uc54c": [76, 80, 81], "\uc54c\uace0": [77, 78], "\uc54c\uace0\ub9ac\uc998\uc73c\ub85c": 75, "\uc54c\uace0\ub9ac\uc998\uc758": 78, "\uc54c\uace0\ub9ac\uc998\uc774": 78, "\uc54c\ub809\uc138\uc774": 82, "\uc54c\uc544\ub0b4\uae30": 80, "\uc54c\uc544\ub0bc": [76, 77], "\uc54c\uc544\uc57c": 78, "\uc54c\uc558\ub2e4": 77, "\uc54c\uce74\ub178\uc774\ub4dc\uc758": 77, "\uc55e\ubd80\ubd84\uc5d0": 77, "\uc55e\uc11c": 76, "\uc55e\uc11c\uc11c": 76, "\uc55e\uc5d0": 78, "\uc560\ub2c8\uba54\uc774\uc158": [75, 77], "\uc57d\uac04": 78, "\uc57d\uac04\uc758": 76, "\uc5b4\ub5a4": [76, 82], "\uc5b4\ub5a4\uac00": 79, "\uc5b4\ub5a8\uae4c": [76, 80], "\uc5b4\ub5bb\uac8c": [76, 77, 79, 80, 81], "\uc5b4\ub835\uc9c0": 76, "\uc5b4\uca0c\ub4e0": 78, "\uc5b4\ucc0c\ub410\ub4e0": 76, "\uc5b8\uae09\ud558\uaca0\ub2e4": 76, "\uc5b8\uae09\ud55c": 81, "\uc5b8\uc5b4\uc758": 76, "\uc5bc\ub9c8\ub098": 77, "\uc5bc\ub9cc\ud07c\uc758": 82, "\uc5c5\ub370\uc774\ud2b8": 77, "\uc5c5\ub370\uc774\ud2b8\uac00": 76, "\uc5c5\ub370\uc774\ud2b8\ub418\uac70\ub098": 76, "\uc5c5\ub370\uc774\ud2b8\ub418\uac8c": 77, "\uc5c5\ub370\uc774\ud2b8\ub418\uc5c8\ub294\uc9c0\ub97c": 77, "\uc5c5\ub370\uc774\ud2b8\ub41c\ub2e4": 78, "\uc5c5\ub370\uc774\ud2b8\ud558\ub294": [77, 78, 80], "\uc5c6\uace0": [78, 80], "\uc5c6\uae30": [76, 77, 78], "\uc5c6\ub294": [75, 76, 82], "\uc5c6\ub2e4": [77, 78, 81], "\uc5c6\uc73c\ubbc0\ub85c": [77, 80], "\uc5c6\uc774": [76, 78], "\uc5d0": 76, "\uc5d0\uc11c": [78, 82], "\uc5d0\uc120": 76, "\uc5d0\ud544\ub85c\uadf8": 83, "\uc5d4\uc9c4": 75, "\uc5d4\uc9c4\uc5d0\ub3c4": 75, "\uc5d4\uc9c4\uc740": 75, "\uc5d4\uc9c4\uc744": 75, "\uc5d4\uc9c4\uc758": 75, "\uc5d4\ud130": 75, "\uc5ec\uae30\uc11c": 77, "\uc5ec\uae30\uc5d0": 76, "\uc5ec\uae30\uc5d0\uc11c": 82, "\uc5ec\ub7ec": 75, "\uc5ec\ub7ec\uac00\uc9c0": 76, "\uc5ec\uc804\ud788": 79, "\uc5ec\uc9c0\uac00": 81, "\uc5f0\uacb0\uc2dc\ud0a4\uba74\uc11c": 82, "\uc5f0\uacb0\uc810\uc774": 75, "\uc601\uc5ed": 80, "\uc601\uc5ed\uacfc": 80, "\uc601\uc5ed\uc744": 80, "\uc601\uc5ed\uc774": 80, "\uc601\ud5a5\uc744": 82, "\uc601\ud654": 82, "\uc608\ub97c": [76, 79], "\uc608\uc2dc": 75, "\uc608\uc2dc\uc774\ub2e4": 82, "\uc608\uc678\uc801\uc73c\ub85c": 76, "\uc608\uc81c\uc778": 76, "\uc608\uce21\ub418\uae30": 77, "\uc624\uac8c": 76, "\uc624\uac8c\ub054": 76, "\uc624\ub2f5\uc774\ub77c\uba74": 81, "\uc624\ub2f5\uc77c": 81, "\uc624\ub798": 76, "\uc624\ub978\ucabd\uc774": 76, "\uc624\ube0c\uc81d\ud2b8\uac04": 78, "\uc624\ube0c\uc81d\ud2b8\uc758": 77, "\uc624\uc9c1": [79, 80], "\uc624\ud574\uc758": 80, "\uc640": [76, 77, 78, 80, 82], "\uc640\uc57c": 77, "\uc644\ubcbd\ud788": 82, "\uc644\uc131\ub418\uc5c8\ub2e4": 78, "\uc644\uc804\ud55c": 80, "\uc644\uc804\ud788": 78, "\uc65c": 78, "\uc65c\ub098\ud558\uba74": 76, "\uc65c\ub0d0\uba74": 78, "\uc65c\ub0d0\ud558\uba74": [77, 78], "\uc678\ubd80": 75, "\uc678\uc758": 76, "\uc694\uc18c": [78, 81], "\uc694\uc18c\ub97c": 76, "\uc694\uc57d\ud558\uc790\uba74": 75, "\uc6a9": 80, "\uc6a9\ub3c4\uac00": 79, "\uc6a9\uc774\ub2e4": 81, "\uc6b0\ub9ac\uac00": 77, "\uc6b0\ub9ac\ub294": [77, 78, 81, 82], "\uc6b0\ub9ac\uc758": 82, "\uc6b0\uc120": [76, 77, 78, 79, 81], "\uc6c0\uc9c1\uc774\uac8c": 77, "\uc6c0\uc9c1\uc774\ub294": 77, "\uc6c0\uc9c1\uc778\ub2e4": [77, 78], "\uc6c0\uc9c1\uc778\ub2e4\ub294": 78, "\uc6c0\uc9c1\uc77c\uae4c": 77, "\uc6d0\ub9ac\ub97c": 76, "\uc704": 79, "\uc704\uce58": [76, 79], "\uc704\uce58\uac00": 77, "\uc704\uce58\ub97c": 76, "\uc704\uce58\uc5d0\uc11c\uc758": 80, "\uc704\ud55c": [75, 76, 80, 81], "\uc704\ud574": [76, 78, 82], "\uc704\ud574\uc11c\ub294": 78, "\uc704\ud574\uc120": 76, "\uc708\ub3c4\uc6b0": 76, "\uc708\ub3c4\uc6b0\uc758": 77, "\uc720\ub2c8\ud2f0": 75, "\uc720\uc6a9\ud55c": 76, "\uc73c\ub85c": [75, 76], "\uc740": 77, "\uc744": [77, 78, 81], "\uc74c\uc545": 82, "\uc758": 75, "\uc758\ubbf8\ub97c": 80, "\uc758\ubbf8\ud558\uac8c": [77, 80], "\uc758\ubbf8\ud558\uace0": 78, "\uc758\ubbf8\ud558\uc9c0\ub294": 78, "\uc758\ubbf8\ud55c\ub2e4": [76, 77, 78], "\uc774": [75, 76, 77, 78, 79, 80, 81, 82], "\uc774\uac83\ub4e4\uc740": 79, "\uc774\uac83\uc740": [76, 77, 81], "\uc774\uac83\uc774": [75, 76, 82], "\uc774\ub294": [77, 78], "\uc774\ub2e4": 76, "\uc774\ub3d9": 83, "\uc774\ub780": 75, "\uc774\ub7f0": [75, 78], "\uc774\ub807\uac8c": 75, "\uc774\ub8e8\uc5b4\uc9c4\ub2e4": [76, 79], "\uc774\ub8e8\uc5b4\uc9d0\uc5d0": 78, "\uc774\ub97c": 77, "\uc774\ub984": 78, "\uc774\ub984\uc73c\ub85c\ub294": 78, "\uc774\ub984\uc744": 78, "\uc774\ub984\uc758": 76, "\uc774\ubbf8": 82, "\uc774\ubbf8\uc9c0": [77, 78, 81], "\uc774\ubca4\ud2b8\uac00": [76, 78], "\uc774\ubca4\ud2b8\ub4e4\uc740": 76, "\uc774\ubca4\ud2b8\ub4e4\uc744": 76, "\uc774\ubca4\ud2b8\ub4e4\uc758": 76, "\uc774\ubca4\ud2b8\ub97c": [76, 78, 80], "\uc774\ubca4\ud2b8\uc801": 75, "\uc774\ubcc4\uc744": 76, "\uc774\uc0c1": [75, 77], "\uc774\uc0c1\uc758": 78, "\uc774\uc5d0": 76, "\uc774\uc6a9\ud558\uba74": 77, "\uc774\uc6a9\ud55c": 75, "\uc774\uc6a9\ud560": 78, "\uc774\uc720\ub294": 80, "\uc774\uc720\uc774\ub2e4": [75, 78, 82], "\uc774\uc804": [77, 78], "\uc774\uc804\uacfc": 79, "\uc774\uc804\ubd80\ud130": 78, "\uc774\uc804\uc5d0\ub294": 78, "\uc774\uc804\uc758": [76, 78, 80], "\uc774\uc81c": [77, 78, 80, 81, 82], "\uc774\uc81c\ub294": 79, "\uc774\uc820": 77, "\uc774\ud574\ud558\ub294": 76, "\uc774\ud574\ud560": [78, 79], "\uc774\ud574\ud588\ub2e4\uba74": 76, "\uc774\ud6c4": 76, "\uc774\ud6c4\uc5d0": 76, "\uc778\uc790\ub97c": 78, "\uc778\ud130\ud398\uc774\uc2a4": 78, "\uc778\ud130\ud398\uc774\uc2a4\ub97c": 81, "\uc77c\ubc18\uc801\uc73c\ub85c": 76, "\uc77c\ubd80": [76, 77, 78], "\uc77c\ubd80\ubd84": 78, "\uc77c\ubd80\uc774\uae30": 75, "\uc77c\uc5b4\ub0ac\ub294\uc9c0": 80, "\uc77c\uc73c\ud0a4\ub294": 82, "\uc77c\uc885\uc758": 77, "\uc77c\uce58\ud558\uc9c0": 76, "\uc784\ub9c8\ub204\uc5d8": 82, "\uc784\uc740": 77, "\uc785\ub825": [75, 76, 77, 80], "\uc785\ub825\ubcf4\ub2e4": 82, "\uc785\ub825\uc2dc\ud0a4\ub294": 82, "\uc785\ub825\uc740": [75, 80], "\uc785\ub825\uc744": [78, 80], "\uc785\ub825\uc774": [77, 78], "\uc785\ub825\uc774\ub098": 77, "\uc785\ub825\uc774\ub780": 80, "\uc785\ub825\ud558\ub294": 78, "\uc785\ubb38\uc7a5\ubcbd\uc774": 75, "\uc788\uac8c": [75, 76], "\uc788\uace0": 79, "\uc788\uae30": 78, "\uc788\ub290\ub0d0": 78, "\uc788\ub294": [75, 76, 80, 81, 82], "\uc788\ub294\ub370": 79, "\uc788\ub2e4": [75, 76, 77, 78, 79, 80, 81, 82], "\uc788\ub2e4\uace0": 76, "\uc788\ub3c4\ub85d": 79, "\uc788\uc5b4\uc57c": 77, "\uc788\uc73c\uba74": 75, "\uc788\uc744\uae4c": [75, 76], "\uc788\uc74c": 80, "\uc788\uc74c\uc744": [77, 78, 80], "\uc790\ub3d9\uc801\uc73c\ub85c": [76, 77], "\uc790\uc8fc": 77, "\uc791\ub3d9": 76, "\uc791\ub3d9\ud558\uc9c0": 77, "\uc791\uc131\ub418\uc5b4\uc57c": 76, "\uc791\uc131\uc740": 81, "\uc791\uc131\ud558\ub294": 75, "\uc791\uc131\ud55c": 75, "\uc791\uc5c5\uc774": 76, "\uc791\uc740": [79, 80], "\uc7a5": 77, "\uc7a5\uc774": 77, "\uc7a5\uc810\ub3c4": 75, "\uc7a5\uc810\uc740": 75, "\uc7a5\uc810\uc744": 75, "\uc800\uae09": 75, "\uc800\uc7a5\ud574": 76, "\uc801\uc6a9\ub418\uc5b4": 80, "\uc801\uc808\ud55c": [76, 77, 78, 80], "\uc801\uc808\ud788": 76, "\uc804\uc138\uacc4": 82, "\uc804\uc5ed": 76, "\uc804\uccb4": [76, 81], "\uc804\uccb4\ub97c": 79, "\uc804\ud600": 78, "\uc808\ucc28\uc801\uc73c\ub85c": 75, "\uc808\ucc28\uc801\uc774": 75, "\uc810\uc218\ub3c4": 78, "\uc810\uc740": 80, "\uc811\uadfc\uc131\uc774": 75, "\uc811\uadfc\ud560": 75, "\uc815\ub2f5\uc774\uac70\ub098": 81, "\uc815\ub2f5\uc774\ub77c\uba74": 81, "\uc815\ub82c\ub41c\ub2e4": 76, "\uc815\ub9ac\ub97c": 77, "\uc815\ubcf4\ub4e4\uc740": 76, "\uc815\ubcf4\ub97c": 76, "\uc815\uc0ac\uac01\ud615": 80, "\uc815\uc0ac\uac01\ud615\uc744": 80, "\uc815\uc218": [79, 81], "\uc815\uc2e0\uc5c6\uc774": 77, "\uc815\uc758": 78, "\uc815\uc911\uc559\uc5d0": 76, "\uc815\uc911\uc559\uc73c\ub85c": 76, "\uc815\uc911\uc559\uc740": 76, "\uc815\uc911\uc559\uc744": 76, "\uc815\uc9c0\ub41c": 77, "\uc815\ud558\uace0": 76, "\uc815\ud558\ub294": 79, "\uc815\ud55c\ub2e4": 76, "\uc815\ud560": [76, 77], "\uc815\ud574\uc57c": 76, "\uc815\ud574\uc838\uc57c": 76, "\uc815\ud655\ud788\ub294": 76, "\uc81c\uacf5\ud558\uae30": 75, "\uc81c\uc57d\uc870\uac74": 78, "\uc81c\uc678\ud654\uba74": 75, "\uc81c\uc791": 75, "\uc81c\ud55c\uc744": 81, "\uc870\uac74\ubb38\uc774": 76, "\uc870\uac74\uc774": 76, "\uc870\uc808\ud558\uac8c": 79, "\uc870\uc885": 83, "\uc874\uc7ac\ud558\uace0": 80, "\uc874\uc7ac\ud558\uae30": 77, "\uc874\uc7ac\ud568\uc744": 80, "\uc885\ub8cc": 77, "\uc885\ub8cc\ub418\uac8c": 76, "\uc885\ub8cc\ub418\uace0": 78, "\uc885\ub8cc\ub418\uc5b4\uc57c": 76, "\uc885\ub8cc\ub41c": 76, "\uc885\ub8cc\ud558\uace0": 76, "\uc885\ub8cc\ud558\ub294": 77, "\uc885\ub958\uc758": 76, "\uc88b\ub2e4\ub294": 75, "\uc88b\uc740": [75, 80], "\uc88b\uc744": 79, "\uc88c\ud45c": 76, "\uc88c\ud45c\ub97c": [78, 79], "\uc88c\ud45c\uc5d0": 77, "\uc8fc\ub294": 75, "\uc8fc\ub85c": 76, "\uc8fc\ubaa9\ud574\ub77c": 78, "\uc8fc\uc5b4\uc9c4": 76, "\uc904\ub4e4\uc740": 78, "\uc911": [75, 77], "\uc911\uc694\ud55c": 82, "\uc990\uae38": 78, "\uc99d\uac00\uc2dc\ud0a4\uac70\ub098": 80, "\uc99d\uac00\ud558\uace0": 81, "\uc9c0\uae08\uae4c\uc9c0": 78, "\uc9c0\uae08\uc740": 78, "\uc9c0\ub294": 80, "\uc9c0\uc2dd\ub9cc\uc73c\ub85c": 82, "\uc9c0\uc2dd\ubcf4\ub2e4": 82, "\uc9c0\uc2dd\uc5d0": 82, "\uc9c0\uc2dd\uc744": 82, "\uc9c0\uc5d0": 81, "\uc9c0\uc6b0\ub294": 75, "\uc9c0\uc810\uc744": 80, "\uc9c1\uad00\uc801\uc73c\ub85c": 78, "\uc9c1\uad00\uc801\uc778": 78, "\uc9c1\uc0ac\uac01\ud615": 76, "\uc9c1\uc0ac\uac01\ud615\ub4e4\uc5d0": 79, "\uc9c1\uc0ac\uac01\ud615\ub4e4\uc744": 79, "\uc9c1\uc0ac\uac01\ud615\uc744": 79, "\uc9c1\uc811": 79, "\uc9c4\uc9dc": 78, "\uc9c4\ud589\ub420": 76, "\uc9c8": 81, "\uc9c8\ub9b0\ub2e4\uba74": 75, "\uc9d1\uc911\ud558\uba74": 75, "\uc9dc\uc99d\ub0a0": 81, "\ucc28\uc774\uc810\uc774": 78, "\ucc28\uc774\uc810\uc774\ub2e4": 78, "\ucc29\uc624\ub97c": 82, "\ucc38\uace0": [76, 77, 78, 79, 80, 81], "\ucc3d\uc758\uc801\uc778": 82, "\ucc3e\uc544\ub0b4\uba74": 77, "\ucc3e\uc744": 79, "\ucc44\ub85c": 78, "\ucc44\uc6b0\ub294": 76, "\ucc98\ub9ac": [75, 76, 78], "\ucc98\ub9ac\uac00": [76, 77], "\ucc98\ub9ac\ub294": 75, "\ucc98\ub9ac\ub97c": 79, "\ucc98\ub9ac\ub9cc\uc774": 80, "\ucc98\ub9ac\uc758": 76, "\ucc98\ub9ac\ud558\uace0": 76, "\ucc98\ub9ac\ud558\ub294": 80, "\ucc98\ub9ac\ud558\ub294\uc9c0\ub294": 77, "\ucc98\ub9ac\ud558\ub824\uba74": 81, "\ucc98\ub9ac\ud560": 76, "\ucc9c\uc7ac\uc131\uc774\ub780": 82, "\uccab\ubc88\uc9f8": 79, "\uccab\uc9f8": [75, 80], "\uccab\uc9f8\ub294": 78, "\uccab\uc9f8\ub85c": 76, "\uccb4\ud06c\ud558\ub294": 76, "\ucd08\uae30\ud654\ub418\uac70\ub098": 76, "\ucd08\uae30\ud654\ub41c\ub2e4": 76, "\ucd08\uae30\ud654\ub428\uc744": 77, "\ucd08\ub85d": 76, "\ucd1d": 79, "\ucd5c\ub300": 79, "\ucd5c\ub300\uac12\uc774": 79, "\ucd5c\ub300\ud55c": 75, "\ucd5c\uc18c": 76, "\ucd94\uac00": 77, "\ucd94\uac00\ub418\uba74": 81, "\ucd94\uac00\ub418\uc5c8\uace0": 77, "\ucd94\uac00\ub418\uc5c8\ub2e4": [77, 80], "\ucd94\uac00\ub41c": 76, "\ucd94\uac00\ub41c\ub2e4": 78, "\ucd94\uac00\uc801\uc73c\ub85c": 78, "\ucd94\uac00\uc801\uc778": [76, 77, 78, 79], "\ucd94\uac00\ud558\uac70\ub098": 76, "\ucd94\uac00\ud558\ub294": [76, 78], "\ucd94\uac00\ud558\ub824": 81, "\ucd94\uac00\ud574\ubcf4\uc790": 78, "\ucd94\uac00\ud574\uc57c": 77, "\ucd9c\ub825": [75, 78, 79, 81, 83], "\ucd9c\ub825\ub418\uac8c": 76, "\ucd9c\ub825\ub418\ub294": 76, "\ucd9c\ub825\ub420": 79, "\ucd9c\ub825\uc6a9\uc774\ub2e4": 80, "\ucd9c\ub825\uc73c\ub85c": 75, "\ucd9c\ub825\uc740": [75, 78], "\ucd9c\ub825\uc744": [76, 77, 80], "\ucd9c\ub825\uc758": 78, "\ucd9c\ub825\uc774": 82, "\ucd9c\ub825\ud558\uae30": 76, "\ucd9c\ub825\ud558\ub294": [76, 78, 79, 81], "\ucd9c\ub825\ud55c\ub2e4": 81, "\ucd9c\ub825\ud574\uc57c": 81, "\ucda9\ub3cc": 75, "\ucda9\ubd84\ud788": 79, "\uce58\uace4": 76, "\uce5c\uc219\ud55c": 76, "\uce60\ud558\uae30": 75, "\uce78\ud2b8\ub294": 82, "\uce94\ubc84\uc2a4": [76, 79], "\uce94\ubc84\uc2a4\ub97c": 76, "\uce94\ubc84\uc2a4\uc5d0": 76, "\uce94\ubc84\uc2a4\uc758": 76, "\ucea1\uc158\uc5d0": 76, "\ucee4\uc9c0\uac8c": 82, "\ucee8\ud150\uce20": 78, "\ucef4\ud4e8\ud130\ub294": 82, "\ucef4\ud4e8\ud130\ub9c8\ub2e4": 77, "\ucef4\ud4e8\ud130\uc5d0\uac8c": 82, "\ucf54\ub4dc": [76, 77, 78, 79, 80, 81], "\ucf54\ub4dc\uac00": 76, "\ucf54\ub4dc\ub97c": 75, "\ucf54\ub4dc\uc640": 76, "\ucf54\ub529\ud558\uac8c": 75, "\ucf54\ub529\ud574": 75, "\ucf58\uc194": 75, "\ucf58\uc194\uc5d0\uc11c": 75, "\ud06c\uac8c": [79, 81], "\ud06c\uace0": 76, "\ud06c\uae30": 76, "\ud06c\uae30\uac00": 76, "\ud06c\uae30\ub97c": [76, 81], "\ud06c\uae30\uc640": 76, "\ud06c\ub2e4\ub294": 82, "\ud06c\uba74": 77, "\ud070": [75, 78, 79, 80], "\ud074\uae4c": 82, "\ud074\ub9ad": 80, "\ud074\ub9ad\uc774": 80, "\ud074\ub9ad\ud588\ub2e4": 80, "\ud07c\uc744": 76, "\ud0a4": 78, "\ud0a4\ub294": 78, "\ud0a4\ub4e4\ub3c4": 78, "\ud0a4\ub97c": 78, "\ud0a4\ubcf4\ub4dc": [75, 78, 79], "\ud0a4\ubcf4\ub4dc\uac00": 80, "\ud0a4\ubcf4\ub4dc\uc5d0": 78, "\ud0a4\uc758": 78, "\ud0c4\ucc3d\uc5d0\uc11c": 79, "\ud14c\ub450\ub9ac\ub97c": [79, 81], "\ud14c\ud2b8\ub9ac\uc2a4\ub97c": 82, "\ud14d\uc2a4\ud2b8": [76, 83], "\ud14d\uc2a4\ud2b8\uac00": [76, 79], "\ud14d\uc2a4\ud2b8\ub294": 76, "\ud14d\uc2a4\ud2b8\ub97c": [76, 77, 79], "\ud14d\uc2a4\ud2b8\uc758": [76, 77], "\ud14d\uc2a4\ud2b8\uc774\ub2e4": 79, "\ud1b5\ud574": 80, "\ud22c\uc790\ud588\uc744\uae4c": 82, "\ud234\uc774": 75, "\ud29c\ud1a0\ub9ac\uc5bc": [15, 81], "\ud29c\ud1a0\ub9ac\uc5bc\uc740": 82, "\ud2b8\ub9ac\uac70": 78, "\ud2b8\ub9ac\uac70\ub418\uba74": 76, "\ud2b9\uc131": [76, 81], "\ud2b9\uc131\uc774": 82, "\ud2b9\uc131\uc774\ub2e4": 82, "\ud2b9\uc774\ud55c": 80, "\ud2b9\uc815": [76, 77, 80], "\ud2b9\uc815\ud55c": 80, "\ud30c\uc774\uac8c\uc784": [76, 81], "\ud30c\uc774\uac8c\uc784\uc740": [75, 76], "\ud30c\uc774\uac8c\uc784\uc744": 75, "\ud30c\uc774\uac8c\uc784\uc758": [75, 76, 77, 82], "\ud30c\uc774\uac8c\uc784\uc774": [75, 76], "\ud30c\uc774\uc36c\uc5d0": 75, "\ud30c\uc774\uc36c\uc758": [75, 76], "\ud30c\uc77c": 75, "\ud30c\uc77c\ub85c": 81, "\ud30c\uc77c\uc744": [75, 76], "\ud30c\uc77c\uc774\ub098": 75, "\ud30c\uc9c0\ud2b8\ub178\ud504\uac00": 82, "\ud310\ub2e8\ud558\ub294": 78, "\ud310\uc815": 80, "\ud3ec\ud568": 82, "\ud3ed\ub113\uc740": 82, "\ud3f0\ud2b8": 76, "\ud3f0\ud2b8\ub97c": 76, "\ud3f0\ud2b8\uc640": 76, "\ud479": 75, "\ud48d\uc131\ud558\uac8c": 78, "\ud504\ub85c\uadf8\ub798\uba38\uac00": 75, "\ud504\ub85c\uadf8\ub798\uba38\ub294": 75, "\ud504\ub85c\uadf8\ub798\ubc0d": 76, "\ud504\ub85c\uadf8\ub798\ubc0d\uacfc": 82, "\ud504\ub85c\uadf8\ub798\ubc0d\uc740": 82, "\ud504\ub85c\uadf8\ub798\ubc0d\uc758": 82, "\ud504\ub85c\uadf8\ub7a8": [76, 77], "\ud504\ub85c\uadf8\ub7a8\uacfc": 75, "\ud504\ub85c\uadf8\ub7a8\uc5d0": 81, "\ud504\ub85c\uadf8\ub7a8\uc5d0\uc120": 78, "\ud504\ub85c\uadf8\ub7a8\uc740": [81, 82], "\ud504\ub85c\uadf8\ub7a8\uc744": [76, 77, 79, 82], "\ud504\ub85c\uadf8\ub7a8\uc758": [75, 76, 78], "\ud504\ub85c\uadf8\ub7a8\uc774": 79, "\ud504\ub85c\uc81d\ud2b8": 77, "\ud504\ub85c\uc81d\ud2b8\uac00": [76, 78], "\ud504\ub85c\uc81d\ud2b8\ub294": [76, 77, 78], "\ud504\ub85c\uc81d\ud2b8\ub85c": 76, "\ud504\ub85c\uc81d\ud2b8\uc5d0": 77, "\ud504\ub85c\uc81d\ud2b8\uc5d0\uc11c": 76, "\ud504\ub85c\uc81d\ud2b8\uc640": 78, "\ud504\ub85c\uc81d\ud2b8\uc740": 75, "\ud504\ub85c\uc81d\ud2b8\uc758": [76, 77, 78], "\ud504\ub85c\uc81d\ud2b8\uc774\uba70": 76, "\ud504\ub864\ub85c\uadf8": 83, "\ud504\ub864\ub85c\uadf8\uc5d0\uc11c": 81, "\ud504\ub9b0\ud2b8": 80, "\ud50c\ub808\uc774": [78, 82], "\ud53c\ud0c0\uace0\ub77c\uc2a4": 77, "\ud544\uc218\uc870\uac74\uc774": 78, "\ud544\uc218\uc870\uac74\uc774\uae30": 78, "\ud544\uc694": [75, 76], "\ud544\uc694\ub3c4": 81, "\ud544\uc694\ub85c": 76, "\ud544\uc694\ud558\uac8c": 80, "\ud544\uc694\ud558\ub2e4": [76, 78], "\ud544\uc694\ud560": 77, "\ud558\uaca0\ub2e4": 79, "\ud558\uace0": 81, "\ud558\uae30": [76, 78], "\ud558\uae30\ub9cc": 79, "\ud558\ub098": 75, "\ud558\ub098\ub85c": 80, "\ud558\ub098\ub9cc": 81, "\ud558\ub098\uc758": [75, 76, 77, 79, 82], "\ud558\ub098\uc774\ub2e4": 77, "\ud558\ub294": [76, 77, 80], "\ud558\ub294\ub370": 80, "\ud558\ub294\uc9c0": 76, "\ud558\ub2e4": 75, "\ud558\uba74": [76, 77, 79], "\ud558\uc580": 79, "\ud558\uc580\uc0c9": 76, "\ud558\uc600\ub2e4": 79, "\ud558\uc9c0\ub9cc": [75, 76, 77, 79, 80, 82], "\ud55c": [77, 80, 82], "\ud55c\uad6d\uc5b4": 15, "\ud55c\ub2e4": [76, 77, 78, 79, 80, 81], "\ud55c\ubc88\ub9cc": 76, "\ud55c\ubc88\ucbe4\uc740": 75, "\ud560": [76, 77, 79, 82], "\ud560\uae4c": 79, "\ud560\ub2f9\ub41c": 76, "\ud568\uc218": [76, 77], "\ud568\uc218\uac00": [76, 77], "\ud568\uc218\ub294": [76, 77, 80, 81], "\ud568\uc218\ub4e4\uacfc": 75, "\ud568\uc218\ub4e4\uc740": [75, 76], "\ud568\uc218\ub4e4\uc744": [75, 76], "\ud568\uc218\ub4e4\uc774": 75, "\ud568\uc218\ub85c": 75, "\ud568\uc218\ub97c": [76, 79], "\ud568\uc218\ubcf4\ub2e4": 77, "\ud568\uc218\uc5d0\uc120": 79, "\ud568\uc218\uc640": [76, 77], "\ud568\uc218\uc758": 76, "\ud568\uc218\uc774\uae30": 76, "\ud568\uc218\uc774\ub2e4": 77, "\ud568\uc218\ud654": 78, "\ud568\uc218\ud654\ub97c": 79, "\ud56d\uc0c1": [75, 76, 78, 80], "\ud574": 75, "\ud574\uacb0\ud560": 75, "\ud574\ub2f9": 78, "\ud574\ub2f9\ub418\ub294": 77, "\ud574\uc11c": 81, "\ud574\uc57c": [76, 79], "\ud5f7\uac08\ub9ac\uba74": 76, "\ud604\uc7ac": 79, "\ud615\uc2dd\uc5d0": 76, "\ud615\uc2dd\uc744": 76, "\ud615\uc2dd\uc774": 76, "\ud638\ucd9c\ub418\ub294\ub370": 76, "\ud638\ucd9c\ub418\uba74": 76, "\ud638\ucd9c\ub418\uc5b4\uc57c": 76, "\ud638\ucd9c\ub41c\ub2e4": 76, "\ud638\ud658\uc131": 75, "\ud654\ub824\ud55c": 76, "\ud654\uba74": [76, 79], "\ud654\uba74\uacfc": 76, "\ud654\uba74\ubcf4\ub2e4\ub294": 77, "\ud654\uba74\ubcf4\ud638\uae30\ucc98\ub7fc": 77, "\ud654\uba74\uc744": [75, 77], "\ud654\uba74\uc758": 76, "\ud655\uc2e4\ud558\ub2e4": 77, "\ud655\uc778\ud558\ub294": [75, 79], "\ud655\uc778\ud558\ub77c": 80, "\ud655\uc778\ud558\uba74": [79, 80], "\ud655\uc778\ud560": [77, 78, 79], "\ud655\uc778\ud574\uc57c": 78, "\ud655\uc815\ub41c\ub2e4\uba74": 76, "\ud658\uacbd": 75, "\ud658\uacbd\uacfc": 75, "\ud658\uacbd\uc5d0\uc11c": 75, "\ud658\uacbd\uc5d0\uc11c\uc758": 75, "\ud658\uacbd\uc6a9": 75, "\ud658\uacbd\uc740": 75, "\ud65c\ub3d9\uc774\ub2e4": 82, "\ud65c\uc131\ud654\ub418\uba74": 80, "\ud65c\uc6a9\ud558\uace0": 82, "\ud65c\uc6a9\ud55c": 75, "\ud65c\uc6a9\ud574": 75, "\ud69f\uc218\ub97c": 77, "\ud6a8\uacfc\uac00": [80, 82], "\ud6a8\uacfc\ub97c": 81, "\ud6a8\uacfc\uc74c\uc744": 81, "\ud6c4": [75, 78], "\ud6e8\uc52c": 79, "\ud765\ubbf8\ub85c\uc6b4": 82}, "titles": ["pygame C API", "High level API exported by pygame.base", "Class BufferProxy API exported by pygame.bufferproxy", "Class Color API exported by pygame.color", "API exported by pygame.display", "API exported by pygame.event", "API exported by pygame._freetype", "API exported by pygame.mixer", "Class Rect API exported by pygame.rect", "API exported by pygame.rwobject", "Slots and c_api - Making functions and data available from other modules", "Class Surface API exported by pygame.surface", "API exported by pygame.surflock", "API exported by pygame.version", "File Path Function Arguments", "Pygame Front Page", "Pygame Logos Page", "<code class=\"xref py py-class docutils literal notranslate\"><span class=\"pre\">pygame.BufferProxy</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.camera</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.cdrom</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.Color</span></code>", "Named Colors", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.cursors</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.display</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.draw</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.event</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.examples</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.fastevent</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.font</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.freetype</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.gfxdraw</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.image</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.joystick</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.key</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.locals</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.mask</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.math</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.midi</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.mixer</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.mouse</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.mixer.music</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.Overlay</span></code>", "<code class=\"xref py py-class docutils literal notranslate\"><span class=\"pre\">pygame.PixelArray</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.pixelcopy</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.Rect</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.scrap</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame._sdl2.controller</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.sdl2_video</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.sndarray</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.sprite</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.Surface</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.surfarray</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.tests</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.time</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame._sdl2.touch</span></code>", "<code class=\"xref py py-mod docutils literal notranslate\"><span class=\"pre\">pygame.transform</span></code>", "Pygame Tutorials - Camera Module Introduction", "Pygame Tutorials - Line By Line Chimp Example", "Pygame Tutorials - Setting Display Modes", "Pygame Tutorials - Import and Initialize", "Making Games With Pygame", "Pygame Tutorials - Help! How Do I Move An Image?", "Pygame Intro", "Pygame Tutorials - Sprite Module Introduction", "Pygame Tutorials - Surfarray Introduction", "pygame/examples/chimp.py", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "Author: Youngwook Kim (Korean)", "\ud55c\uad6d\uc5b4 \ud29c\ud1a0\ub9ac\uc5bc", "A Newbie Guide to pygame", "Revision: Pygame fundamentals", "Kicking things off", "Game object classes", "User-controllable objects", "Putting it all together"], "titleterms": {"": 62, "1": [32, 61, 85, 86, 87, 88, 89], "2": [32, 62, 85, 86, 87, 89], "3": [85, 86, 88, 89], "360": 32, "4": [32, 85, 87], "5": [32, 85, 88], "6": 89, "A": [61, 62, 84, 87, 88], "AND": 63, "Be": 84, "By": 58, "Into": 68, "It": 62, "NO": 84, "On": [58, 62], "One": 62, "The": [58, 62, 64, 85, 86, 89], "There": 84, "To": 62, "With": 61, "_freetyp": 6, "_sdl2": [47, 55], "access": [42, 49, 52], "ad": 62, "advanc": [64, 65, 71, 72], "advic": 84, "all": [58, 62, 89], "alpha": [73, 84], "an": [17, 62], "anim": 69, "api": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13], "ar": [62, 84], "architectur": 84, "argument": 14, "arrai": [17, 43, 52], "audio": [19, 40], "author": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "avail": 10, "axi": 32, "back": 62, "background": [58, 62], "ball": [87, 89], "base": 1, "basic": [50, 57, 59, 68, 69, 70, 85], "bat": [88, 89], "blit": [62, 85], "bother": 84, "buffer": 17, "bufferproxi": 2, "bufferproxypygam": 17, "button": 72, "c": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12], "c_api": 10, "camera": [18, 57], "camerapygam": 18, "captur": 57, "cdrom": 19, "cdrompygam": 19, "center": 58, "chang": 62, "chimp": [58, 66], "class": [2, 3, 8, 11, 36, 50, 58, 64, 87, 88], "clipboard": 46, "close": 63, "code": 61, "collis": [64, 84], "color": [3, 20, 21], "colorkei": 84, "colorpygam": 20, "colorspac": 57, "com": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "comfort": 84, "common": [32, 64], "comput": [29, 57], "con": 32, "connect": 57, "constant": 34, "contact": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "contain": 44, "content": 61, "control": [19, 23, 32, 40, 47, 57, 88], "controllerpygam": 47, "convert": 84, "coordin": [45, 62], "copi": 43, "creat": [58, 62], "cursor": 22, "cursorspygam": 22, "da": 85, "data": [10, 49, 52], "decid": 59, "definit": 62, "design": 84, "detect": [64, 84], "direct": 42, "displai": [4, 23, 58, 59], "displaypygam": 23, "distract": 84, "divers": [87, 88], "do": [62, 84], "document": 15, "don": 84, "draw": [24, 30, 58], "drawpygam": 24, "driven": 68, "entir": 58, "epilog": 74, "event": [5, 25, 27, 58, 68, 70, 84, 85, 88], "eventpygam": 25, "everyth": 58, "exampl": [26, 58, 59, 65, 66], "examplesmodul": 26, "export": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 17], "extend": 64, "fasteventpygam": 27, "file": 14, "finish": [58, 89], "first": [62, 86], "font": [28, 29], "fontpygam": 28, "freetypeenhanc": 29, "friend": 84, "from": [10, 62], "front": 15, "function": [10, 14, 59, 62, 65, 71, 86], "fundament": 85, "game": [50, 58, 61, 63, 84, 85, 87], "gamepad": 32, "gener": 43, "get": 84, "gfxdrawpygam": 30, "gmail": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "go": 62, "graduat": 65, "graphic": 41, "group": 64, "gui": [68, 76], "guid": 84, "handl": [58, 62, 86], "help": 62, "here": 62, "hero": 62, "high": 1, "histori": [63, 64], "hit": 89, "how": [59, 62], "i": [62, 70, 84], "imag": [31, 35, 51, 57, 62], "imagepygam": 31, "import": [57, 58, 60, 65], "improv": 62, "inform": 44, "init": [57, 60], "initi": [58, 60], "input": [37, 55, 58, 62, 70], "interact": [25, 27, 32, 37], "interfac": 52, "intro": 63, "introduct": [57, 58, 59, 61, 63, 64, 65], "issu": 84, "joi": 32, "joystick": 32, "joystickpygam": 32, "just": 62, "keyboard": 33, "keypygam": 33, "kick": 86, "kim": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "know": 84, "korean": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "left": 32, "legaci": 16, "lesson": 64, "let": [62, 89], "level": [1, 44], "line": [58, 86], "list": [57, 62], "live": 57, "load": [28, 29, 38, 58, 86], "localspygam": 34, "lock": 65, "logo": 16, "loop": [58, 85], "main": 58, "make": [10, 61, 62], "manag": 84, "mani": 64, "map": [32, 62], "mask": [35, 57], "maskpygam": 35, "mathpygam": 36, "midi": 37, "midipygam": 37, "mix": 64, "mixer": [7, 40], "mixerpygam": 38, "mode": 59, "modul": [10, 18, 19, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 43, 44, 46, 47, 49, 50, 52, 54, 55, 56, 57, 58, 63, 64, 86], "monitor": 54, "more": [62, 65], "mous": 39, "mousepygam": 39, "move": 62, "movement": 62, "multipl": 62, "musicpygam": 40, "mysteri": 62, "name": 21, "need": 84, "new": 70, "newbi": 84, "next": 62, "nintendo": 32, "note": 61, "numpi": 65, "object": [17, 20, 41, 42, 45, 50, 51, 58, 85, 87, 88], "obsolet": 84, "off": 86, "option": 84, "other": [10, 65], "outdat": 84, "output": [37, 68, 72], "over": 58, "overlai": 41, "overlaypygam": 41, "overview": 63, "own": [62, 64], "packag": [44, 53], "page": [15, 16], "part": 84, "path": 14, "pattern": 84, "perfect": 84, "physic": 87, "pixel": [42, 43, 52, 62, 84], "pixelarraypygam": 42, "pixelcopypygam": 43, "plai": 38, "playstat": 32, "plu": 73, "prepar": [58, 62], "pro": 32, "problem": 64, "process": [69, 71], "product": 89, "program": 26, "prolog": 67, "protocol": 17, "put": [58, 62, 89], "py": [13, 66], "pygam": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 84, 85, 88], "pygameth": 44, "python": [63, 84], "pythoni": 84, "queue": [25, 27], "quick": 15, "quit": 60, "realli": 84, "recogn": 84, "rect": [8, 84], "rectangular": 45, "rectpygam": 45, "refer": 15, "render": [28, 29, 64], "repres": 51, "represent": 20, "resourc": [22, 58, 86], "revis": 85, "right": 32, "rule": 84, "rumia0601": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "rwobject": 9, "sampl": 49, "scene": 58, "scrappygam": 46, "screen": [23, 62], "sdl2_video": 48, "set": [59, 69], "setup": 58, "shape": [24, 30], "side": [84, 89], "simpl": [87, 88], "singl": 57, "six": 84, "slot": 10, "smooth": 62, "sndarraypygam": 49, "so": 62, "softwar": 84, "some": 62, "sound": [38, 49], "sprite": [58, 64, 87], "spritepygam": 50, "src_c": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12], "src_py": 13, "start": 15, "step": 62, "store": 45, "stream": [40, 57], "style": 61, "subsystem": 84, "suit": 53, "support": 46, "surfac": [11, 17, 42, 52, 56, 65, 84], "surfacepygam": 51, "surfarrai": 65, "surfarraypygam": 52, "surflock": 12, "switch": 32, "t": 84, "ta": 85, "tabl": 61, "take": 62, "tast": 63, "templat": 68, "test": 53, "testspygam": 53, "text": 58, "them": 64, "thing": [84, 86], "threshold": 57, "through": 17, "time": [54, 62], "timepygam": 54, "togeth": [62, 64, 89], "top": 44, "touch": 55, "touchpygam": 55, "trackbal": 32, "transfer": 31, "transform": 56, "transformpygam": 56, "transpar": 65, "tutori": [15, 57, 58, 59, 60, 62, 64, 65], "type": 64, "unit": 53, "updat": 58, "us": [18, 52, 57, 84], "user": [62, 88], "v": 84, "vector": [36, 87], "version": [13, 44], "versionsmal": 44, "video": 41, "vision": 57, "wai": 84, "wari": 84, "what": [62, 84], "which": 84, "while": 58, "why": 67, "window": 23, "work": [33, 39, 47, 55, 84], "x": 32, "xbox": 32, "you": [62, 84], "youngwook": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "your": [62, 64, 84], "\uadf8\ub9ac\uace0": [79, 80, 81], "\uae30\ubc18\uacfc": 76, "\uae30\ubc18\uc73c\ub85c\uc758": 76, "\uae30\ubcf8": 76, "\uae30\ucd08": [76, 77, 78], "\ub354": 81, "\ubc84\ud2bc": 80, "\uc0c8\ub85c\uc6b4": 78, "\uc2ec\ud654": [79, 80], "\uc5d0\ud544\ub85c\uadf8": 82, "\uc65c": 75, "\uc6c0\uc9c1\uc774\uae30": 77, "\uc704\ud55c": 77, "\uc774\ubca4\ud2b8": [76, 78], "\uc785\ub825": 78, "\uc785\ub825\uc740": 78, "\uc785\ubb38": 76, "\uc870\uac74": 77, "\uc870\uae08": 81, "\ucc98\ub9ac": [77, 79], "\ucd9c\ub825": [76, 80], "\ud29c\ud1a0\ub9ac\uc5bc": 83, "\ud30c\uc774\uac8c\uc784": 75, "\ud504\ub864\ub85c\uadf8": 75, "\ud558\ud544": 75, "\ud55c\uad6d\uc5b4": 83, "\ud568\uc218\ud654": 79, "\ud615\uc2dd\uacfc": 76, "\ud654\uba74\uc774": 77}})
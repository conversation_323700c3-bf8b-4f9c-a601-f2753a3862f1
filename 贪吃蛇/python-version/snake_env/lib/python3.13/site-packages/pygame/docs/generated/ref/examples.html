<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.examples &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.fastevent" href="fastevent.html" />
    <link rel="prev" title="pygame.event" href="event.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.examples">
<span id="pygame-examples"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.examples</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">module of example programs</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.aliens.main">pygame.examples.aliens.main</a></div>
</td>
<td>—</td>
<td>play the full aliens example</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.stars.main">pygame.examples.stars.main</a></div>
</td>
<td>—</td>
<td>run a simple starfield example</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.chimp.main">pygame.examples.chimp.main</a></div>
</td>
<td>—</td>
<td>hit the moving chimp</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.moveit.main">pygame.examples.moveit.main</a></div>
</td>
<td>—</td>
<td>display animated objects on the screen</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.fonty.main">pygame.examples.fonty.main</a></div>
</td>
<td>—</td>
<td>run a font rendering example</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.freetype_misc.main">pygame.examples.freetype_misc.main</a></div>
</td>
<td>—</td>
<td>run a FreeType rendering example</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.vgrade.main">pygame.examples.vgrade.main</a></div>
</td>
<td>—</td>
<td>display a vertical gradient</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.eventlist.main">pygame.examples.eventlist.main</a></div>
</td>
<td>—</td>
<td>display pygame events</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.arraydemo.main">pygame.examples.arraydemo.main</a></div>
</td>
<td>—</td>
<td>show various surfarray effects</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.sound.main">pygame.examples.sound.main</a></div>
</td>
<td>—</td>
<td>load and play a sound</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.sound_array_demos.main">pygame.examples.sound_array_demos.main</a></div>
</td>
<td>—</td>
<td>play various sndarray effects</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.liquid.main">pygame.examples.liquid.main</a></div>
</td>
<td>—</td>
<td>display an animated liquid effect</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.glcube.main">pygame.examples.glcube.main</a></div>
</td>
<td>—</td>
<td>display an animated 3D cube using OpenGL</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.scrap_clipboard.main">pygame.examples.scrap_clipboard.main</a></div>
</td>
<td>—</td>
<td>access the clipboard</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.mask.main">pygame.examples.mask.main</a></div>
</td>
<td>—</td>
<td>display multiple images bounce off each other using collision detection</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.testsprite.main">pygame.examples.testsprite.main</a></div>
</td>
<td>—</td>
<td>show lots of sprites moving around</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.headless_no_windows_needed.main">pygame.examples.headless_no_windows_needed.main</a></div>
</td>
<td>—</td>
<td>write an image file that is smoothscaled copy of an input file</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.joystick.main">pygame.examples.joystick.main</a></div>
</td>
<td>—</td>
<td>demonstrate joystick functionality</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.blend_fill.main">pygame.examples.blend_fill.main</a></div>
</td>
<td>—</td>
<td>demonstrate the various surface.fill method blend options</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.blit_blends.main">pygame.examples.blit_blends.main</a></div>
</td>
<td>—</td>
<td>uses alternative additive fill to that of surface.fill</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.cursors.main">pygame.examples.cursors.main</a></div>
</td>
<td>—</td>
<td>display two different custom cursors</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.pixelarray.main">pygame.examples.pixelarray.main</a></div>
</td>
<td>—</td>
<td>display various pixelarray generated effects</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.scaletest.main">pygame.examples.scaletest.main</a></div>
</td>
<td>—</td>
<td>interactively scale an image using smoothscale</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.midi.main">pygame.examples.midi.main</a></div>
</td>
<td>—</td>
<td>run a midi example</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.scroll.main">pygame.examples.scroll.main</a></div>
</td>
<td>—</td>
<td>run a Surface.scroll example that shows a magnified image</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.camera.main">pygame.examples.camera.main</a></div>
</td>
<td>—</td>
<td>display video captured live from an attached camera</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="examples.html#pygame.examples.playmus.main">pygame.examples.playmus.main</a></div>
</td>
<td>—</td>
<td>play an audio file</td>
</tr>
</tbody>
</table>
<p>These examples should help get you started with pygame. Here is a brief rundown
of what you get. The source code for these examples is in the public domain.
Feel free to use for your own projects.</p>
<p>There are several ways to run the examples. First they can be run as
stand-alone programs. Second they can be imported and their <code class="docutils literal notranslate"><span class="pre">main()</span></code> methods
called (see below). Finally, the easiest way is to use the python -m option:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pygame</span><span class="o">.</span><span class="n">examples</span><span class="o">.&lt;</span><span class="n">example</span> <span class="n">name</span><span class="o">&gt;</span> <span class="o">&lt;</span><span class="n">example</span> <span class="n">arguments</span><span class="o">&gt;</span>
</pre></div>
</div>
<p>eg:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pygame</span><span class="o">.</span><span class="n">examples</span><span class="o">.</span><span class="n">scaletest</span> <span class="n">someimage</span><span class="o">.</span><span class="n">png</span>
</pre></div>
</div>
<p>Resources such as images and sounds for the examples are found in the
pygame/examples/data subdirectory.</p>
<p>You can find where the example files are installed by using the following
commands inside the python interpreter.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pygame.examples.scaletest</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pygame</span><span class="o">.</span><span class="n">examples</span><span class="o">.</span><span class="n">scaletest</span><span class="o">.</span><span class="vm">__file__</span>
<span class="go">&#39;/usr/lib/python2.6/site-packages/pygame/examples/scaletest.py&#39;</span>
</pre></div>
</div>
<p>On each OS and version of Python the location will be slightly different.
For example on Windows it might be in 'C:/Python26/Lib/site-packages/pygame/examples/'
On Mac OS X it might be in '/Library/Frameworks/Python.framework/Versions/2.6/lib/python2.6/site-packages/pygame/examples/'</p>
<p>You can also run the examples in the python interpreter by calling each modules main() function.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pygame.examples.scaletest</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pygame</span><span class="o">.</span><span class="n">examples</span><span class="o">.</span><span class="n">scaletest</span><span class="o">.</span><span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<p>We're always on the lookout for more examples and/or example requests. Code
like this is probably the best way to start getting involved with python
gaming.</p>
<p>examples as a package is new to pygame 1.9.0. But most of the examples came with
pygame much earlier.</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.aliens.main">
<span class="sig-prename descclassname"><span class="pre">aliens.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.aliens.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">play the full aliens example</span></div>
<div class="line"><span class="signature">aliens.main() -&gt; None</span></div>
</div>
<p>This started off as a port of the <code class="docutils literal notranslate"><span class="pre">SDL</span></code> demonstration, Aliens. Now it has
evolved into something sort of resembling fun. This demonstrates a lot of
different uses of sprites and optimized blitting. Also transparency,
colorkeys, fonts, sound, music, joystick, and more. (PS, my high score is
117! goodluck)</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.stars.main">
<span class="sig-prename descclassname"><span class="pre">stars.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.stars.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">run a simple starfield example</span></div>
<div class="line"><span class="signature">stars.main() -&gt; None</span></div>
</div>
<p>A simple starfield example. You can change the center of perspective by
leftclicking the mouse on the screen.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.chimp.main">
<span class="sig-prename descclassname"><span class="pre">chimp.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.chimp.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">hit the moving chimp</span></div>
<div class="line"><span class="signature">chimp.main() -&gt; None</span></div>
</div>
<p>This simple example is derived from the line-by-line tutorial that comes
with pygame. It is based on a 'popular' web banner. Note there are comments
here, but for the full explanation, follow along in the tutorial.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.moveit.main">
<span class="sig-prename descclassname"><span class="pre">moveit.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.moveit.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display animated objects on the screen</span></div>
<div class="line"><span class="signature">moveit.main() -&gt; None</span></div>
</div>
<p>This is the full and final example from the Pygame Tutorial, &quot;How Do I Make
It Move&quot;. It creates 10 objects and animates them on the screen.</p>
<p>Note it's a bit scant on error checking, but it's easy to read. :]
Fortunately, this is python, and we needn't wrestle with a pile of error
codes.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.fonty.main">
<span class="sig-prename descclassname"><span class="pre">fonty.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.fonty.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">run a font rendering example</span></div>
<div class="line"><span class="signature">fonty.main() -&gt; None</span></div>
</div>
<p>Super quick, super simple application demonstrating the different ways to
render fonts with the font module</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.freetype_misc.main">
<span class="sig-prename descclassname"><span class="pre">freetype_misc.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.freetype_misc.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">run a FreeType rendering example</span></div>
<div class="line"><span class="signature">freetype_misc.main() -&gt; None</span></div>
</div>
<p>A showcase of rendering features the <a class="tooltip reference internal" href="freetype.html#pygame.freetype.Font" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.freetype.Font</span></code><span class="tooltip-content">Create a new Font instance from a supported font file.</span></a>
class provides in addition to those available with <a class="tooltip reference internal" href="font.html#pygame.font.Font" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.font.Font</span></code><span class="tooltip-content">create a new Font object from a file</span></a>.
It is a demonstration of direct to surface rendering, with vertical text
and rotated text, opaque text and semi transparent text, horizontally
stretched text and vertically stretched text.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.vgrade.main">
<span class="sig-prename descclassname"><span class="pre">vgrade.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.vgrade.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display a vertical gradient</span></div>
<div class="line"><span class="signature">vgrade.main() -&gt; None</span></div>
</div>
<p>Demonstrates creating a vertical gradient with pixelcopy and NumPy python.
The app will create a new gradient every half second and report the time
needed to create and display the image. If you're not prepared to start
working with the NumPy arrays, don't worry about the source for this one :]</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.eventlist.main">
<span class="sig-prename descclassname"><span class="pre">eventlist.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.eventlist.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display pygame events</span></div>
<div class="line"><span class="signature">eventlist.main() -&gt; None</span></div>
</div>
<p>Eventlist is a sloppy style of pygame, but is a handy tool for learning
about pygame events and input. At the top of the screen are the state of
several device values, and a scrolling list of events are displayed on the
bottom.</p>
<p>This is not quality 'ui' code at all, but you can see how to implement very
non-interactive status displays, or even a crude text output control.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.arraydemo.main">
<span class="sig-prename descclassname"><span class="pre">arraydemo.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.arraydemo.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">show various surfarray effects</span></div>
<div class="line"><span class="signature">arraydemo.main(arraytype=None) -&gt; None</span></div>
</div>
<p>Another example filled with various surfarray effects. It requires the
surfarray and image modules to be installed. This little demo can also make
a good starting point for any of your own tests with surfarray</p>
<p>The <code class="docutils literal notranslate"><span class="pre">arraytype</span></code> parameter is deprecated; passing any value besides 'numpy'
will raise ValueError.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.sound.main">
<span class="sig-prename descclassname"><span class="pre">sound.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.sound.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">load and play a sound</span></div>
<div class="line"><span class="signature">sound.main(file_path=None) -&gt; None</span></div>
</div>
<p>Extremely basic testing of the mixer module. Load a sound and play it. All
from the command shell, no graphics.</p>
<p>If provided, use the audio file 'file_path', otherwise use a default file.</p>
<p><code class="docutils literal notranslate"><span class="pre">sound.py</span></code> optional command line argument: an audio file</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.sound_array_demos.main">
<span class="sig-prename descclassname"><span class="pre">sound_array_demos.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.sound_array_demos.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">play various sndarray effects</span></div>
<div class="line"><span class="signature">sound_array_demos.main(arraytype=None) -&gt; None</span></div>
</div>
<p>Uses sndarray and NumPy to create offset faded copies of the
original sound. Currently it just uses hardcoded values for the number of
echoes and the delay. Easy for you to recreate as needed.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">arraytype</span></code> parameter is deprecated; passing any value besides 'numpy'
will raise ValueError.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.liquid.main">
<span class="sig-prename descclassname"><span class="pre">liquid.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.liquid.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display an animated liquid effect</span></div>
<div class="line"><span class="signature">liquid.main() -&gt; None</span></div>
</div>
<p>This example was created in a quick comparison with the BlitzBasic gaming
language. Nonetheless, it demonstrates a quick 8-bit setup (with colormap).</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.glcube.main">
<span class="sig-prename descclassname"><span class="pre">glcube.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.glcube.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display an animated 3D cube using OpenGL</span></div>
<div class="line"><span class="signature">glcube.main() -&gt; None</span></div>
</div>
<p>Using PyOpenGL and pygame, this creates a spinning 3D multicolored cube.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.scrap_clipboard.main">
<span class="sig-prename descclassname"><span class="pre">scrap_clipboard.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.scrap_clipboard.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">access the clipboard</span></div>
<div class="line"><span class="signature">scrap_clipboard.main() -&gt; None</span></div>
</div>
<p>A simple demonstration example for the clipboard support.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.mask.main">
<span class="sig-prename descclassname"><span class="pre">mask.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.mask.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display multiple images bounce off each other using collision detection</span></div>
<div class="line"><span class="signature">mask.main(*args) -&gt; None</span></div>
</div>
<p>Positional arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">one</span> <span class="ow">or</span> <span class="n">more</span> <span class="n">image</span> <span class="n">file</span> <span class="n">names</span><span class="o">.</span>
</pre></div>
</div>
<p>This <code class="docutils literal notranslate"><span class="pre">pygame.masks</span></code> demo will display multiple moving sprites bouncing off
each other. More than one sprite image can be provided.</p>
<p>If run as a program then <code class="docutils literal notranslate"><span class="pre">mask.py</span></code> takes one or more image files as
command line arguments.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.testsprite.main">
<span class="sig-prename descclassname"><span class="pre">testsprite.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.testsprite.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">show lots of sprites moving around</span></div>
<div class="line"><span class="signature">testsprite.main(update_rects = True, use_static = False, use_FastRenderGroup = False, screen_dims = [640, 480], use_alpha = False, flags = 0) -&gt; None</span></div>
</div>
<p>Optional keyword arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">update_rects</span> <span class="o">-</span> <span class="n">use</span> <span class="n">the</span> <span class="n">RenderUpdate</span> <span class="n">sprite</span> <span class="n">group</span> <span class="k">class</span>
<span class="nc">use_static</span> <span class="o">-</span> <span class="n">include</span> <span class="n">non</span><span class="o">-</span><span class="n">moving</span> <span class="n">images</span>
<span class="n">use_FastRenderGroup</span> <span class="o">-</span> <span class="n">Use</span> <span class="n">the</span> <span class="n">FastRenderGroup</span> <span class="n">sprite</span> <span class="n">group</span>
<span class="n">screen_dims</span> <span class="o">-</span> <span class="n">pygame</span> <span class="n">window</span> <span class="n">dimensions</span>
<span class="n">use_alpha</span> <span class="o">-</span> <span class="n">use</span> <span class="n">alpha</span> <span class="n">blending</span>
<span class="n">flags</span> <span class="o">-</span> <span class="n">additional</span> <span class="n">display</span> <span class="n">mode</span> <span class="n">flags</span>
</pre></div>
</div>
<p>Like the <code class="docutils literal notranslate"><span class="pre">testsprite.c</span></code> that comes with SDL, this pygame version shows
lots of sprites moving around.</p>
<p>If run as a stand-alone program then no command line arguments are taken.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.headless_no_windows_needed.main">
<span class="sig-prename descclassname"><span class="pre">headless_no_windows_needed.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.headless_no_windows_needed.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">write an image file that is smoothscaled copy of an input file</span></div>
<div class="line"><span class="signature">headless_no_windows_needed.main(fin, fout, w, h) -&gt; None</span></div>
</div>
<p>arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">fin</span> <span class="o">-</span> <span class="n">name</span> <span class="n">of</span> <span class="n">an</span> <span class="nb">input</span> <span class="n">image</span> <span class="n">file</span>
<span class="n">fout</span> <span class="o">-</span> <span class="n">name</span> <span class="n">of</span> <span class="n">the</span> <span class="n">output</span> <span class="n">file</span> <span class="n">to</span> <span class="n">create</span><span class="o">/</span><span class="n">overwrite</span>
<span class="n">w</span><span class="p">,</span> <span class="n">h</span> <span class="o">-</span> <span class="n">size</span> <span class="n">of</span> <span class="n">the</span> <span class="n">rescaled</span> <span class="n">image</span><span class="p">,</span> <span class="k">as</span> <span class="n">integer</span> <span class="n">width</span> <span class="ow">and</span> <span class="n">height</span>
</pre></div>
</div>
<p>How to use pygame with no windowing system, like on headless servers.</p>
<p>Thumbnail generation with scaling is an example of what you can do with
pygame.</p>
<p><code class="docutils literal notranslate"><span class="pre">NOTE</span></code>: the pygame scale function uses MMX/SSE if available, and can be
run in multiple threads.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">headless_no_windows_needed.py</span></code> is run as a program it takes the
following command line arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">-</span><span class="n">scale</span> <span class="n">inputimage</span> <span class="n">outputimage</span> <span class="n">new_width</span> <span class="n">new_height</span>
<span class="n">eg</span><span class="o">.</span> <span class="o">-</span><span class="n">scale</span> <span class="ow">in</span><span class="o">.</span><span class="n">png</span> <span class="n">outpng</span> <span class="mi">50</span> <span class="mi">50</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.joystick.main">
<span class="sig-prename descclassname"><span class="pre">joystick.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.joystick.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">demonstrate joystick functionality</span></div>
<div class="line"><span class="signature">joystick.main() -&gt; None</span></div>
</div>
<p>A demo showing full joystick support.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.2.</span></p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.blend_fill.main">
<span class="sig-prename descclassname"><span class="pre">blend_fill.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.blend_fill.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">demonstrate the various surface.fill method blend options</span></div>
<div class="line"><span class="signature">blend_fill.main() -&gt; None</span></div>
</div>
<p>A interactive demo that lets one choose which BLEND_xxx option to apply to a
surface.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.blit_blends.main">
<span class="sig-prename descclassname"><span class="pre">blit_blends.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.blit_blends.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">uses alternative additive fill to that of surface.fill</span></div>
<div class="line"><span class="signature">blit_blends.main() -&gt; None</span></div>
</div>
<p>Fake additive blending. Using NumPy. it doesn't clamp. Press r,g,b Somewhat
like blend_fill.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.cursors.main">
<span class="sig-prename descclassname"><span class="pre">cursors.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.cursors.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display two different custom cursors</span></div>
<div class="line"><span class="signature">cursors.main() -&gt; None</span></div>
</div>
<p>Display an arrow or circle with crossbar cursor.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.pixelarray.main">
<span class="sig-prename descclassname"><span class="pre">pixelarray.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.pixelarray.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display various pixelarray generated effects</span></div>
<div class="line"><span class="signature">pixelarray.main() -&gt; None</span></div>
</div>
<p>Display various pixelarray generated effects.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.scaletest.main">
<span class="sig-prename descclassname"><span class="pre">scaletest.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.scaletest.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">interactively scale an image using smoothscale</span></div>
<div class="line"><span class="signature">scaletest.main(imagefile, convert_alpha=False, run_speed_test=True) -&gt; None</span></div>
</div>
<p>arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">imagefile</span> <span class="o">-</span> <span class="n">file</span> <span class="n">name</span> <span class="n">of</span> <span class="n">source</span> <span class="n">image</span> <span class="p">(</span><span class="n">required</span><span class="p">)</span>
<span class="n">convert_alpha</span> <span class="o">-</span> <span class="n">use</span> <span class="n">convert_alpha</span><span class="p">()</span> <span class="n">on</span> <span class="n">the</span> <span class="n">surf</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="n">run_speed_test</span> <span class="o">-</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
</pre></div>
</div>
<p>A smoothscale example that resized an image on the screen. Vertical and
horizontal arrow keys are used to change the width and height of the
displayed image. If the convert_alpha option is True then the source image
is forced to have source alpha, whether or not the original images does. If
run_speed_test is True then a background timing test is performed instead of
the interactive scaler.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">scaletest.py</span></code> is run as a program then the command line options are:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">ImageFile</span> <span class="p">[</span><span class="o">-</span><span class="n">t</span><span class="p">]</span> <span class="p">[</span><span class="o">-</span><span class="n">convert_alpha</span><span class="p">]</span>
<span class="p">[</span><span class="o">-</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">Run</span> <span class="n">Speed</span> <span class="n">Test</span>
<span class="p">[</span><span class="o">-</span><span class="n">convert_alpha</span><span class="p">]</span> <span class="o">=</span> <span class="n">Use</span> <span class="n">convert_alpha</span><span class="p">()</span> <span class="n">on</span> <span class="n">the</span> <span class="n">surf</span><span class="o">.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.midi.main">
<span class="sig-prename descclassname"><span class="pre">midi.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.midi.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">run a midi example</span></div>
<div class="line"><span class="signature">midi.main(mode='output', device_id=None) -&gt; None</span></div>
</div>
<p>Arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">mode</span> <span class="o">-</span> <span class="k">if</span> <span class="s1">&#39;output&#39;</span> <span class="n">run</span> <span class="n">a</span> <span class="n">midi</span> <span class="n">keyboard</span> <span class="n">output</span> <span class="n">example</span>
          <span class="s1">&#39;input&#39;</span> <span class="n">run</span> <span class="n">a</span> <span class="n">midi</span> <span class="n">event</span> <span class="n">logger</span> <span class="nb">input</span> <span class="n">example</span>
          <span class="s1">&#39;list&#39;</span> <span class="nb">list</span> <span class="n">available</span> <span class="n">midi</span> <span class="n">devices</span>
       <span class="p">(</span><span class="n">default</span> <span class="s1">&#39;output&#39;</span><span class="p">)</span>
<span class="n">device_id</span> <span class="o">-</span> <span class="n">midi</span> <span class="n">device</span> <span class="n">number</span><span class="p">;</span> <span class="k">if</span> <span class="kc">None</span> <span class="n">then</span> <span class="n">use</span> <span class="n">the</span> <span class="n">default</span> <span class="n">midi</span> <span class="nb">input</span> <span class="ow">or</span>
            <span class="n">output</span> <span class="n">device</span> <span class="k">for</span> <span class="n">the</span> <span class="n">system</span>
</pre></div>
</div>
<p>The output example shows how to translate mouse clicks or computer keyboard
events into midi notes. It implements a rudimentary button widget and state
machine.</p>
<p>The input example shows how to translate midi input to pygame events.</p>
<p>With the use of a virtual midi patch cord the output and input examples can
be run as separate processes and connected so the keyboard output is
displayed on a console.</p>
<p>new to pygame 1.9.0</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.scroll.main">
<span class="sig-prename descclassname"><span class="pre">scroll.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.scroll.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">run a Surface.scroll example that shows a magnified image</span></div>
<div class="line"><span class="signature">scroll.main(image_file=None) -&gt; None</span></div>
</div>
<p>This example shows a scrollable image that has a zoom factor of eight. It
uses the <a class="reference internal" href="surface.html#pygame.Surface.scroll" title="pygame.Surface.scroll"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Surface.scroll()</span></code></a>
function to shift the image on the display surface.
A clip rectangle protects a margin area. If called as a function,
the example accepts an optional image file path. If run as a program it
takes an optional file path command line argument. If no file is provided a
default image file is used.</p>
<p>When running click on a black triangle to move one pixel in the direction
the triangle points. Or use the arrow keys. Close the window or press
<code class="docutils literal notranslate"><span class="pre">ESC</span></code> to quit.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.camera.main">
<span class="sig-prename descclassname"><span class="pre">camera.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.camera.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">display video captured live from an attached camera</span></div>
<div class="line"><span class="signature">camera.main() -&gt; None</span></div>
</div>
<p>A simple live video player, it uses the first available camera it finds on
the system.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.examples.playmus.main">
<span class="sig-prename descclassname"><span class="pre">playmus.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.examples.playmus.main" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">play an audio file</span></div>
<div class="line"><span class="signature">playmus.main(file_path) -&gt; None</span></div>
</div>
<p>A simple music player with window and keyboard playback control. Playback can
be paused and rewound to the beginning.</p>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref/examples.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fastevent.html" title="pygame.fastevent"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="event.html" title="pygame.event"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.examples</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>
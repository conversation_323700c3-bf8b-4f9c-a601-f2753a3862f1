.. include:: common.txt

Named Colors
============

.. raw:: html

    <style>
    .aliceblue {background-color: #f0f8ff; color: #f0f8ff;}
    .antiquewhite {background-color: #faebd7; color: #faebd7;}
    .antiquewhite1 {background-color: #ffefdb; color: #ffefdb;}
    .antiquewhite2 {background-color: #eedfcc; color: #eedfcc;}
    .antiquewhite3 {background-color: #cdc0b0; color: #cdc0b0;}
    .antiquewhite4 {background-color: #8b8378; color: #8b8378;}
    .aqua {background-color: #00ffff; color: #00ffff;}
    .aquamarine {background-color: #7fffd4; color: #7fffd4;}
    .aquamarine1 {background-color: #7fffd4; color: #7fffd4;}
    .aquamarine2 {background-color: #76eec6; color: #76eec6;}
    .aquamarine3 {background-color: #66cdaa; color: #66cdaa;}
    .aquamarine4 {background-color: #458b74; color: #458b74;}
    .azure {background-color: #f0ffff; color: #f0ffff;}
    .azure1 {background-color: #f0ffff; color: #f0ffff;}
    .azure3 {background-color: #c1cdcd; color: #c1cdcd;}
    .azure2 {background-color: #e0eeee; color: #e0eeee;}
    .azure4 {background-color: #838b8b; color: #838b8b;}
    .beige {background-color: #f5f5dc; color: #f5f5dc;}
    .bisque {background-color: #ffe4c4; color: #ffe4c4;}
    .bisque1 {background-color: #ffe4c4; color: #ffe4c4;}
    .bisque2 {background-color: #eed5b7; color: #eed5b7;}
    .bisque3 {background-color: #cdb79e; color: #cdb79e;}
    .bisque4 {background-color: #8b7d6b; color: #8b7d6b;}
    .black {background-color: #000000; color: #000000;}
    .blanchedalmond {background-color: #ffebcd; color: #ffebcd;}
    .blue {background-color: #0000ff; color: #0000ff;}
    .blue1 {background-color: #0000ff; color: #0000ff;}
    .blue2 {background-color: #0000ee; color: #0000ee;}
    .blue3 {background-color: #0000cd; color: #0000cd;}
    .blue4 {background-color: #00008b; color: #00008b;}
    .blueviolet {background-color: #8a2be2; color: #8a2be2;}
    .brown {background-color: #a52a2a; color: #a52a2a;}
    .brown1 {background-color: #ff4040; color: #ff4040;}
    .brown2 {background-color: #ee3b3b; color: #ee3b3b;}
    .brown3 {background-color: #cd3333; color: #cd3333;}
    .brown4 {background-color: #8b2323; color: #8b2323;}
    .burlywood {background-color: #deb887; color: #deb887;}
    .burlywood1 {background-color: #ffd39b; color: #ffd39b;}
    .burlywood2 {background-color: #eec591; color: #eec591;}
    .burlywood3 {background-color: #cdaa7d; color: #cdaa7d;}
    .burlywood4 {background-color: #8b7355; color: #8b7355;}
    .cadetblue {background-color: #5f9ea0; color: #5f9ea0;}
    .cadetblue1 {background-color: #98f5ff; color: #98f5ff;}
    .cadetblue2 {background-color: #8ee5ee; color: #8ee5ee;}
    .cadetblue3 {background-color: #7ac5cd; color: #7ac5cd;}
    .cadetblue4 {background-color: #53868b; color: #53868b;}
    .chartreuse {background-color: #7fff00; color: #7fff00;}
    .chartreuse1 {background-color: #7fff00; color: #7fff00;}
    .chartreuse2 {background-color: #76ee00; color: #76ee00;}
    .chartreuse3 {background-color: #66cd00; color: #66cd00;}
    .chartreuse4 {background-color: #458b00; color: #458b00;}
    .chocolate {background-color: #d2691e; color: #d2691e;}
    .chocolate1 {background-color: #ff7f24; color: #ff7f24;}
    .chocolate2 {background-color: #ee7621; color: #ee7621;}
    .chocolate3 {background-color: #cd661d; color: #cd661d;}
    .chocolate4 {background-color: #8b4513; color: #8b4513;}
    .coral {background-color: #ff7f50; color: #ff7f50;}
    .coral1 {background-color: #ff7256; color: #ff7256;}
    .coral2 {background-color: #ee6a50; color: #ee6a50;}
    .coral3 {background-color: #cd5b45; color: #cd5b45;}
    .coral4 {background-color: #8b3e2f; color: #8b3e2f;}
    .cornflowerblue {background-color: #6495ed; color: #6495ed;}
    .cornsilk {background-color: #fff8dc; color: #fff8dc;}
    .cornsilk1 {background-color: #fff8dc; color: #fff8dc;}
    .cornsilk2 {background-color: #eee8cd; color: #eee8cd;}
    .cornsilk3 {background-color: #cdc8b1; color: #cdc8b1;}
    .cornsilk4 {background-color: #8b8878; color: #8b8878;}
    .crimson {background-color: #dc143c; color: #dc143c;}
    .cyan {background-color: #00ffff; color: #00ffff;}
    .cyan1 {background-color: #00ffff; color: #00ffff;}
    .cyan2 {background-color: #00eeee; color: #00eeee;}
    .cyan3 {background-color: #00cdcd; color: #00cdcd;}
    .cyan4 {background-color: #008b8b; color: #008b8b;}
    .darkblue {background-color: #00008b; color: #00008b;}
    .darkcyan {background-color: #008b8b; color: #008b8b;}
    .darkgoldenrod {background-color: #b8860b; color: #b8860b;}
    .darkgoldenrod1 {background-color: #ffb90f; color: #ffb90f;}
    .darkgoldenrod2 {background-color: #eead0e; color: #eead0e;}
    .darkgoldenrod3 {background-color: #cd950c; color: #cd950c;}
    .darkgoldenrod4 {background-color: #8b6508; color: #8b6508;}
    .darkgray {background-color: #a9a9a9; color: #a9a9a9;}
    .darkgreen {background-color: #006400; color: #006400;}
    .darkgrey {background-color: #a9a9a9; color: #a9a9a9;}
    .darkkhaki {background-color: #bdb76b; color: #bdb76b;}
    .darkmagenta {background-color: #8b008b; color: #8b008b;}
    .darkolivegreen {background-color: #556b2f; color: #556b2f;}
    .darkolivegreen1 {background-color: #caff70; color: #caff70;}
    .darkolivegreen2 {background-color: #bcee68; color: #bcee68;}
    .darkolivegreen3 {background-color: #a2cd5a; color: #a2cd5a;}
    .darkolivegreen4 {background-color: #6e8b3d; color: #6e8b3d;}
    .darkorange {background-color: #ff8c00; color: #ff8c00;}
    .darkorange1 {background-color: #ff7f00; color: #ff7f00;}
    .darkorange2 {background-color: #ee7600; color: #ee7600;}
    .darkorange3 {background-color: #cd6600; color: #cd6600;}
    .darkorange4 {background-color: #8b4500; color: #8b4500;}
    .darkorchid {background-color: #9932cc; color: #9932cc;}
    .darkorchid1 {background-color: #bf3eff; color: #bf3eff;}
    .darkorchid2 {background-color: #b23aee; color: #b23aee;}
    .darkorchid3 {background-color: #9a32cd; color: #9a32cd;}
    .darkorchid4 {background-color: #68228b; color: #68228b;}
    .darkred {background-color: #8b0000; color: #8b0000;}
    .darksalmon {background-color: #e9967a; color: #e9967a;}
    .darkseagreen {background-color: #8fbc8f; color: #8fbc8f;}
    .darkseagreen1 {background-color: #c1ffc1; color: #c1ffc1;}
    .darkseagreen2 {background-color: #b4eeb4; color: #b4eeb4;}
    .darkseagreen3 {background-color: #9bcd9b; color: #9bcd9b;}
    .darkseagreen4 {background-color: #698b69; color: #698b69;}
    .darkslateblue {background-color: #483d8b; color: #483d8b;}
    .darkslategray {background-color: #2f4f4f; color: #2f4f4f;}
    .darkslategray1 {background-color: #97ffff; color: #97ffff;}
    .darkslategray2 {background-color: #8deeee; color: #8deeee;}
    .darkslategray3 {background-color: #79cdcd; color: #79cdcd;}
    .darkslategray4 {background-color: #528b8b; color: #528b8b;}
    .darkslategrey {background-color: #2f4f4f; color: #2f4f4f;}
    .darkturquoise {background-color: #00ced1; color: #00ced1;}
    .darkviolet {background-color: #9400d3; color: #9400d3;}
    .deeppink {background-color: #ff1493; color: #ff1493;}
    .deeppink1 {background-color: #ff1493; color: #ff1493;}
    .deeppink2 {background-color: #ee1289; color: #ee1289;}
    .deeppink3 {background-color: #cd1076; color: #cd1076;}
    .deeppink4 {background-color: #8b0a50; color: #8b0a50;}
    .deepskyblue {background-color: #00bfff; color: #00bfff;}
    .deepskyblue1 {background-color: #00bfff; color: #00bfff;}
    .deepskyblue2 {background-color: #00b2ee; color: #00b2ee;}
    .deepskyblue3 {background-color: #009acd; color: #009acd;}
    .deepskyblue4 {background-color: #00688b; color: #00688b;}
    .dimgray {background-color: #696969; color: #696969;}
    .dimgrey {background-color: #696969; color: #696969;}
    .dodgerblue {background-color: #1e90ff; color: #1e90ff;}
    .dodgerblue1 {background-color: #1e90ff; color: #1e90ff;}
    .dodgerblue2 {background-color: #1c86ee; color: #1c86ee;}
    .dodgerblue3 {background-color: #1874cd; color: #1874cd;}
    .dodgerblue4 {background-color: #104e8b; color: #104e8b;}
    .firebrick {background-color: #b22222; color: #b22222;}
    .firebrick1 {background-color: #ff3030; color: #ff3030;}
    .firebrick2 {background-color: #ee2c2c; color: #ee2c2c;}
    .firebrick3 {background-color: #cd2626; color: #cd2626;}
    .firebrick4 {background-color: #8b1a1a; color: #8b1a1a;}
    .floralwhite {background-color: #fffaf0; color: #fffaf0;}
    .forestgreen {background-color: #228b22; color: #228b22;}
    .fuchsia {background-color: #ff00ff; color: #ff00ff;}
    .gainsboro {background-color: #dcdcdc; color: #dcdcdc;}
    .ghostwhite {background-color: #f8f8ff; color: #f8f8ff;}
    .gold {background-color: #ffd700; color: #ffd700;}
    .gold1 {background-color: #ffd700; color: #ffd700;}
    .gold2 {background-color: #eec900; color: #eec900;}
    .gold3 {background-color: #cdad00; color: #cdad00;}
    .gold4 {background-color: #8b7500; color: #8b7500;}
    .goldenrod {background-color: #daa520; color: #daa520;}
    .goldenrod1 {background-color: #ffc125; color: #ffc125;}
    .goldenrod2 {background-color: #eeb422; color: #eeb422;}
    .goldenrod3 {background-color: #cd9b1d; color: #cd9b1d;}
    .goldenrod4 {background-color: #8b6914; color: #8b6914;}
    .gray {background-color: #bebebe; color: #bebebe;}
    .gray0 {background-color: #000000; color: #000000;}
    .gray1 {background-color: #030303; color: #030303;}
    .gray2 {background-color: #050505; color: #050505;}
    .gray3 {background-color: #080808; color: #080808;}
    .gray4 {background-color: #0a0a0a; color: #0a0a0a;}
    .gray5 {background-color: #0d0d0d; color: #0d0d0d;}
    .gray6 {background-color: #0f0f0f; color: #0f0f0f;}
    .gray7 {background-color: #121212; color: #121212;}
    .gray8 {background-color: #141414; color: #141414;}
    .gray9 {background-color: #171717; color: #171717;}
    .gray10 {background-color: #1a1a1a; color: #1a1a1a;}
    .gray11 {background-color: #1c1c1c; color: #1c1c1c;}
    .gray12 {background-color: #1f1f1f; color: #1f1f1f;}
    .gray13 {background-color: #212121; color: #212121;}
    .gray14 {background-color: #242424; color: #242424;}
    .gray15 {background-color: #262626; color: #262626;}
    .gray16 {background-color: #292929; color: #292929;}
    .gray17 {background-color: #2b2b2b; color: #2b2b2b;}
    .gray18 {background-color: #2e2e2e; color: #2e2e2e;}
    .gray19 {background-color: #303030; color: #303030;}
    .gray20 {background-color: #333333; color: #333333;}
    .gray21 {background-color: #363636; color: #363636;}
    .gray22 {background-color: #383838; color: #383838;}
    .gray23 {background-color: #3b3b3b; color: #3b3b3b;}
    .gray24 {background-color: #3d3d3d; color: #3d3d3d;}
    .gray25 {background-color: #404040; color: #404040;}
    .gray26 {background-color: #424242; color: #424242;}
    .gray27 {background-color: #454545; color: #454545;}
    .gray28 {background-color: #474747; color: #474747;}
    .gray29 {background-color: #4a4a4a; color: #4a4a4a;}
    .gray30 {background-color: #4d4d4d; color: #4d4d4d;}
    .gray31 {background-color: #4f4f4f; color: #4f4f4f;}
    .gray32 {background-color: #525252; color: #525252;}
    .gray33 {background-color: #545454; color: #545454;}
    .gray34 {background-color: #575757; color: #575757;}
    .gray35 {background-color: #595959; color: #595959;}
    .gray36 {background-color: #5c5c5c; color: #5c5c5c;}
    .gray37 {background-color: #5e5e5e; color: #5e5e5e;}
    .gray38 {background-color: #616161; color: #616161;}
    .gray39 {background-color: #636363; color: #636363;}
    .gray40 {background-color: #666666; color: #666666;}
    .gray41 {background-color: #696969; color: #696969;}
    .gray42 {background-color: #6b6b6b; color: #6b6b6b;}
    .gray43 {background-color: #6e6e6e; color: #6e6e6e;}
    .gray44 {background-color: #707070; color: #707070;}
    .gray45 {background-color: #737373; color: #737373;}
    .gray46 {background-color: #757575; color: #757575;}
    .gray47 {background-color: #787878; color: #787878;}
    .gray48 {background-color: #7a7a7a; color: #7a7a7a;}
    .gray49 {background-color: #7d7d7d; color: #7d7d7d;}
    .gray50 {background-color: #7f7f7f; color: #7f7f7f;}
    .gray51 {background-color: #828282; color: #828282;}
    .gray52 {background-color: #858585; color: #858585;}
    .gray53 {background-color: #878787; color: #878787;}
    .gray54 {background-color: #8a8a8a; color: #8a8a8a;}
    .gray55 {background-color: #8c8c8c; color: #8c8c8c;}
    .gray56 {background-color: #8f8f8f; color: #8f8f8f;}
    .gray57 {background-color: #919191; color: #919191;}
    .gray58 {background-color: #949494; color: #949494;}
    .gray59 {background-color: #969696; color: #969696;}
    .gray60 {background-color: #999999; color: #999999;}
    .gray61 {background-color: #9c9c9c; color: #9c9c9c;}
    .gray62 {background-color: #9e9e9e; color: #9e9e9e;}
    .gray63 {background-color: #a1a1a1; color: #a1a1a1;}
    .gray64 {background-color: #a3a3a3; color: #a3a3a3;}
    .gray65 {background-color: #a6a6a6; color: #a6a6a6;}
    .gray66 {background-color: #a8a8a8; color: #a8a8a8;}
    .gray67 {background-color: #ababab; color: #ababab;}
    .gray68 {background-color: #adadad; color: #adadad;}
    .gray69 {background-color: #b0b0b0; color: #b0b0b0;}
    .gray70 {background-color: #b3b3b3; color: #b3b3b3;}
    .gray71 {background-color: #b5b5b5; color: #b5b5b5;}
    .gray72 {background-color: #b8b8b8; color: #b8b8b8;}
    .gray73 {background-color: #bababa; color: #bababa;}
    .gray74 {background-color: #bdbdbd; color: #bdbdbd;}
    .gray75 {background-color: #bfbfbf; color: #bfbfbf;}
    .gray76 {background-color: #c2c2c2; color: #c2c2c2;}
    .gray77 {background-color: #c4c4c4; color: #c4c4c4;}
    .gray78 {background-color: #c7c7c7; color: #c7c7c7;}
    .gray79 {background-color: #c9c9c9; color: #c9c9c9;}
    .gray80 {background-color: #cccccc; color: #cccccc;}
    .gray81 {background-color: #cfcfcf; color: #cfcfcf;}
    .gray82 {background-color: #d1d1d1; color: #d1d1d1;}
    .gray83 {background-color: #d4d4d4; color: #d4d4d4;}
    .gray84 {background-color: #d6d6d6; color: #d6d6d6;}
    .gray85 {background-color: #d9d9d9; color: #d9d9d9;}
    .gray86 {background-color: #dbdbdb; color: #dbdbdb;}
    .gray87 {background-color: #dedede; color: #dedede;}
    .gray88 {background-color: #e0e0e0; color: #e0e0e0;}
    .gray89 {background-color: #e3e3e3; color: #e3e3e3;}
    .gray90 {background-color: #e5e5e5; color: #e5e5e5;}
    .gray91 {background-color: #e8e8e8; color: #e8e8e8;}
    .gray92 {background-color: #ebebeb; color: #ebebeb;}
    .gray93 {background-color: #ededed; color: #ededed;}
    .gray94 {background-color: #f0f0f0; color: #f0f0f0;}
    .gray95 {background-color: #f2f2f2; color: #f2f2f2;}
    .gray96 {background-color: #f5f5f5; color: #f5f5f5;}
    .gray97 {background-color: #f7f7f7; color: #f7f7f7;}
    .gray98 {background-color: #fafafa; color: #fafafa;}
    .gray99 {background-color: #fcfcfc; color: #fcfcfc;}
    .gray100 {background-color: #ffffff; color: #ffffff;}
    .green {background-color: #00ff00; color: #00ff00;}
    .green1 {background-color: #00ff00; color: #00ff00;}
    .green2 {background-color: #00ee00; color: #00ee00;}
    .green3 {background-color: #00cd00; color: #00cd00;}
    .green4 {background-color: #008b00; color: #008b00;}
    .greenyellow {background-color: #adff2f; color: #adff2f;}
    .grey {background-color: #bebebe; color: #bebebe;}
    .grey0 {background-color: #000000; color: #000000;}
    .grey1 {background-color: #030303; color: #030303;}
    .grey2 {background-color: #050505; color: #050505;}
    .grey3 {background-color: #080808; color: #080808;}
    .grey4 {background-color: #0a0a0a; color: #0a0a0a;}
    .grey5 {background-color: #0d0d0d; color: #0d0d0d;}
    .grey6 {background-color: #0f0f0f; color: #0f0f0f;}
    .grey7 {background-color: #121212; color: #121212;}
    .grey8 {background-color: #141414; color: #141414;}
    .grey9 {background-color: #171717; color: #171717;}
    .grey10 {background-color: #1a1a1a; color: #1a1a1a;}
    .grey11 {background-color: #1c1c1c; color: #1c1c1c;}
    .grey12 {background-color: #1f1f1f; color: #1f1f1f;}
    .grey13 {background-color: #212121; color: #212121;}
    .grey14 {background-color: #242424; color: #242424;}
    .grey15 {background-color: #262626; color: #262626;}
    .grey16 {background-color: #292929; color: #292929;}
    .grey17 {background-color: #2b2b2b; color: #2b2b2b;}
    .grey18 {background-color: #2e2e2e; color: #2e2e2e;}
    .grey19 {background-color: #303030; color: #303030;}
    .grey20 {background-color: #333333; color: #333333;}
    .grey21 {background-color: #363636; color: #363636;}
    .grey22 {background-color: #383838; color: #383838;}
    .grey23 {background-color: #3b3b3b; color: #3b3b3b;}
    .grey24 {background-color: #3d3d3d; color: #3d3d3d;}
    .grey25 {background-color: #404040; color: #404040;}
    .grey26 {background-color: #424242; color: #424242;}
    .grey27 {background-color: #454545; color: #454545;}
    .grey28 {background-color: #474747; color: #474747;}
    .grey29 {background-color: #4a4a4a; color: #4a4a4a;}
    .grey30 {background-color: #4d4d4d; color: #4d4d4d;}
    .grey31 {background-color: #4f4f4f; color: #4f4f4f;}
    .grey32 {background-color: #525252; color: #525252;}
    .grey33 {background-color: #545454; color: #545454;}
    .grey34 {background-color: #575757; color: #575757;}
    .grey35 {background-color: #595959; color: #595959;}
    .grey36 {background-color: #5c5c5c; color: #5c5c5c;}
    .grey37 {background-color: #5e5e5e; color: #5e5e5e;}
    .grey38 {background-color: #616161; color: #616161;}
    .grey39 {background-color: #636363; color: #636363;}
    .grey40 {background-color: #666666; color: #666666;}
    .grey41 {background-color: #696969; color: #696969;}
    .grey42 {background-color: #6b6b6b; color: #6b6b6b;}
    .grey43 {background-color: #6e6e6e; color: #6e6e6e;}
    .grey44 {background-color: #707070; color: #707070;}
    .grey45 {background-color: #737373; color: #737373;}
    .grey46 {background-color: #757575; color: #757575;}
    .grey47 {background-color: #787878; color: #787878;}
    .grey48 {background-color: #7a7a7a; color: #7a7a7a;}
    .grey49 {background-color: #7d7d7d; color: #7d7d7d;}
    .grey50 {background-color: #7f7f7f; color: #7f7f7f;}
    .grey51 {background-color: #828282; color: #828282;}
    .grey52 {background-color: #858585; color: #858585;}
    .grey53 {background-color: #878787; color: #878787;}
    .grey54 {background-color: #8a8a8a; color: #8a8a8a;}
    .grey55 {background-color: #8c8c8c; color: #8c8c8c;}
    .grey56 {background-color: #8f8f8f; color: #8f8f8f;}
    .grey57 {background-color: #919191; color: #919191;}
    .grey58 {background-color: #949494; color: #949494;}
    .grey59 {background-color: #969696; color: #969696;}
    .grey60 {background-color: #999999; color: #999999;}
    .grey61 {background-color: #9c9c9c; color: #9c9c9c;}
    .grey62 {background-color: #9e9e9e; color: #9e9e9e;}
    .grey63 {background-color: #a1a1a1; color: #a1a1a1;}
    .grey64 {background-color: #a3a3a3; color: #a3a3a3;}
    .grey65 {background-color: #a6a6a6; color: #a6a6a6;}
    .grey66 {background-color: #a8a8a8; color: #a8a8a8;}
    .grey67 {background-color: #ababab; color: #ababab;}
    .grey68 {background-color: #adadad; color: #adadad;}
    .grey69 {background-color: #b0b0b0; color: #b0b0b0;}
    .grey70 {background-color: #b3b3b3; color: #b3b3b3;}
    .grey71 {background-color: #b5b5b5; color: #b5b5b5;}
    .grey72 {background-color: #b8b8b8; color: #b8b8b8;}
    .grey73 {background-color: #bababa; color: #bababa;}
    .grey74 {background-color: #bdbdbd; color: #bdbdbd;}
    .grey75 {background-color: #bfbfbf; color: #bfbfbf;}
    .grey76 {background-color: #c2c2c2; color: #c2c2c2;}
    .grey77 {background-color: #c4c4c4; color: #c4c4c4;}
    .grey78 {background-color: #c7c7c7; color: #c7c7c7;}
    .grey79 {background-color: #c9c9c9; color: #c9c9c9;}
    .grey80 {background-color: #cccccc; color: #cccccc;}
    .grey81 {background-color: #cfcfcf; color: #cfcfcf;}
    .grey82 {background-color: #d1d1d1; color: #d1d1d1;}
    .grey83 {background-color: #d4d4d4; color: #d4d4d4;}
    .grey84 {background-color: #d6d6d6; color: #d6d6d6;}
    .grey85 {background-color: #d9d9d9; color: #d9d9d9;}
    .grey86 {background-color: #dbdbdb; color: #dbdbdb;}
    .grey87 {background-color: #dedede; color: #dedede;}
    .grey88 {background-color: #e0e0e0; color: #e0e0e0;}
    .grey89 {background-color: #e3e3e3; color: #e3e3e3;}
    .grey90 {background-color: #e5e5e5; color: #e5e5e5;}
    .grey91 {background-color: #e8e8e8; color: #e8e8e8;}
    .grey92 {background-color: #ebebeb; color: #ebebeb;}
    .grey93 {background-color: #ededed; color: #ededed;}
    .grey94 {background-color: #f0f0f0; color: #f0f0f0;}
    .grey95 {background-color: #f2f2f2; color: #f2f2f2;}
    .grey96 {background-color: #f5f5f5; color: #f5f5f5;}
    .grey97 {background-color: #f7f7f7; color: #f7f7f7;}
    .grey98 {background-color: #fafafa; color: #fafafa;}
    .grey99 {background-color: #fcfcfc; color: #fcfcfc;}
    .grey100 {background-color: #ffffff; color: #ffffff;}
    .honeydew {background-color: #f0fff0; color: #f0fff0;}
    .honeydew1 {background-color: #f0fff0; color: #f0fff0;}
    .honeydew2 {background-color: #e0eee0; color: #e0eee0;}
    .honeydew3 {background-color: #c1cdc1; color: #c1cdc1;}
    .honeydew4 {background-color: #838b83; color: #838b83;}
    .hotpink {background-color: #ff69b4; color: #ff69b4;}
    .hotpink1 {background-color: #ff6eb4; color: #ff6eb4;}
    .hotpink2 {background-color: #ee6aa7; color: #ee6aa7;}
    .hotpink3 {background-color: #cd6090; color: #cd6090;}
    .hotpink4 {background-color: #8b3a62; color: #8b3a62;}
    .indianred {background-color: #cd5c5c; color: #cd5c5c;}
    .indianred1 {background-color: #ff6a6a; color: #ff6a6a;}
    .indianred2 {background-color: #ee6363; color: #ee6363;}
    .indianred3 {background-color: #cd5555; color: #cd5555;}
    .indianred4 {background-color: #8b3a3a; color: #8b3a3a;}
    .indigo {background-color: #4b0082; color: #4b0082;}
    .ivory {background-color: #fffff0; color: #fffff0;}
    .ivory1 {background-color: #fffff0; color: #fffff0;}
    .ivory2 {background-color: #eeeee0; color: #eeeee0;}
    .ivory3 {background-color: #cdcdc1; color: #cdcdc1;}
    .ivory4 {background-color: #8b8b83; color: #8b8b83;}
    .khaki {background-color: #f0e68c; color: #f0e68c;}
    .khaki1 {background-color: #fff68f; color: #fff68f;}
    .khaki2 {background-color: #eee685; color: #eee685;}
    .khaki3 {background-color: #cdc673; color: #cdc673;}
    .khaki4 {background-color: #8b864e; color: #8b864e;}
    .lavender {background-color: #e6e6fa; color: #e6e6fa;}
    .lavenderblush {background-color: #fff0f5; color: #fff0f5;}
    .lavenderblush1 {background-color: #fff0f5; color: #fff0f5;}
    .lavenderblush2 {background-color: #eee0e5; color: #eee0e5;}
    .lavenderblush3 {background-color: #cdc1c5; color: #cdc1c5;}
    .lavenderblush4 {background-color: #8b8386; color: #8b8386;}
    .lawngreen {background-color: #7cfc00; color: #7cfc00;}
    .lemonchiffon {background-color: #fffacd; color: #fffacd;}
    .lemonchiffon1 {background-color: #fffacd; color: #fffacd;}
    .lemonchiffon2 {background-color: #eee9bf; color: #eee9bf;}
    .lemonchiffon3 {background-color: #cdc9a5; color: #cdc9a5;}
    .lemonchiffon4 {background-color: #8b8970; color: #8b8970;}
    .lightblue {background-color: #add8e6; color: #add8e6;}
    .lightblue1 {background-color: #bfefff; color: #bfefff;}
    .lightblue2 {background-color: #b2dfee; color: #b2dfee;}
    .lightblue3 {background-color: #9ac0cd; color: #9ac0cd;}
    .lightblue4 {background-color: #68838b; color: #68838b;}
    .lightcoral {background-color: #f08080; color: #f08080;}
    .lightcyan {background-color: #e0ffff; color: #e0ffff;}
    .lightcyan1 {background-color: #e0ffff; color: #e0ffff;}
    .lightcyan2 {background-color: #d1eeee; color: #d1eeee;}
    .lightcyan3 {background-color: #b4cdcd; color: #b4cdcd;}
    .lightcyan4 {background-color: #7a8b8b; color: #7a8b8b;}
    .lightgoldenrod {background-color: #eedd82; color: #eedd82;}
    .lightgoldenrod1 {background-color: #ffec8b; color: #ffec8b;}
    .lightgoldenrod2 {background-color: #eedc82; color: #eedc82;}
    .lightgoldenrod3 {background-color: #cdbe70; color: #cdbe70;}
    .lightgoldenrod4 {background-color: #8b814c; color: #8b814c;}
    .lightgoldenrodyellow {background-color: #fafad2; color: #fafad2;}
    .lightgray {background-color: #d3d3d3; color: #d3d3d3;}
    .lightgreen {background-color: #90ee90; color: #90ee90;}
    .lightgrey {background-color: #d3d3d3; color: #d3d3d3;}
    .lightpink {background-color: #ffb6c1; color: #ffb6c1;}
    .lightpink1 {background-color: #ffaeb9; color: #ffaeb9;}
    .lightpink2 {background-color: #eea2ad; color: #eea2ad;}
    .lightpink3 {background-color: #cd8c95; color: #cd8c95;}
    .lightpink4 {background-color: #8b5f65; color: #8b5f65;}
    .lightsalmon {background-color: #ffa07a; color: #ffa07a;}
    .lightsalmon1 {background-color: #ffa07a; color: #ffa07a;}
    .lightsalmon2 {background-color: #ee9572; color: #ee9572;}
    .lightsalmon3 {background-color: #cd8162; color: #cd8162;}
    .lightsalmon4 {background-color: #8b5742; color: #8b5742;}
    .lightseagreen {background-color: #20b2aa; color: #20b2aa;}
    .lightskyblue {background-color: #87cefa; color: #87cefa;}
    .lightskyblue1 {background-color: #b0e2ff; color: #b0e2ff;}
    .lightskyblue2 {background-color: #a4d3ee; color: #a4d3ee;}
    .lightskyblue3 {background-color: #8db6cd; color: #8db6cd;}
    .lightskyblue4 {background-color: #607b8b; color: #607b8b;}
    .lightslateblue {background-color: #8470ff; color: #8470ff;}
    .lightslategray {background-color: #778899; color: #778899;}
    .lightslategrey {background-color: #778899; color: #778899;}
    .lightsteelblue {background-color: #b0c4de; color: #b0c4de;}
    .lightsteelblue1 {background-color: #cae1ff; color: #cae1ff;}
    .lightsteelblue2 {background-color: #bcd2ee; color: #bcd2ee;}
    .lightsteelblue3 {background-color: #a2b5cd; color: #a2b5cd;}
    .lightsteelblue4 {background-color: #6e7b8b; color: #6e7b8b;}
    .lightyellow {background-color: #ffffe0; color: #ffffe0;}
    .lightyellow1 {background-color: #ffffe0; color: #ffffe0;}
    .lightyellow2 {background-color: #eeeed1; color: #eeeed1;}
    .lightyellow3 {background-color: #cdcdb4; color: #cdcdb4;}
    .lightyellow4 {background-color: #8b8b7a; color: #8b8b7a;}
    .linen {background-color: #faf0e6; color: #faf0e6;}
    .limegreen {background-color: #32cd32; color: #32cd32;}
    .lime {background-color: #00ff00; color: #00ff00;}
    .magenta {background-color: #ff00ff; color: #ff00ff;}
    .magenta1 {background-color: #ff00ff; color: #ff00ff;}
    .magenta2 {background-color: #ee00ee; color: #ee00ee;}
    .magenta3 {background-color: #cd00cd; color: #cd00cd;}
    .magenta4 {background-color: #8b008b; color: #8b008b;}
    .maroon {background-color: #b03060; color: #b03060;}
    .maroon1 {background-color: #ff34b3; color: #ff34b3;}
    .maroon2 {background-color: #ee30a7; color: #ee30a7;}
    .maroon3 {background-color: #cd2990; color: #cd2990;}
    .maroon4 {background-color: #8b1c62; color: #8b1c62;}
    .mediumaquamarine {background-color: #66cdaa; color: #66cdaa;}
    .mediumblue {background-color: #0000cd; color: #0000cd;}
    .mediumorchid {background-color: #ba55d3; color: #ba55d3;}
    .mediumorchid1 {background-color: #e066ff; color: #e066ff;}
    .mediumorchid2 {background-color: #d15fee; color: #d15fee;}
    .mediumorchid3 {background-color: #b452cd; color: #b452cd;}
    .mediumorchid4 {background-color: #7a378b; color: #7a378b;}
    .mediumpurple {background-color: #9370db; color: #9370db;}
    .mediumpurple1 {background-color: #ab82ff; color: #ab82ff;}
    .mediumpurple2 {background-color: #9f79ee; color: #9f79ee;}
    .mediumpurple3 {background-color: #8968cd; color: #8968cd;}
    .mediumpurple4 {background-color: #5d478b; color: #5d478b;}
    .mediumseagreen {background-color: #3cb371; color: #3cb371;}
    .mediumslateblue {background-color: #7b68ee; color: #7b68ee;}
    .mediumspringgreen {background-color: #00fa9a; color: #00fa9a;}
    .mediumturquoise {background-color: #48d1cc; color: #48d1cc;}
    .mediumvioletred {background-color: #c71585; color: #c71585;}
    .midnightblue {background-color: #191970; color: #191970;}
    .mintcream {background-color: #f5fffa; color: #f5fffa;}
    .mistyrose {background-color: #ffe4e1; color: #ffe4e1;}
    .mistyrose1 {background-color: #ffe4e1; color: #ffe4e1;}
    .mistyrose2 {background-color: #eed5d2; color: #eed5d2;}
    .mistyrose3 {background-color: #cdb7b5; color: #cdb7b5;}
    .mistyrose4 {background-color: #8b7d7b; color: #8b7d7b;}
    .moccasin {background-color: #ffe4b5; color: #ffe4b5;}
    .navajowhite {background-color: #ffdead; color: #ffdead;}
    .navajowhite1 {background-color: #ffdead; color: #ffdead;}
    .navajowhite2 {background-color: #eecfa1; color: #eecfa1;}
    .navajowhite3 {background-color: #cdb38b; color: #cdb38b;}
    .navajowhite4 {background-color: #8b795e; color: #8b795e;}
    .navy {background-color: #000080; color: #000080;}
    .navyblue {background-color: #000080; color: #000080;}
    .oldlace {background-color: #fdf5e6; color: #fdf5e6;}
    .olive {background-color: #808000; color: #808000;}
    .olivedrab {background-color: #6b8e23; color: #6b8e23;}
    .olivedrab1 {background-color: #c0ff3e; color: #c0ff3e;}
    .olivedrab2 {background-color: #b3ee3a; color: #b3ee3a;}
    .olivedrab3 {background-color: #9acd32; color: #9acd32;}
    .olivedrab4 {background-color: #698b22; color: #698b22;}
    .orange {background-color: #ffa500; color: #ffa500;}
    .orange1 {background-color: #ffa500; color: #ffa500;}
    .orange2 {background-color: #ee9a00; color: #ee9a00;}
    .orange3 {background-color: #cd8500; color: #cd8500;}
    .orange4 {background-color: #8b5a00; color: #8b5a00;}
    .orangered {background-color: #ff4500; color: #ff4500;}
    .orangered1 {background-color: #ff4500; color: #ff4500;}
    .orangered2 {background-color: #ee4000; color: #ee4000;}
    .orangered3 {background-color: #cd3700; color: #cd3700;}
    .orangered4 {background-color: #8b2500; color: #8b2500;}
    .orchid {background-color: #da70d6; color: #da70d6;}
    .orchid1 {background-color: #ff83fa; color: #ff83fa;}
    .orchid2 {background-color: #ee7ae9; color: #ee7ae9;}
    .orchid3 {background-color: #cd69c9; color: #cd69c9;}
    .orchid4 {background-color: #8b4789; color: #8b4789;}
    .palegreen {background-color: #98fb98; color: #98fb98;}
    .palegreen1 {background-color: #9aff9a; color: #9aff9a;}
    .palegreen2 {background-color: #90ee90; color: #90ee90;}
    .palegreen3 {background-color: #7ccd7c; color: #7ccd7c;}
    .palegreen4 {background-color: #548b54; color: #548b54;}
    .palegoldenrod {background-color: #eee8aa; color: #eee8aa;}
    .paleturquoise {background-color: #afeeee; color: #afeeee;}
    .paleturquoise1 {background-color: #bbffff; color: #bbffff;}
    .paleturquoise2 {background-color: #aeeeee; color: #aeeeee;}
    .paleturquoise3 {background-color: #96cdcd; color: #96cdcd;}
    .paleturquoise4 {background-color: #668b8b; color: #668b8b;}
    .palevioletred {background-color: #db7093; color: #db7093;}
    .palevioletred1 {background-color: #ff82ab; color: #ff82ab;}
    .palevioletred2 {background-color: #ee799f; color: #ee799f;}
    .palevioletred3 {background-color: #cd6889; color: #cd6889;}
    .palevioletred4 {background-color: #8b475d; color: #8b475d;}
    .papayawhip {background-color: #ffefd5; color: #ffefd5;}
    .peachpuff {background-color: #ffdab9; color: #ffdab9;}
    .peachpuff1 {background-color: #ffdab9; color: #ffdab9;}
    .peachpuff2 {background-color: #eecbad; color: #eecbad;}
    .peachpuff3 {background-color: #cdaf95; color: #cdaf95;}
    .peachpuff4 {background-color: #8b7765; color: #8b7765;}
    .peru {background-color: #cd853f; color: #cd853f;}
    .pink {background-color: #ffc0cb; color: #ffc0cb;}
    .pink1 {background-color: #ffb5c5; color: #ffb5c5;}
    .pink2 {background-color: #eea9b8; color: #eea9b8;}
    .pink3 {background-color: #cd919e; color: #cd919e;}
    .pink4 {background-color: #8b636c; color: #8b636c;}
    .plum {background-color: #dda0dd; color: #dda0dd;}
    .plum1 {background-color: #ffbbff; color: #ffbbff;}
    .plum2 {background-color: #eeaeee; color: #eeaeee;}
    .plum3 {background-color: #cd96cd; color: #cd96cd;}
    .plum4 {background-color: #8b668b; color: #8b668b;}
    .powderblue {background-color: #b0e0e6; color: #b0e0e6;}
    .purple {background-color: #a020f0; color: #a020f0;}
    .purple1 {background-color: #9b30ff; color: #9b30ff;}
    .purple2 {background-color: #912cee; color: #912cee;}
    .purple3 {background-color: #7d26cd; color: #7d26cd;}
    .purple4 {background-color: #551a8b; color: #551a8b;}
    .red {background-color: #ff0000; color: #ff0000;}
    .red1 {background-color: #ff0000; color: #ff0000;}
    .red2 {background-color: #ee0000; color: #ee0000;}
    .red3 {background-color: #cd0000; color: #cd0000;}
    .red4 {background-color: #8b0000; color: #8b0000;}
    .rosybrown {background-color: #bc8f8f; color: #bc8f8f;}
    .rosybrown1 {background-color: #ffc1c1; color: #ffc1c1;}
    .rosybrown2 {background-color: #eeb4b4; color: #eeb4b4;}
    .rosybrown3 {background-color: #cd9b9b; color: #cd9b9b;}
    .rosybrown4 {background-color: #8b6969; color: #8b6969;}
    .royalblue {background-color: #4169e1; color: #4169e1;}
    .royalblue1 {background-color: #4876ff; color: #4876ff;}
    .royalblue2 {background-color: #436eee; color: #436eee;}
    .royalblue3 {background-color: #3a5fcd; color: #3a5fcd;}
    .royalblue4 {background-color: #27408b; color: #27408b;}
    .salmon {background-color: #fa8072; color: #fa8072;}
    .salmon1 {background-color: #ff8c69; color: #ff8c69;}
    .salmon2 {background-color: #ee8262; color: #ee8262;}
    .salmon3 {background-color: #cd7054; color: #cd7054;}
    .salmon4 {background-color: #8b4c39; color: #8b4c39;}
    .saddlebrown {background-color: #8b4513; color: #8b4513;}
    .sandybrown {background-color: #f4a460; color: #f4a460;}
    .seagreen {background-color: #2e8b57; color: #2e8b57;}
    .seagreen1 {background-color: #54ff9f; color: #54ff9f;}
    .seagreen2 {background-color: #4eee94; color: #4eee94;}
    .seagreen3 {background-color: #43cd80; color: #43cd80;}
    .seagreen4 {background-color: #2e8b57; color: #2e8b57;}
    .seashell {background-color: #fff5ee; color: #fff5ee;}
    .seashell1 {background-color: #fff5ee; color: #fff5ee;}
    .seashell2 {background-color: #eee5de; color: #eee5de;}
    .seashell3 {background-color: #cdc5bf; color: #cdc5bf;}
    .seashell4 {background-color: #8b8682; color: #8b8682;}
    .sienna {background-color: #a0522d; color: #a0522d;}
    .sienna1 {background-color: #ff8247; color: #ff8247;}
    .sienna2 {background-color: #ee7942; color: #ee7942;}
    .sienna3 {background-color: #cd6839; color: #cd6839;}
    .sienna4 {background-color: #8b4726; color: #8b4726;}
    .silver {background-color: #c0c0c0; color: #c0c0c0;}
    .skyblue {background-color: #87ceeb; color: #87ceeb;}
    .skyblue1 {background-color: #87ceff; color: #87ceff;}
    .skyblue2 {background-color: #7ec0ee; color: #7ec0ee;}
    .skyblue3 {background-color: #6ca6cd; color: #6ca6cd;}
    .skyblue4 {background-color: #4a708b; color: #4a708b;}
    .slateblue {background-color: #6a5acd; color: #6a5acd;}
    .slateblue1 {background-color: #836fff; color: #836fff;}
    .slateblue2 {background-color: #7a67ee; color: #7a67ee;}
    .slateblue3 {background-color: #6959cd; color: #6959cd;}
    .slateblue4 {background-color: #473c8b; color: #473c8b;}
    .slategray {background-color: #708090; color: #708090;}
    .slategray1 {background-color: #c6e2ff; color: #c6e2ff;}
    .slategray2 {background-color: #b9d3ee; color: #b9d3ee;}
    .slategray3 {background-color: #9fb6cd; color: #9fb6cd;}
    .slategray4 {background-color: #6c7b8b; color: #6c7b8b;}
    .slategrey {background-color: #708090; color: #708090;}
    .snow {background-color: #fffafa; color: #fffafa;}
    .snow1 {background-color: #fffafa; color: #fffafa;}
    .snow2 {background-color: #eee9e9; color: #eee9e9;}
    .snow3 {background-color: #cdc9c9; color: #cdc9c9;}
    .snow4 {background-color: #8b8989; color: #8b8989;}
    .springgreen {background-color: #00ff7f; color: #00ff7f;}
    .springgreen1 {background-color: #00ff7f; color: #00ff7f;}
    .springgreen2 {background-color: #00ee76; color: #00ee76;}
    .springgreen3 {background-color: #00cd66; color: #00cd66;}
    .springgreen4 {background-color: #008b45; color: #008b45;}
    .steelblue {background-color: #4682b4; color: #4682b4;}
    .steelblue1 {background-color: #63b8ff; color: #63b8ff;}
    .steelblue2 {background-color: #5cacee; color: #5cacee;}
    .steelblue3 {background-color: #4f94cd; color: #4f94cd;}
    .steelblue4 {background-color: #36648b; color: #36648b;}
    .tan {background-color: #d2b48c; color: #d2b48c;}
    .tan1 {background-color: #ffa54f; color: #ffa54f;}
    .tan2 {background-color: #ee9a49; color: #ee9a49;}
    .tan3 {background-color: #cd853f; color: #cd853f;}
    .tan4 {background-color: #8b5a2b; color: #8b5a2b;}
    .teal {background-color: #008080; color: #008080;}
    .thistle {background-color: #d8bfd8; color: #d8bfd8;}
    .thistle1 {background-color: #ffe1ff; color: #ffe1ff;}
    .thistle2 {background-color: #eed2ee; color: #eed2ee;}
    .thistle3 {background-color: #cdb5cd; color: #cdb5cd;}
    .thistle4 {background-color: #8b7b8b; color: #8b7b8b;}
    .tomato {background-color: #ff6347; color: #ff6347;}
    .tomato1 {background-color: #ff6347; color: #ff6347;}
    .tomato2 {background-color: #ee5c42; color: #ee5c42;}
    .tomato3 {background-color: #cd4f39; color: #cd4f39;}
    .tomato4 {background-color: #8b3626; color: #8b3626;}
    .turquoise {background-color: #40e0d0; color: #40e0d0;}
    .turquoise1 {background-color: #00f5ff; color: #00f5ff;}
    .turquoise2 {background-color: #00e5ee; color: #00e5ee;}
    .turquoise3 {background-color: #00c5cd; color: #00c5cd;}
    .turquoise4 {background-color: #00868b; color: #00868b;}
    .violet {background-color: #ee82ee; color: #ee82ee;}
    .violetred {background-color: #d02090; color: #d02090;}
    .violetred1 {background-color: #ff3e96; color: #ff3e96;}
    .violetred2 {background-color: #ee3a8c; color: #ee3a8c;}
    .violetred3 {background-color: #cd3278; color: #cd3278;}
    .violetred4 {background-color: #8b2252; color: #8b2252;}
    .wheat {background-color: #f5deb3; color: #f5deb3;}
    .wheat1 {background-color: #ffe7ba; color: #ffe7ba;}
    .wheat2 {background-color: #eed8ae; color: #eed8ae;}
    .wheat3 {background-color: #cdba96; color: #cdba96;}
    .wheat4 {background-color: #8b7e66; color: #8b7e66;}
    .white {background-color: #ffffff; color: #ffffff;}
    .whitesmoke {background-color: #f5f5f5; color: #f5f5f5;}
    .yellow {background-color: #ffff00; color: #ffff00;}
    .yellow1 {background-color: #ffff00; color: #ffff00;}
    .yellow2 {background-color: #eeee00; color: #eeee00;}
    .yellow3 {background-color: #cdcd00; color: #cdcd00;}
    .yellow4 {background-color: #8b8b00; color: #8b8b00;}
    .yellowgreen {background-color: #9acd32; color: #9acd32;}
    </style>

:doc:`color` lets you specify any of these named colors when creating a new
``pygame.Color`` (taken from the
`colordict module <https://github.com/pygame/pygame/blob/main/src_py/colordict.py>`_).

.. role:: aliceblue
.. role:: antiquewhite
.. role:: antiquewhite1
.. role:: antiquewhite2
.. role:: antiquewhite3
.. role:: antiquewhite4
.. role:: aqua
.. role:: aquamarine
.. role:: aquamarine1
.. role:: aquamarine2
.. role:: aquamarine3
.. role:: aquamarine4
.. role:: azure
.. role:: azure1
.. role:: azure2
.. role:: azure3
.. role:: azure4
.. role:: beige
.. role:: bisque
.. role:: bisque1
.. role:: bisque2
.. role:: bisque3
.. role:: bisque4
.. role:: black
.. role:: blanchedalmond
.. role:: blue
.. role:: blue1
.. role:: blue2
.. role:: blue3
.. role:: blue4
.. role:: blueviolet
.. role:: brown
.. role:: brown1
.. role:: brown2
.. role:: brown3
.. role:: brown4
.. role:: burlywood
.. role:: burlywood1
.. role:: burlywood2
.. role:: burlywood3
.. role:: burlywood4
.. role:: cadetblue
.. role:: cadetblue1
.. role:: cadetblue2
.. role:: cadetblue3
.. role:: cadetblue4
.. role:: chartreuse
.. role:: chartreuse1
.. role:: chartreuse2
.. role:: chartreuse3
.. role:: chartreuse4
.. role:: chocolate
.. role:: chocolate1
.. role:: chocolate2
.. role:: chocolate3
.. role:: chocolate4
.. role:: coral
.. role:: coral1
.. role:: coral2
.. role:: coral3
.. role:: coral4
.. role:: cornflowerblue
.. role:: cornsilk
.. role:: cornsilk1
.. role:: cornsilk2
.. role:: cornsilk3
.. role:: cornsilk4
.. role:: crimson
.. role:: cyan
.. role:: cyan1
.. role:: cyan2
.. role:: cyan3
.. role:: cyan4
.. role:: darkblue
.. role:: darkcyan
.. role:: darkgoldenrod
.. role:: darkgoldenrod1
.. role:: darkgoldenrod2
.. role:: darkgoldenrod3
.. role:: darkgoldenrod4
.. role:: darkgray
.. role:: darkgreen
.. role:: darkgrey
.. role:: darkkhaki
.. role:: darkmagenta
.. role:: darkolivegreen
.. role:: darkolivegreen1
.. role:: darkolivegreen2
.. role:: darkolivegreen3
.. role:: darkolivegreen4
.. role:: darkorange
.. role:: darkorange1
.. role:: darkorange2
.. role:: darkorange3
.. role:: darkorange4
.. role:: darkorchid
.. role:: darkorchid1
.. role:: darkorchid2
.. role:: darkorchid3
.. role:: darkorchid4
.. role:: darkred
.. role:: darksalmon
.. role:: darkseagreen
.. role:: darkseagreen1
.. role:: darkseagreen2
.. role:: darkseagreen3
.. role:: darkseagreen4
.. role:: darkslateblue
.. role:: darkslategray
.. role:: darkslategray1
.. role:: darkslategray2
.. role:: darkslategray3
.. role:: darkslategray4
.. role:: darkslategrey
.. role:: darkturquoise
.. role:: darkviolet
.. role:: deeppink
.. role:: deeppink1
.. role:: deeppink2
.. role:: deeppink3
.. role:: deeppink4
.. role:: deepskyblue
.. role:: deepskyblue1
.. role:: deepskyblue2
.. role:: deepskyblue3
.. role:: deepskyblue4
.. role:: dimgray
.. role:: dimgrey
.. role:: dodgerblue
.. role:: dodgerblue1
.. role:: dodgerblue2
.. role:: dodgerblue3
.. role:: dodgerblue4
.. role:: firebrick
.. role:: firebrick1
.. role:: firebrick2
.. role:: firebrick3
.. role:: firebrick4
.. role:: floralwhite
.. role:: forestgreen
.. role:: fuchsia
.. role:: gainsboro
.. role:: ghostwhite
.. role:: gold
.. role:: gold1
.. role:: gold2
.. role:: gold3
.. role:: gold4
.. role:: goldenrod
.. role:: goldenrod1
.. role:: goldenrod2
.. role:: goldenrod3
.. role:: goldenrod4
.. role:: gray
.. role:: gray0
.. role:: gray1
.. role:: gray2
.. role:: gray3
.. role:: gray4
.. role:: gray5
.. role:: gray6
.. role:: gray7
.. role:: gray8
.. role:: gray9
.. role:: gray10
.. role:: gray11
.. role:: gray12
.. role:: gray13
.. role:: gray14
.. role:: gray15
.. role:: gray16
.. role:: gray17
.. role:: gray18
.. role:: gray19
.. role:: gray20
.. role:: gray21
.. role:: gray22
.. role:: gray23
.. role:: gray24
.. role:: gray25
.. role:: gray26
.. role:: gray27
.. role:: gray28
.. role:: gray29
.. role:: gray30
.. role:: gray31
.. role:: gray32
.. role:: gray33
.. role:: gray34
.. role:: gray35
.. role:: gray36
.. role:: gray37
.. role:: gray38
.. role:: gray39
.. role:: gray40
.. role:: gray41
.. role:: gray42
.. role:: gray43
.. role:: gray44
.. role:: gray45
.. role:: gray46
.. role:: gray47
.. role:: gray48
.. role:: gray49
.. role:: gray50
.. role:: gray51
.. role:: gray52
.. role:: gray53
.. role:: gray54
.. role:: gray55
.. role:: gray56
.. role:: gray57
.. role:: gray58
.. role:: gray59
.. role:: gray60
.. role:: gray61
.. role:: gray62
.. role:: gray63
.. role:: gray64
.. role:: gray65
.. role:: gray66
.. role:: gray67
.. role:: gray68
.. role:: gray69
.. role:: gray70
.. role:: gray71
.. role:: gray72
.. role:: gray73
.. role:: gray74
.. role:: gray75
.. role:: gray76
.. role:: gray77
.. role:: gray78
.. role:: gray79
.. role:: gray80
.. role:: gray81
.. role:: gray82
.. role:: gray83
.. role:: gray84
.. role:: gray85
.. role:: gray86
.. role:: gray87
.. role:: gray88
.. role:: gray89
.. role:: gray90
.. role:: gray91
.. role:: gray92
.. role:: gray93
.. role:: gray94
.. role:: gray95
.. role:: gray96
.. role:: gray97
.. role:: gray98
.. role:: gray99
.. role:: gray100
.. role:: green
.. role:: green1
.. role:: green2
.. role:: green3
.. role:: green4
.. role:: greenyellow
.. role:: grey
.. role:: grey0
.. role:: grey1
.. role:: grey2
.. role:: grey3
.. role:: grey4
.. role:: grey5
.. role:: grey6
.. role:: grey7
.. role:: grey8
.. role:: grey9
.. role:: grey10
.. role:: grey11
.. role:: grey12
.. role:: grey13
.. role:: grey14
.. role:: grey15
.. role:: grey16
.. role:: grey17
.. role:: grey18
.. role:: grey19
.. role:: grey20
.. role:: grey21
.. role:: grey22
.. role:: grey23
.. role:: grey24
.. role:: grey25
.. role:: grey26
.. role:: grey27
.. role:: grey28
.. role:: grey29
.. role:: grey30
.. role:: grey31
.. role:: grey32
.. role:: grey33
.. role:: grey34
.. role:: grey35
.. role:: grey36
.. role:: grey37
.. role:: grey38
.. role:: grey39
.. role:: grey40
.. role:: grey41
.. role:: grey42
.. role:: grey43
.. role:: grey44
.. role:: grey45
.. role:: grey46
.. role:: grey47
.. role:: grey48
.. role:: grey49
.. role:: grey50
.. role:: grey51
.. role:: grey52
.. role:: grey53
.. role:: grey54
.. role:: grey55
.. role:: grey56
.. role:: grey57
.. role:: grey58
.. role:: grey59
.. role:: grey60
.. role:: grey61
.. role:: grey62
.. role:: grey63
.. role:: grey64
.. role:: grey65
.. role:: grey66
.. role:: grey67
.. role:: grey68
.. role:: grey69
.. role:: grey70
.. role:: grey71
.. role:: grey72
.. role:: grey73
.. role:: grey74
.. role:: grey75
.. role:: grey76
.. role:: grey77
.. role:: grey78
.. role:: grey79
.. role:: grey80
.. role:: grey81
.. role:: grey82
.. role:: grey83
.. role:: grey84
.. role:: grey85
.. role:: grey86
.. role:: grey87
.. role:: grey88
.. role:: grey89
.. role:: grey90
.. role:: grey91
.. role:: grey92
.. role:: grey93
.. role:: grey94
.. role:: grey95
.. role:: grey96
.. role:: grey97
.. role:: grey98
.. role:: grey99
.. role:: grey100
.. role:: honeydew
.. role:: honeydew1
.. role:: honeydew2
.. role:: honeydew3
.. role:: honeydew4
.. role:: hotpink
.. role:: hotpink1
.. role:: hotpink2
.. role:: hotpink3
.. role:: hotpink4
.. role:: indianred
.. role:: indianred1
.. role:: indianred2
.. role:: indianred3
.. role:: indianred4
.. role:: indigo
.. role:: ivory
.. role:: ivory1
.. role:: ivory2
.. role:: ivory3
.. role:: ivory4
.. role:: khaki
.. role:: khaki1
.. role:: khaki2
.. role:: khaki3
.. role:: khaki4
.. role:: lavender
.. role:: lavenderblush
.. role:: lavenderblush1
.. role:: lavenderblush2
.. role:: lavenderblush3
.. role:: lavenderblush4
.. role:: lawngreen
.. role:: lemonchiffon
.. role:: lemonchiffon1
.. role:: lemonchiffon2
.. role:: lemonchiffon3
.. role:: lemonchiffon4
.. role:: lightblue
.. role:: lightblue1
.. role:: lightblue2
.. role:: lightblue3
.. role:: lightblue4
.. role:: lightcoral
.. role:: lightcyan
.. role:: lightcyan1
.. role:: lightcyan2
.. role:: lightcyan3
.. role:: lightcyan4
.. role:: lightgoldenrod
.. role:: lightgoldenrod1
.. role:: lightgoldenrod2
.. role:: lightgoldenrod3
.. role:: lightgoldenrod4
.. role:: lightgoldenrodyellow
.. role:: lightgray
.. role:: lightgreen
.. role:: lightgrey
.. role:: lightpink
.. role:: lightpink1
.. role:: lightpink2
.. role:: lightpink3
.. role:: lightpink4
.. role:: lightsalmon
.. role:: lightsalmon1
.. role:: lightsalmon2
.. role:: lightsalmon3
.. role:: lightsalmon4
.. role:: lightseagreen
.. role:: lightskyblue
.. role:: lightskyblue1
.. role:: lightskyblue2
.. role:: lightskyblue3
.. role:: lightskyblue4
.. role:: lightslateblue
.. role:: lightslategray
.. role:: lightslategrey
.. role:: lightsteelblue
.. role:: lightsteelblue1
.. role:: lightsteelblue2
.. role:: lightsteelblue3
.. role:: lightsteelblue4
.. role:: lightyellow
.. role:: lightyellow1
.. role:: lightyellow2
.. role:: lightyellow3
.. role:: lightyellow4
.. role:: limegreen
.. role:: lime
.. role:: linen
.. role:: magenta
.. role:: magenta1
.. role:: magenta2
.. role:: magenta3
.. role:: magenta4
.. role:: maroon
.. role:: maroon1
.. role:: maroon2
.. role:: maroon3
.. role:: maroon4
.. role:: mediumaquamarine
.. role:: mediumblue
.. role:: mediumorchid
.. role:: mediumorchid1
.. role:: mediumorchid2
.. role:: mediumorchid3
.. role:: mediumorchid4
.. role:: mediumpurple
.. role:: mediumpurple1
.. role:: mediumpurple2
.. role:: mediumpurple3
.. role:: mediumpurple4
.. role:: mediumseagreen
.. role:: mediumslateblue
.. role:: mediumspringgreen
.. role:: mediumturquoise
.. role:: mediumvioletred
.. role:: midnightblue
.. role:: mintcream
.. role:: mistyrose
.. role:: mistyrose1
.. role:: mistyrose2
.. role:: mistyrose3
.. role:: mistyrose4
.. role:: moccasin
.. role:: navajowhite
.. role:: navajowhite1
.. role:: navajowhite2
.. role:: navajowhite3
.. role:: navajowhite4
.. role:: navy
.. role:: navyblue
.. role:: oldlace
.. role:: olive
.. role:: olivedrab
.. role:: olivedrab1
.. role:: olivedrab2
.. role:: olivedrab3
.. role:: olivedrab4
.. role:: orange
.. role:: orange1
.. role:: orange2
.. role:: orange3
.. role:: orange4
.. role:: orangered
.. role:: orangered1
.. role:: orangered2
.. role:: orangered3
.. role:: orangered4
.. role:: orchid
.. role:: orchid1
.. role:: orchid2
.. role:: orchid3
.. role:: orchid4
.. role:: palegoldenrod
.. role:: palegreen
.. role:: palegreen1
.. role:: palegreen2
.. role:: palegreen3
.. role:: palegreen4
.. role:: paleturquoise
.. role:: paleturquoise1
.. role:: paleturquoise2
.. role:: paleturquoise3
.. role:: paleturquoise4
.. role:: palevioletred
.. role:: palevioletred1
.. role:: palevioletred2
.. role:: palevioletred3
.. role:: palevioletred4
.. role:: papayawhip
.. role:: peachpuff
.. role:: peachpuff1
.. role:: peachpuff2
.. role:: peachpuff3
.. role:: peachpuff4
.. role:: peru
.. role:: pink
.. role:: pink1
.. role:: pink2
.. role:: pink3
.. role:: pink4
.. role:: plum
.. role:: plum1
.. role:: plum2
.. role:: plum3
.. role:: plum4
.. role:: powderblue
.. role:: purple
.. role:: purple1
.. role:: purple2
.. role:: purple3
.. role:: purple4
.. role:: red
.. role:: red1
.. role:: red2
.. role:: red3
.. role:: red4
.. role:: rosybrown
.. role:: rosybrown1
.. role:: rosybrown2
.. role:: rosybrown3
.. role:: rosybrown4
.. role:: royalblue
.. role:: royalblue1
.. role:: royalblue2
.. role:: royalblue3
.. role:: royalblue4
.. role:: saddlebrown
.. role:: salmon
.. role:: salmon1
.. role:: salmon2
.. role:: salmon3
.. role:: salmon4
.. role:: sandybrown
.. role:: seagreen
.. role:: seagreen1
.. role:: seagreen2
.. role:: seagreen3
.. role:: seagreen4
.. role:: seashell
.. role:: seashell1
.. role:: seashell2
.. role:: seashell3
.. role:: seashell4
.. role:: sienna
.. role:: sienna1
.. role:: sienna2
.. role:: sienna3
.. role:: sienna4
.. role:: silver
.. role:: skyblue
.. role:: skyblue1
.. role:: skyblue2
.. role:: skyblue3
.. role:: skyblue4
.. role:: slateblue
.. role:: slateblue1
.. role:: slateblue2
.. role:: slateblue3
.. role:: slateblue4
.. role:: slategray
.. role:: slategray1
.. role:: slategray2
.. role:: slategray3
.. role:: slategray4
.. role:: slategrey
.. role:: snow
.. role:: snow1
.. role:: snow2
.. role:: snow3
.. role:: snow4
.. role:: springgreen
.. role:: springgreen1
.. role:: springgreen2
.. role:: springgreen3
.. role:: springgreen4
.. role:: steelblue
.. role:: steelblue1
.. role:: steelblue2
.. role:: steelblue3
.. role:: steelblue4
.. role:: tan
.. role:: tan1
.. role:: tan2
.. role:: tan3
.. role:: tan4
.. role:: teal
.. role:: thistle
.. role:: thistle1
.. role:: thistle2
.. role:: thistle3
.. role:: thistle4
.. role:: tomato
.. role:: tomato1
.. role:: tomato2
.. role:: tomato3
.. role:: tomato4
.. role:: turquoise
.. role:: turquoise1
.. role:: turquoise2
.. role:: turquoise3
.. role:: turquoise4
.. role:: violet
.. role:: violetred
.. role:: violetred1
.. role:: violetred2
.. role:: violetred3
.. role:: violetred4
.. role:: wheat
.. role:: wheat1
.. role:: wheat2
.. role:: wheat3
.. role:: wheat4
.. role:: white
.. role:: whitesmoke
.. role:: yellow
.. role:: yellow1
.. role:: yellow2
.. role:: yellow3
.. role:: yellow4
.. role:: yellowgreen

==========================  ======================================================================================================
Name                        Color
==========================  ======================================================================================================
``aliceblue``               :aliceblue:`████████`
``antiquewhite``            :antiquewhite:`████████`
``antiquewhite1``           :antiquewhite1:`████████`
``antiquewhite2``           :antiquewhite2:`████████`
``antiquewhite3``           :antiquewhite3:`████████`
``antiquewhite4``           :antiquewhite4:`████████`
``aqua``                    :aqua:`████████`
``aquamarine``              :aquamarine:`████████`
``aquamarine1``             :aquamarine1:`████████`
``aquamarine2``             :aquamarine2:`████████`
``aquamarine3``             :aquamarine3:`████████`
``aquamarine4``             :aquamarine4:`████████`
``azure``                   :azure:`████████`
``azure1``                  :azure1:`████████`
``azure2``                  :azure2:`████████`
``azure3``                  :azure3:`████████`
``azure4``                  :azure4:`████████`
``beige``                   :beige:`████████`
``bisque``                  :bisque:`████████`
``bisque1``                 :bisque1:`████████`
``bisque2``                 :bisque2:`████████`
``bisque3``                 :bisque3:`████████`
``bisque4``                 :bisque4:`████████`
``black``                   :black:`████████`
``blanchedalmond``          :blanchedalmond:`████████`
``blue``                    :blue:`████████`
``blue1``                   :blue1:`████████`
``blue2``                   :blue2:`████████`
``blue3``                   :blue3:`████████`
``blue4``                   :blue4:`████████`
``blueviolet``              :blueviolet:`████████`
``brown``                   :brown:`████████`
``brown1``                  :brown1:`████████`
``brown2``                  :brown2:`████████`
``brown3``                  :brown3:`████████`
``brown4``                  :brown4:`████████`
``burlywood``               :burlywood:`████████`
``burlywood1``              :burlywood1:`████████`
``burlywood2``              :burlywood2:`████████`
``burlywood3``              :burlywood3:`████████`
``burlywood4``              :burlywood4:`████████`
``cadetblue``               :cadetblue:`████████`
``cadetblue1``              :cadetblue1:`████████`
``cadetblue2``              :cadetblue2:`████████`
``cadetblue3``              :cadetblue3:`████████`
``cadetblue4``              :cadetblue4:`████████`
``chartreuse``              :chartreuse:`████████`
``chartreuse1``             :chartreuse1:`████████`
``chartreuse2``             :chartreuse2:`████████`
``chartreuse3``             :chartreuse3:`████████`
``chartreuse4``             :chartreuse4:`████████`
``chocolate``               :chocolate:`████████`
``chocolate1``              :chocolate1:`████████`
``chocolate2``              :chocolate2:`████████`
``chocolate3``              :chocolate3:`████████`
``chocolate4``              :chocolate4:`████████`
``coral``                   :coral:`████████`
``coral1``                  :coral1:`████████`
``coral2``                  :coral2:`████████`
``coral3``                  :coral3:`████████`
``coral4``                  :coral4:`████████`
``cornflowerblue``          :cornflowerblue:`████████`
``cornsilk``                :cornsilk:`████████`
``cornsilk1``               :cornsilk1:`████████`
``cornsilk2``               :cornsilk2:`████████`
``cornsilk3``               :cornsilk3:`████████`
``cornsilk4``               :cornsilk4:`████████`
``crimson``                 :crimson:`████████`
``cyan``                    :cyan:`████████`
``cyan1``                   :cyan1:`████████`
``cyan2``                   :cyan2:`████████`
``cyan3``                   :cyan3:`████████`
``cyan4``                   :cyan4:`████████`
``darkblue``                :darkblue:`████████`
``darkcyan``                :darkcyan:`████████`
``darkgoldenrod``           :darkgoldenrod:`████████`
``darkgoldenrod1``          :darkgoldenrod1:`████████`
``darkgoldenrod2``          :darkgoldenrod2:`████████`
``darkgoldenrod3``          :darkgoldenrod3:`████████`
``darkgoldenrod4``          :darkgoldenrod4:`████████`
``darkgray``                :darkgray:`████████`
``darkgreen``               :darkgreen:`████████`
``darkgrey``                :darkgrey:`████████`
``darkkhaki``               :darkkhaki:`████████`
``darkmagenta``             :darkmagenta:`████████`
``darkolivegreen``          :darkolivegreen:`████████`
``darkolivegreen1``         :darkolivegreen1:`████████`
``darkolivegreen2``         :darkolivegreen2:`████████`
``darkolivegreen3``         :darkolivegreen3:`████████`
``darkolivegreen4``         :darkolivegreen4:`████████`
``darkorange``              :darkorange:`████████`
``darkorange1``             :darkorange1:`████████`
``darkorange2``             :darkorange2:`████████`
``darkorange3``             :darkorange3:`████████`
``darkorange4``             :darkorange4:`████████`
``darkorchid``              :darkorchid:`████████`
``darkorchid1``             :darkorchid1:`████████`
``darkorchid2``             :darkorchid2:`████████`
``darkorchid3``             :darkorchid3:`████████`
``darkorchid4``             :darkorchid4:`████████`
``darkred``                 :darkred:`████████`
``darksalmon``              :darksalmon:`████████`
``darkseagreen``            :darkseagreen:`████████`
``darkseagreen1``           :darkseagreen1:`████████`
``darkseagreen2``           :darkseagreen2:`████████`
``darkseagreen3``           :darkseagreen3:`████████`
``darkseagreen4``           :darkseagreen4:`████████`
``darkslateblue``           :darkslateblue:`████████`
``darkslategray``           :darkslategray:`████████`
``darkslategray1``          :darkslategray1:`████████`
``darkslategray2``          :darkslategray2:`████████`
``darkslategray3``          :darkslategray3:`████████`
``darkslategray4``          :darkslategray4:`████████`
``darkslategrey``           :darkslategrey:`████████`
``darkturquoise``           :darkturquoise:`████████`
``darkviolet``              :darkviolet:`████████`
``deeppink``                :deeppink:`████████`
``deeppink1``               :deeppink1:`████████`
``deeppink2``               :deeppink2:`████████`
``deeppink3``               :deeppink3:`████████`
``deeppink4``               :deeppink4:`████████`
``deepskyblue``             :deepskyblue:`████████`
``deepskyblue1``            :deepskyblue1:`████████`
``deepskyblue2``            :deepskyblue2:`████████`
``deepskyblue3``            :deepskyblue3:`████████`
``deepskyblue4``            :deepskyblue4:`████████`
``dimgray``                 :dimgray:`████████`
``dimgrey``                 :dimgrey:`████████`
``dodgerblue``              :dodgerblue:`████████`
``dodgerblue1``             :dodgerblue1:`████████`
``dodgerblue2``             :dodgerblue2:`████████`
``dodgerblue3``             :dodgerblue3:`████████`
``dodgerblue4``             :dodgerblue4:`████████`
``firebrick``               :firebrick:`████████`
``firebrick1``              :firebrick1:`████████`
``firebrick2``              :firebrick2:`████████`
``firebrick3``              :firebrick3:`████████`
``firebrick4``              :firebrick4:`████████`
``floralwhite``             :floralwhite:`████████`
``forestgreen``             :forestgreen:`████████`
``fuchsia``                 :fuchsia:`████████`
``gainsboro``               :gainsboro:`████████`
``ghostwhite``              :ghostwhite:`████████`
``gold``                    :gold:`████████`
``gold1``                   :gold1:`████████`
``gold2``                   :gold2:`████████`
``gold3``                   :gold3:`████████`
``gold4``                   :gold4:`████████`
``goldenrod``               :goldenrod:`████████`
``goldenrod1``              :goldenrod1:`████████`
``goldenrod2``              :goldenrod2:`████████`
``goldenrod3``              :goldenrod3:`████████`
``goldenrod4``              :goldenrod4:`████████`
``gray``                    :gray:`████████`
``gray0``                   :gray0:`████████`
``gray1``                   :gray1:`████████`
``gray2``                   :gray2:`████████`
``gray3``                   :gray3:`████████`
``gray4``                   :gray4:`████████`
``gray5``                   :gray5:`████████`
``gray6``                   :gray6:`████████`
``gray7``                   :gray7:`████████`
``gray8``                   :gray8:`████████`
``gray9``                   :gray9:`████████`
``gray10``                  :gray10:`████████`
``gray11``                  :gray11:`████████`
``gray12``                  :gray12:`████████`
``gray13``                  :gray13:`████████`
``gray14``                  :gray14:`████████`
``gray15``                  :gray15:`████████`
``gray16``                  :gray16:`████████`
``gray17``                  :gray17:`████████`
``gray18``                  :gray18:`████████`
``gray19``                  :gray19:`████████`
``gray20``                  :gray20:`████████`
``gray21``                  :gray21:`████████`
``gray22``                  :gray22:`████████`
``gray23``                  :gray23:`████████`
``gray24``                  :gray24:`████████`
``gray25``                  :gray25:`████████`
``gray26``                  :gray26:`████████`
``gray27``                  :gray27:`████████`
``gray28``                  :gray28:`████████`
``gray29``                  :gray29:`████████`
``gray30``                  :gray30:`████████`
``gray31``                  :gray31:`████████`
``gray32``                  :gray32:`████████`
``gray33``                  :gray33:`████████`
``gray34``                  :gray34:`████████`
``gray35``                  :gray35:`████████`
``gray36``                  :gray36:`████████`
``gray37``                  :gray37:`████████`
``gray38``                  :gray38:`████████`
``gray39``                  :gray39:`████████`
``gray40``                  :gray40:`████████`
``gray41``                  :gray41:`████████`
``gray42``                  :gray42:`████████`
``gray43``                  :gray43:`████████`
``gray44``                  :gray44:`████████`
``gray45``                  :gray45:`████████`
``gray46``                  :gray46:`████████`
``gray47``                  :gray47:`████████`
``gray48``                  :gray48:`████████`
``gray49``                  :gray49:`████████`
``gray50``                  :gray50:`████████`
``gray51``                  :gray51:`████████`
``gray52``                  :gray52:`████████`
``gray53``                  :gray53:`████████`
``gray54``                  :gray54:`████████`
``gray55``                  :gray55:`████████`
``gray56``                  :gray56:`████████`
``gray57``                  :gray57:`████████`
``gray58``                  :gray58:`████████`
``gray59``                  :gray59:`████████`
``gray60``                  :gray60:`████████`
``gray61``                  :gray61:`████████`
``gray62``                  :gray62:`████████`
``gray63``                  :gray63:`████████`
``gray64``                  :gray64:`████████`
``gray65``                  :gray65:`████████`
``gray66``                  :gray66:`████████`
``gray67``                  :gray67:`████████`
``gray68``                  :gray68:`████████`
``gray69``                  :gray69:`████████`
``gray70``                  :gray70:`████████`
``gray71``                  :gray71:`████████`
``gray72``                  :gray72:`████████`
``gray73``                  :gray73:`████████`
``gray74``                  :gray74:`████████`
``gray75``                  :gray75:`████████`
``gray76``                  :gray76:`████████`
``gray77``                  :gray77:`████████`
``gray78``                  :gray78:`████████`
``gray79``                  :gray79:`████████`
``gray80``                  :gray80:`████████`
``gray81``                  :gray81:`████████`
``gray82``                  :gray82:`████████`
``gray83``                  :gray83:`████████`
``gray84``                  :gray84:`████████`
``gray85``                  :gray85:`████████`
``gray86``                  :gray86:`████████`
``gray87``                  :gray87:`████████`
``gray88``                  :gray88:`████████`
``gray89``                  :gray89:`████████`
``gray90``                  :gray90:`████████`
``gray91``                  :gray91:`████████`
``gray92``                  :gray92:`████████`
``gray93``                  :gray93:`████████`
``gray94``                  :gray94:`████████`
``gray95``                  :gray95:`████████`
``gray96``                  :gray96:`████████`
``gray97``                  :gray97:`████████`
``gray98``                  :gray98:`████████`
``gray99``                  :gray99:`████████`
``gray100``                 :gray100:`████████`
``green``                   :green:`████████`
``green1``                  :green1:`████████`
``green2``                  :green2:`████████`
``green3``                  :green3:`████████`
``green4``                  :green4:`████████`
``greenyellow``             :greenyellow:`████████`
``grey``                    :grey:`████████`
``grey0``                   :grey0:`████████`
``grey1``                   :grey1:`████████`
``grey2``                   :grey2:`████████`
``grey3``                   :grey3:`████████`
``grey4``                   :grey4:`████████`
``grey5``                   :grey5:`████████`
``grey6``                   :grey6:`████████`
``grey7``                   :grey7:`████████`
``grey8``                   :grey8:`████████`
``grey9``                   :grey9:`████████`
``grey10``                  :grey10:`████████`
``grey11``                  :grey11:`████████`
``grey12``                  :grey12:`████████`
``grey13``                  :grey13:`████████`
``grey14``                  :grey14:`████████`
``grey15``                  :grey15:`████████`
``grey16``                  :grey16:`████████`
``grey17``                  :grey17:`████████`
``grey18``                  :grey18:`████████`
``grey19``                  :grey19:`████████`
``grey20``                  :grey20:`████████`
``grey21``                  :grey21:`████████`
``grey22``                  :grey22:`████████`
``grey23``                  :grey23:`████████`
``grey24``                  :grey24:`████████`
``grey25``                  :grey25:`████████`
``grey26``                  :grey26:`████████`
``grey27``                  :grey27:`████████`
``grey28``                  :grey28:`████████`
``grey29``                  :grey29:`████████`
``grey30``                  :grey30:`████████`
``grey31``                  :grey31:`████████`
``grey32``                  :grey32:`████████`
``grey33``                  :grey33:`████████`
``grey34``                  :grey34:`████████`
``grey35``                  :grey35:`████████`
``grey36``                  :grey36:`████████`
``grey37``                  :grey37:`████████`
``grey38``                  :grey38:`████████`
``grey39``                  :grey39:`████████`
``grey40``                  :grey40:`████████`
``grey41``                  :grey41:`████████`
``grey42``                  :grey42:`████████`
``grey43``                  :grey43:`████████`
``grey44``                  :grey44:`████████`
``grey45``                  :grey45:`████████`
``grey46``                  :grey46:`████████`
``grey47``                  :grey47:`████████`
``grey48``                  :grey48:`████████`
``grey49``                  :grey49:`████████`
``grey50``                  :grey50:`████████`
``grey51``                  :grey51:`████████`
``grey52``                  :grey52:`████████`
``grey53``                  :grey53:`████████`
``grey54``                  :grey54:`████████`
``grey55``                  :grey55:`████████`
``grey56``                  :grey56:`████████`
``grey57``                  :grey57:`████████`
``grey58``                  :grey58:`████████`
``grey59``                  :grey59:`████████`
``grey60``                  :grey60:`████████`
``grey61``                  :grey61:`████████`
``grey62``                  :grey62:`████████`
``grey63``                  :grey63:`████████`
``grey64``                  :grey64:`████████`
``grey65``                  :grey65:`████████`
``grey66``                  :grey66:`████████`
``grey67``                  :grey67:`████████`
``grey68``                  :grey68:`████████`
``grey69``                  :grey69:`████████`
``grey70``                  :grey70:`████████`
``grey71``                  :grey71:`████████`
``grey72``                  :grey72:`████████`
``grey73``                  :grey73:`████████`
``grey74``                  :grey74:`████████`
``grey75``                  :grey75:`████████`
``grey76``                  :grey76:`████████`
``grey77``                  :grey77:`████████`
``grey78``                  :grey78:`████████`
``grey79``                  :grey79:`████████`
``grey80``                  :grey80:`████████`
``grey81``                  :grey81:`████████`
``grey82``                  :grey82:`████████`
``grey83``                  :grey83:`████████`
``grey84``                  :grey84:`████████`
``grey85``                  :grey85:`████████`
``grey86``                  :grey86:`████████`
``grey87``                  :grey87:`████████`
``grey88``                  :grey88:`████████`
``grey89``                  :grey89:`████████`
``grey90``                  :grey90:`████████`
``grey91``                  :grey91:`████████`
``grey92``                  :grey92:`████████`
``grey93``                  :grey93:`████████`
``grey94``                  :grey94:`████████`
``grey95``                  :grey95:`████████`
``grey96``                  :grey96:`████████`
``grey97``                  :grey97:`████████`
``grey98``                  :grey98:`████████`
``grey99``                  :grey99:`████████`
``grey100``                 :grey100:`████████`
``honeydew``                :honeydew:`████████`
``honeydew1``               :honeydew1:`████████`
``honeydew2``               :honeydew2:`████████`
``honeydew3``               :honeydew3:`████████`
``honeydew4``               :honeydew4:`████████`
``hotpink``                 :hotpink:`████████`
``hotpink1``                :hotpink1:`████████`
``hotpink2``                :hotpink2:`████████`
``hotpink3``                :hotpink3:`████████`
``hotpink4``                :hotpink4:`████████`
``indianred``               :indianred:`████████`
``indianred1``              :indianred1:`████████`
``indianred2``              :indianred2:`████████`
``indianred3``              :indianred3:`████████`
``indianred4``              :indianred4:`████████`
``indigo``                  :indigo:`████████`
``ivory``                   :ivory:`████████`
``ivory1``                  :ivory1:`████████`
``ivory2``                  :ivory2:`████████`
``ivory3``                  :ivory3:`████████`
``ivory4``                  :ivory4:`████████`
``khaki``                   :khaki:`████████`
``khaki1``                  :khaki1:`████████`
``khaki2``                  :khaki2:`████████`
``khaki3``                  :khaki3:`████████`
``khaki4``                  :khaki4:`████████`
``lavender``                :lavender:`████████`
``lavenderblush``           :lavenderblush:`████████`
``lavenderblush1``          :lavenderblush1:`████████`
``lavenderblush2``          :lavenderblush2:`████████`
``lavenderblush3``          :lavenderblush3:`████████`
``lavenderblush4``          :lavenderblush4:`████████`
``lawngreen``               :lawngreen:`████████`
``lemonchiffon``            :lemonchiffon:`████████`
``lemonchiffon1``           :lemonchiffon1:`████████`
``lemonchiffon2``           :lemonchiffon2:`████████`
``lemonchiffon3``           :lemonchiffon3:`████████`
``lemonchiffon4``           :lemonchiffon4:`████████`
``lightblue``               :lightblue:`████████`
``lightblue1``              :lightblue1:`████████`
``lightblue2``              :lightblue2:`████████`
``lightblue3``              :lightblue3:`████████`
``lightblue4``              :lightblue4:`████████`
``lightcoral``              :lightcoral:`████████`
``lightcyan``               :lightcyan:`████████`
``lightcyan1``              :lightcyan1:`████████`
``lightcyan2``              :lightcyan2:`████████`
``lightcyan3``              :lightcyan3:`████████`
``lightcyan4``              :lightcyan4:`████████`
``lightgoldenrod``          :lightgoldenrod:`████████`
``lightgoldenrod1``         :lightgoldenrod1:`████████`
``lightgoldenrod2``         :lightgoldenrod2:`████████`
``lightgoldenrod3``         :lightgoldenrod3:`████████`
``lightgoldenrod4``         :lightgoldenrod4:`████████`
``lightgoldenrodyellow``    :lightgoldenrodyellow:`████████`
``lightgray``               :lightgray:`████████`
``lightgreen``              :lightgreen:`████████`
``lightgrey``               :lightgrey:`████████`
``lightpink``               :lightpink:`████████`
``lightpink1``              :lightpink1:`████████`
``lightpink2``              :lightpink2:`████████`
``lightpink3``              :lightpink3:`████████`
``lightpink4``              :lightpink4:`████████`
``lightsalmon``             :lightsalmon:`████████`
``lightsalmon1``            :lightsalmon1:`████████`
``lightsalmon2``            :lightsalmon2:`████████`
``lightsalmon3``            :lightsalmon3:`████████`
``lightsalmon4``            :lightsalmon4:`████████`
``lightseagreen``           :lightseagreen:`████████`
``lightskyblue``            :lightskyblue:`████████`
``lightskyblue1``           :lightskyblue1:`████████`
``lightskyblue2``           :lightskyblue2:`████████`
``lightskyblue3``           :lightskyblue3:`████████`
``lightskyblue4``           :lightskyblue4:`████████`
``lightslateblue``          :lightslateblue:`████████`
``lightslategray``          :lightslategray:`████████`
``lightslategrey``          :lightslategrey:`████████`
``lightsteelblue``          :lightsteelblue:`████████`
``lightsteelblue1``         :lightsteelblue1:`████████`
``lightsteelblue2``         :lightsteelblue2:`████████`
``lightsteelblue3``         :lightsteelblue3:`████████`
``lightsteelblue4``         :lightsteelblue4:`████████`
``lightyellow``             :lightyellow:`████████`
``lightyellow1``            :lightyellow1:`████████`
``lightyellow2``            :lightyellow2:`████████`
``lightyellow3``            :lightyellow3:`████████`
``lightyellow4``            :lightyellow4:`████████`
``lime``                    :lime:`████████`
``limegreen``               :limegreen:`████████`
``linen``                   :linen:`████████`
``magenta``                 :magenta:`████████`
``magenta1``                :magenta1:`████████`
``magenta2``                :magenta2:`████████`
``magenta3``                :magenta3:`████████`
``magenta4``                :magenta4:`████████`
``maroon``                  :maroon:`████████`
``maroon1``                 :maroon1:`████████`
``maroon2``                 :maroon2:`████████`
``maroon3``                 :maroon3:`████████`
``maroon4``                 :maroon4:`████████`
``mediumaquamarine``        :mediumaquamarine:`████████`
``mediumblue``              :mediumblue:`████████`
``mediumorchid``            :mediumorchid:`████████`
``mediumorchid1``           :mediumorchid1:`████████`
``mediumorchid2``           :mediumorchid2:`████████`
``mediumorchid3``           :mediumorchid3:`████████`
``mediumorchid4``           :mediumorchid4:`████████`
``mediumpurple``            :mediumpurple:`████████`
``mediumpurple1``           :mediumpurple1:`████████`
``mediumpurple2``           :mediumpurple2:`████████`
``mediumpurple3``           :mediumpurple3:`████████`
``mediumpurple4``           :mediumpurple4:`████████`
``mediumseagreen``          :mediumseagreen:`████████`
``mediumslateblue``         :mediumslateblue:`████████`
``mediumspringgreen``       :mediumspringgreen:`████████`
``mediumturquoise``         :mediumturquoise:`████████`
``mediumvioletred``         :mediumvioletred:`████████`
``midnightblue``            :midnightblue:`████████`
``mintcream``               :mintcream:`████████`
``mistyrose``               :mistyrose:`████████`
``mistyrose1``              :mistyrose1:`████████`
``mistyrose2``              :mistyrose2:`████████`
``mistyrose3``              :mistyrose3:`████████`
``mistyrose4``              :mistyrose4:`████████`
``moccasin``                :moccasin:`████████`
``navajowhite``             :navajowhite:`████████`
``navajowhite1``            :navajowhite1:`████████`
``navajowhite2``            :navajowhite2:`████████`
``navajowhite3``            :navajowhite3:`████████`
``navajowhite4``            :navajowhite4:`████████`
``navy``                    :navy:`████████`
``navyblue``                :navyblue:`████████`
``oldlace``                 :oldlace:`████████`
``olive``                   :olive:`████████`
``olivedrab``               :olivedrab:`████████`
``olivedrab1``              :olivedrab1:`████████`
``olivedrab2``              :olivedrab2:`████████`
``olivedrab3``              :olivedrab3:`████████`
``olivedrab4``              :olivedrab4:`████████`
``orange``                  :orange:`████████`
``orange1``                 :orange1:`████████`
``orange2``                 :orange2:`████████`
``orange3``                 :orange3:`████████`
``orange4``                 :orange4:`████████`
``orangered``               :orangered:`████████`
``orangered1``              :orangered1:`████████`
``orangered2``              :orangered2:`████████`
``orangered3``              :orangered3:`████████`
``orangered4``              :orangered4:`████████`
``orchid``                  :orchid:`████████`
``orchid1``                 :orchid1:`████████`
``orchid2``                 :orchid2:`████████`
``orchid3``                 :orchid3:`████████`
``orchid4``                 :orchid4:`████████`
``palegoldenrod``           :palegoldenrod:`████████`
``palegreen``               :palegreen:`████████`
``palegreen1``              :palegreen1:`████████`
``palegreen2``              :palegreen2:`████████`
``palegreen3``              :palegreen3:`████████`
``palegreen4``              :palegreen4:`████████`
``paleturquoise``           :paleturquoise:`████████`
``paleturquoise1``          :paleturquoise1:`████████`
``paleturquoise2``          :paleturquoise2:`████████`
``paleturquoise3``          :paleturquoise3:`████████`
``paleturquoise4``          :paleturquoise4:`████████`
``palevioletred``           :palevioletred:`████████`
``palevioletred1``          :palevioletred1:`████████`
``palevioletred2``          :palevioletred2:`████████`
``palevioletred3``          :palevioletred3:`████████`
``palevioletred4``          :palevioletred4:`████████`
``papayawhip``              :papayawhip:`████████`
``peachpuff``               :peachpuff:`████████`
``peachpuff1``              :peachpuff1:`████████`
``peachpuff2``              :peachpuff2:`████████`
``peachpuff3``              :peachpuff3:`████████`
``peachpuff4``              :peachpuff4:`████████`
``peru``                    :peru:`████████`
``pink``                    :pink:`████████`
``pink1``                   :pink1:`████████`
``pink2``                   :pink2:`████████`
``pink3``                   :pink3:`████████`
``pink4``                   :pink4:`████████`
``plum``                    :plum:`████████`
``plum1``                   :plum1:`████████`
``plum2``                   :plum2:`████████`
``plum3``                   :plum3:`████████`
``plum4``                   :plum4:`████████`
``powderblue``              :powderblue:`████████`
``purple``                  :purple:`████████`
``purple1``                 :purple1:`████████`
``purple2``                 :purple2:`████████`
``purple3``                 :purple3:`████████`
``purple4``                 :purple4:`████████`
``red``                     :red:`████████`
``red1``                    :red1:`████████`
``red2``                    :red2:`████████`
``red3``                    :red3:`████████`
``red4``                    :red4:`████████`
``rosybrown``               :rosybrown:`████████`
``rosybrown1``              :rosybrown1:`████████`
``rosybrown2``              :rosybrown2:`████████`
``rosybrown3``              :rosybrown3:`████████`
``rosybrown4``              :rosybrown4:`████████`
``royalblue``               :royalblue:`████████`
``royalblue1``              :royalblue1:`████████`
``royalblue2``              :royalblue2:`████████`
``royalblue3``              :royalblue3:`████████`
``royalblue4``              :royalblue4:`████████`
``saddlebrown``             :saddlebrown:`████████`
``salmon``                  :salmon:`████████`
``salmon1``                 :salmon1:`████████`
``salmon2``                 :salmon2:`████████`
``salmon3``                 :salmon3:`████████`
``salmon4``                 :salmon4:`████████`
``sandybrown``              :sandybrown:`████████`
``seagreen``                :seagreen:`████████`
``seagreen1``               :seagreen1:`████████`
``seagreen2``               :seagreen2:`████████`
``seagreen3``               :seagreen3:`████████`
``seagreen4``               :seagreen4:`████████`
``seashell``                :seashell:`████████`
``seashell1``               :seashell1:`████████`
``seashell2``               :seashell2:`████████`
``seashell3``               :seashell3:`████████`
``seashell4``               :seashell4:`████████`
``sienna``                  :sienna:`████████`
``sienna1``                 :sienna1:`████████`
``sienna2``                 :sienna2:`████████`
``sienna3``                 :sienna3:`████████`
``sienna4``                 :sienna4:`████████`
``silver``                  :silver:`████████`
``skyblue``                 :skyblue:`████████`
``skyblue1``                :skyblue1:`████████`
``skyblue2``                :skyblue2:`████████`
``skyblue3``                :skyblue3:`████████`
``skyblue4``                :skyblue4:`████████`
``slateblue``               :slateblue:`████████`
``slateblue1``              :slateblue1:`████████`
``slateblue2``              :slateblue2:`████████`
``slateblue3``              :slateblue3:`████████`
``slateblue4``              :slateblue4:`████████`
``slategray``               :slategray:`████████`
``slategray1``              :slategray1:`████████`
``slategray2``              :slategray2:`████████`
``slategray3``              :slategray3:`████████`
``slategray4``              :slategray4:`████████`
``slategrey``               :slategrey:`████████`
``snow``                    :snow:`████████`
``snow1``                   :snow1:`████████`
``snow2``                   :snow2:`████████`
``snow3``                   :snow3:`████████`
``snow4``                   :snow4:`████████`
``springgreen``             :springgreen:`████████`
``springgreen1``            :springgreen1:`████████`
``springgreen2``            :springgreen2:`████████`
``springgreen3``            :springgreen3:`████████`
``springgreen4``            :springgreen4:`████████`
``steelblue``               :steelblue:`████████`
``steelblue1``              :steelblue1:`████████`
``steelblue2``              :steelblue2:`████████`
``steelblue3``              :steelblue3:`████████`
``steelblue4``              :steelblue4:`████████`
``tan``                     :tan:`████████`
``tan1``                    :tan1:`████████`
``tan2``                    :tan2:`████████`
``tan3``                    :tan3:`████████`
``tan4``                    :tan4:`████████`
``teal``                    :teal:`████████`
``thistle``                 :thistle:`████████`
``thistle1``                :thistle1:`████████`
``thistle2``                :thistle2:`████████`
``thistle3``                :thistle3:`████████`
``thistle4``                :thistle4:`████████`
``tomato``                  :tomato:`████████`
``tomato1``                 :tomato1:`████████`
``tomato2``                 :tomato2:`████████`
``tomato3``                 :tomato3:`████████`
``tomato4``                 :tomato4:`████████`
``turquoise``               :turquoise:`████████`
``turquoise1``              :turquoise1:`████████`
``turquoise2``              :turquoise2:`████████`
``turquoise3``              :turquoise3:`████████`
``turquoise4``              :turquoise4:`████████`
``violet``                  :violet:`████████`
``violetred``               :violetred:`████████`
``violetred1``              :violetred1:`████████`
``violetred2``              :violetred2:`████████`
``violetred3``              :violetred3:`████████`
``violetred4``              :violetred4:`████████`
``wheat``                   :wheat:`████████`
``wheat1``                  :wheat1:`████████`
``wheat2``                  :wheat2:`████████`
``wheat3``                  :wheat3:`████████`
``wheat4``                  :wheat4:`████████`
``white``                   :white:`████████`
``whitesmoke``              :whitesmoke:`████████`
``yellow``                  :yellow:`████████`
``yellow1``                 :yellow1:`████████`
``yellow2``                 :yellow2:`████████`
``yellow3``                 :yellow3:`████████`
``yellow4``                 :yellow4:`████████`
``yellowgreen``             :yellowgreen:`████████`
==========================  ======================================================================================================

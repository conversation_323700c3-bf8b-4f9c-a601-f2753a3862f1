<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Python Module Index &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/pygame.css?v=a854c6a8" />
    <script src="_static/documentation_options.js?v=0a414f3d"></script>
    <script src="_static/doctools.js?v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="_static/pygame.ico"/>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 


  </head><body>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="#" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Module Index</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
          <div class="body" role="main">
            

   <h1>Python Module Index</h1>

   <div class="modindex-jumpbox">
   <a href="#cap-."><strong>.</strong></a> | 
   <a href="#cap-p"><strong>p</strong></a>
   </div>

   <table class="indextable modindextable">
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-."><td></td><td>
       <strong>.</strong></td><td></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/sdl2_controller.html#module-pygame._sdl2.controller"><code class="xref">pygame._sdl2.controller</code></a></td><td>
       <em>pygame module to work with controllers</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/touch.html#module-pygame._sdl2.touch"><code class="xref">pygame._sdl2.touch</code></a></td><td>
       <em>pygame module to work with touch input</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/sdl2_video.html#module-pygame._sdl2.video"><code class="xref">pygame._sdl2.video</code></a></td><td>
       <em>Experimental pygame module for porting new SDL video systems</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/camera.html#module-pygame.camera"><code class="xref">pygame.camera</code></a></td><td>
       <em>pygame module for camera use</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/cdrom.html#module-pygame.cdrom"><code class="xref">pygame.cdrom</code></a></td><td>
       <em>pygame module for audio cdrom control</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/cursors.html#module-pygame.cursors"><code class="xref">pygame.cursors</code></a></td><td>
       <em>pygame module for cursor resources</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/display.html#module-pygame.display"><code class="xref">pygame.display</code></a></td><td>
       <em>pygame module to control the display window and screen</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/draw.html#module-pygame.draw"><code class="xref">pygame.draw</code></a></td><td>
       <em>pygame module for drawing shapes</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/event.html#module-pygame.event"><code class="xref">pygame.event</code></a></td><td>
       <em>pygame module for interacting with events and queues</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/examples.html#module-pygame.examples"><code class="xref">pygame.examples</code></a></td><td>
       <em>module of example programs</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/fastevent.html#module-pygame.fastevent"><code class="xref">pygame.fastevent</code></a></td><td>
       <em>pygame module for interacting with events and queues from multiple
threads.</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/font.html#module-pygame.font"><code class="xref">pygame.font</code></a></td><td>
       <em>pygame module for loading and rendering fonts</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/freetype.html#module-pygame.freetype"><code class="xref">pygame.freetype</code></a></td><td>
       <em>Enhanced pygame module for loading and rendering computer fonts</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/gfxdraw.html#module-pygame.gfxdraw"><code class="xref">pygame.gfxdraw</code></a></td><td>
       <em>pygame module for drawing shapes</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/image.html#module-pygame.image"><code class="xref">pygame.image</code></a></td><td>
       <em>pygame module for loading and saving images</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/joystick.html#module-pygame.joystick"><code class="xref">pygame.joystick</code></a></td><td>
       <em>Pygame module for interacting with joysticks, gamepads, and trackballs.</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/key.html#module-pygame.key"><code class="xref">pygame.key</code></a></td><td>
       <em>pygame module to work with the keyboard</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/locals.html#module-pygame.locals"><code class="xref">pygame.locals</code></a></td><td>
       <em>pygame constants</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/mask.html#module-pygame.mask"><code class="xref">pygame.mask</code></a></td><td>
       <em>pygame module for image masks.</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/math.html#module-pygame.math"><code class="xref">pygame.math</code></a></td><td>
       <em>pygame module for vector classes</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/midi.html#module-pygame.midi"><code class="xref">pygame.midi</code></a></td><td>
       <em>pygame module for interacting with midi input and output.</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/mixer.html#module-pygame.mixer"><code class="xref">pygame.mixer</code></a></td><td>
       <em>pygame module for loading and playing sounds</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/music.html#module-pygame.mixer.music"><code class="xref">pygame.mixer.music</code></a></td><td>
       <em>pygame module for controlling streamed audio</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/mouse.html#module-pygame.mouse"><code class="xref">pygame.mouse</code></a></td><td>
       <em>pygame module to work with the mouse</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/pixelcopy.html#module-pygame.pixelcopy"><code class="xref">pygame.pixelcopy</code></a></td><td>
       <em>pygame module for general pixel array copying</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/scrap.html#module-pygame.scrap"><code class="xref">pygame.scrap</code></a></td><td>
       <em>pygame module for clipboard support.</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/sndarray.html#module-pygame.sndarray"><code class="xref">pygame.sndarray</code></a></td><td>
       <em>pygame module for accessing sound sample data</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/sprite.html#module-pygame.sprite"><code class="xref">pygame.sprite</code></a></td><td>
       <em>pygame module with basic game object classes</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/surfarray.html#module-pygame.surfarray"><code class="xref">pygame.surfarray</code></a></td><td>
       <em>pygame module for accessing surface pixel data using array interfaces</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/tests.html#module-pygame.tests"><code class="xref">pygame.tests</code></a></td><td>
       <em>Pygame unit test suite package</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/time.html#module-pygame.time"><code class="xref">pygame.time</code></a></td><td>
       <em>pygame module for monitoring time</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/transform.html#module-pygame.transform"><code class="xref">pygame.transform</code></a></td><td>
       <em>pygame module to transform surfaces</em></td></tr>
     <tr class="cg-0">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="ref/pygame.html#module-pygame.version"><code class="xref">pygame.version</code></a></td><td>
       <em>small module containing version information</em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-p"><td></td><td>
       <strong>p</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="ref/pygame.html#module-pygame"><code class="xref">pygame</code></a></td><td>
       <em>the top level pygame package</em></td></tr>
   </table>


            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="#" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Module Index</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>
# 🐍 贪吃蛇桌面版

基于Python + PyGame开发的经典贪吃蛇游戏桌面版本。

## 🚀 快速开始

### 1. 安装依赖

确保你的系统已安装Python 3.6+，然后安装PyGame：

```bash
pip install pygame
```

### 2. 运行游戏

```bash
python snake_game.py
```

## 🎮 游戏控制

### 主菜单
- **数字键 1-5**：选择游戏速度（1=非常慢，5=非常快）
- **空格键**：开始游戏

### 游戏中
- **方向键**：控制蛇的移动方向
- **空格键**：暂停/继续游戏
- **R键**：重新开始游戏
- **ESC键**：退出游戏

### 游戏结束
- **空格键**：重新开始
- **M键**：返回主菜单

## 🎯 游戏特色

### 视觉效果
- **蛇头眼睛**：根据移动方向动态调整
- **普通食物**：红色圆形，带有脉动效果
- **特殊食物**：金色星形，旋转动画，10%概率出现，50分
- **网格背景**：清晰的游戏区域划分

### 游戏功能
- **5档速度**：从非常慢到非常快，适合不同水平玩家
- **分数系统**：普通食物10分，特殊食物50分
- **最高分记录**：自动保存到本地文件
- **设置保存**：游戏速度等设置自动保存

### 界面设计
- **主菜单**：显示最高分和控制说明
- **游戏界面**：实时显示分数、最高分、蛇长度、当前速度
- **暂停界面**：半透明覆盖层
- **游戏结束**：显示最终分数，新纪录特殊提示

## 📁 文件说明

- `snake_game.py` - 主游戏文件，包含完整游戏逻辑
- `high_score.txt` - 最高分记录文件（自动生成）
- `settings.json` - 游戏设置文件（自动生成）

## 🔧 技术特点

### 代码结构
- **面向对象设计**：Snake、Food、GameData、SnakeGame等类
- **枚举类型**：Direction、GameState、FoodType等
- **数据类**：Position、GameConfig等
- **模块化**：功能分离，便于维护和扩展

### 性能优化
- **高效碰撞检测**：使用位置比较而非像素检测
- **智能食物生成**：避免与蛇身重叠
- **帧率控制**：根据难度调整游戏速度

## 🎨 自定义配置

可以通过修改 `GameConfig` 类来自定义游戏参数：

```python
@dataclass
class GameConfig:
    WINDOW_WIDTH: int = 800      # 窗口宽度
    WINDOW_HEIGHT: int = 600     # 窗口高度
    GAME_WIDTH: int = 600        # 游戏区域宽度
    GAME_HEIGHT: int = 400       # 游戏区域高度
    GRID_SIZE: int = 20          # 网格大小
    
    # 颜色配置
    GREEN: Tuple[int, int, int] = (76, 175, 80)    # 蛇身颜色
    DARK_GREEN: Tuple[int, int, int] = (45, 90, 39) # 蛇头颜色
    RED: Tuple[int, int, int] = (255, 87, 34)      # 普通食物颜色
    GOLD: Tuple[int, int, int] = (255, 215, 0)     # 特殊食物颜色
    
    # 游戏速度（FPS）
    SPEED_VERY_SLOW: int = 4     # 非常慢
    SPEED_SLOW: int = 6          # 慢
    SPEED_NORMAL: int = 8        # 普通
    SPEED_FAST: int = 12         # 快
    SPEED_VERY_FAST: int = 16    # 非常快
```

## 🐛 故障排除

### 常见问题

1. **ImportError: No module named 'pygame'**
   ```bash
   pip install pygame
   ```

2. **游戏窗口无法显示**
   - 确保系统支持图形界面
   - 检查Python版本是否为3.6+

3. **游戏运行缓慢**
   - 尝试降低游戏速度
   - 检查系统资源占用

### 系统要求
- Python 3.6+
- PyGame 2.0+
- 支持图形界面的操作系统

## 📝 更新日志

### v1.0.0
- 完整的贪吃蛇游戏功能
- 5档速度选择
- 特殊食物系统
- 最高分记录
- 设置保存功能
- 精美的视觉效果

---

**享受游戏吧！** 🎮

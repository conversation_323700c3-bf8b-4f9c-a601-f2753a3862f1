#!/usr/bin/env python3
"""
贪吃蛇游戏测试脚本
验证游戏组件是否正常工作
"""

def test_imports():
    """测试导入"""
    print("测试导入模块...")
    try:
        import pygame
        print(f"✅ PyGame导入成功，版本: {pygame.version.ver}")
    except ImportError as e:
        print(f"❌ PyGame导入失败: {e}")
        return False
    
    try:
        from snake_game import Snake, Food, GameConfig, Position, Direction, FoodType
        print("✅ 游戏模块导入成功")
    except ImportError as e:
        print(f"❌ 游戏模块导入失败: {e}")
        return False
    
    return True

def test_game_components():
    """测试游戏组件"""
    print("\n测试游戏组件...")
    
    try:
        from snake_game import Snake, Food, Position, Direction
        
        # 测试蛇
        snake = Snake()
        print(f"✅ 蛇初始化成功，初始长度: {len(snake.body)}")
        
        # 测试移动
        initial_head = snake.body[0]
        snake.move()
        new_head = snake.body[0]
        print(f"✅ 蛇移动测试成功: {initial_head.x},{initial_head.y} -> {new_head.x},{new_head.y}")
        
        # 测试食物
        food = Food()
        food.generate(snake.body)
        print(f"✅ 食物生成成功，位置: ({food.position.x}, {food.position.y})")
        
        # 测试碰撞检测
        collision = snake.check_wall_collision()
        print(f"✅ 碰撞检测测试成功，撞墙: {collision}")
        
        return True
        
    except Exception as e:
        print(f"❌ 游戏组件测试失败: {e}")
        return False

def test_pygame_init():
    """测试PyGame初始化"""
    print("\n测试PyGame初始化...")
    
    try:
        import pygame
        pygame.init()
        
        # 测试创建窗口（不显示）
        screen = pygame.display.set_mode((100, 100))
        print("✅ PyGame窗口创建成功")
        
        # 测试字体
        font = pygame.font.Font(None, 24)
        print("✅ PyGame字体加载成功")
        
        pygame.quit()
        print("✅ PyGame清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ PyGame初始化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🐍 贪吃蛇游戏测试")
    print("=" * 30)
    
    all_passed = True
    
    # 测试导入
    if not test_imports():
        all_passed = False
    
    # 测试游戏组件
    if not test_game_components():
        all_passed = False
    
    # 测试PyGame初始化
    if not test_pygame_init():
        all_passed = False
    
    print("\n" + "=" * 30)
    if all_passed:
        print("🎉 所有测试通过！游戏可以正常运行")
        print("\n运行游戏:")
        print("python snake_game.py")
        print("或者:")
        print("python run_game.py")
    else:
        print("❌ 部分测试失败，请检查环境配置")
        print("\n建议:")
        print("1. 确保Python版本 >= 3.6")
        print("2. 安装PyGame: pip install pygame")
        print("3. 检查系统是否支持图形界面")

if __name__ == "__main__":
    main()

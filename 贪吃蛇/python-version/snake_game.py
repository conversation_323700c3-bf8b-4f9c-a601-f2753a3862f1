#!/usr/bin/env python3
"""
🐍 贪吃蛇桌面版游戏
使用Python + PyGame实现的经典贪吃蛇游戏

运行方式：python snake_game.py
依赖：pip install pygame

控制方式：
- 方向键：控制蛇的移动
- 空格键：暂停/继续游戏
- R键：重新开始游戏
- ESC键：退出游戏
"""

import pygame
import random
import sys
import json
import os
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional

# 初始化pygame
pygame.init()

# 游戏配置
@dataclass
class GameConfig:
    # 窗口设置
    WINDOW_WIDTH: int = 800
    WINDOW_HEIGHT: int = 600
    GAME_WIDTH: int = 600
    GAME_HEIGHT: int = 400
    GRID_SIZE: int = 20
    
    # 颜色配置
    BLACK: Tuple[int, int, int] = (0, 0, 0)
    WHITE: Tuple[int, int, int] = (255, 255, 255)
    GREEN: Tuple[int, int, int] = (76, 175, 80)
    DARK_GREEN: Tuple[int, int, int] = (45, 90, 39)
    RED: Tuple[int, int, int] = (255, 87, 34)
    BLUE: Tuple[int, int, int] = (33, 150, 243)
    GRAY: Tuple[int, int, int] = (158, 158, 158)
    LIGHT_GRAY: Tuple[int, int, int] = (245, 245, 245)
    GOLD: Tuple[int, int, int] = (255, 215, 0)
    
    # 游戏速度（FPS）
    SPEED_VERY_SLOW: int = 4
    SPEED_SLOW: int = 6
    SPEED_NORMAL: int = 8
    SPEED_FAST: int = 12
    SPEED_VERY_FAST: int = 16
    
    # 初始设置
    INITIAL_SNAKE_LENGTH: int = 3
    INITIAL_SPEED: int = SPEED_VERY_SLOW

config = GameConfig()

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"

class FoodType(Enum):
    NORMAL = "normal"
    SPECIAL = "special"

@dataclass
class Position:
    x: int
    y: int
    
    def __add__(self, other):
        return Position(self.x + other.x, self.y + other.y)
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y

class Food:
    def __init__(self):
        self.position = Position(0, 0)
        self.type = FoodType.NORMAL
        self.value = 10
        self.animation_frame = 0
        
    def generate(self, snake_positions: List[Position]):
        """生成新的食物位置，避免与蛇身重叠"""
        max_x = config.GAME_WIDTH // config.GRID_SIZE - 1
        max_y = config.GAME_HEIGHT // config.GRID_SIZE - 1
        
        attempts = 0
        while attempts < 100:  # 防止无限循环
            x = random.randint(0, max_x)
            y = random.randint(0, max_y)
            new_pos = Position(x, y)
            
            if new_pos not in snake_positions:
                self.position = new_pos
                break
            attempts += 1
        
        # 10% 概率生成特殊食物
        if random.random() < 0.1:
            self.type = FoodType.SPECIAL
            self.value = 50
        else:
            self.type = FoodType.NORMAL
            self.value = 10
            
        self.animation_frame = 0
    
    def draw(self, screen: pygame.Surface):
        """绘制食物"""
        self.animation_frame += 0.2
        
        pixel_x = self.position.x * config.GRID_SIZE
        pixel_y = self.position.y * config.GRID_SIZE
        center_x = pixel_x + config.GRID_SIZE // 2
        center_y = pixel_y + config.GRID_SIZE // 2
        
        if self.type == FoodType.SPECIAL:
            # 特殊食物：旋转的星形
            self._draw_star(screen, center_x, center_y)
        else:
            # 普通食物：脉动的圆形
            pulse_scale = 1 + 0.1 * abs(pygame.math.Vector2(1, 0).rotate(self.animation_frame * 10).x)
            radius = int(config.GRID_SIZE // 3 * pulse_scale)
            pygame.draw.circle(screen, config.RED, (center_x, center_y), radius)
            # 高光效果
            highlight_pos = (center_x - radius // 3, center_y - radius // 3)
            pygame.draw.circle(screen, config.WHITE, highlight_pos, radius // 3)
    
    def _draw_star(self, screen: pygame.Surface, center_x: int, center_y: int):
        """绘制星形特殊食物"""
        points = []
        outer_radius = config.GRID_SIZE // 2 - 2
        inner_radius = outer_radius // 2
        
        for i in range(10):  # 5个尖角，每个尖角2个点
            angle = i * 36 - 90 + self.animation_frame * 2  # 旋转动画
            radius = outer_radius if i % 2 == 0 else inner_radius
            
            x = center_x + radius * pygame.math.Vector2(1, 0).rotate(angle).x
            y = center_y + radius * pygame.math.Vector2(1, 0).rotate(angle).y
            points.append((x, y))
        
        pygame.draw.polygon(screen, config.GOLD, points)
        pygame.draw.polygon(screen, config.RED, points, 2)

class Snake:
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置蛇到初始状态"""
        start_x = config.GAME_WIDTH // config.GRID_SIZE // 2
        start_y = config.GAME_HEIGHT // config.GRID_SIZE // 2
        
        self.body = [
            Position(start_x, start_y),
            Position(start_x - 1, start_y),
            Position(start_x - 2, start_y)
        ]
        self.direction = Direction.RIGHT
        self.next_direction = Direction.RIGHT
        self.grow_pending = False
    
    def set_direction(self, new_direction: Direction):
        """设置移动方向，防止反向移动"""
        current_dir = self.direction.value
        new_dir = new_direction.value
        
        # 防止反向移动
        if (current_dir[0] + new_dir[0] != 0) or (current_dir[1] + new_dir[1] != 0):
            self.next_direction = new_direction
    
    def move(self):
        """移动蛇"""
        self.direction = self.next_direction
        
        # 计算新头部位置
        head = self.body[0]
        direction_offset = Position(*self.direction.value)
        new_head = head + direction_offset
        
        # 添加新头部
        self.body.insert(0, new_head)
        
        # 如果不需要增长，移除尾部
        if not self.grow_pending:
            self.body.pop()
        else:
            self.grow_pending = False
    
    def grow(self):
        """让蛇增长"""
        self.grow_pending = True
    
    def check_wall_collision(self) -> bool:
        """检查是否撞墙"""
        head = self.body[0]
        max_x = config.GAME_WIDTH // config.GRID_SIZE - 1
        max_y = config.GAME_HEIGHT // config.GRID_SIZE - 1
        
        return head.x < 0 or head.x > max_x or head.y < 0 or head.y > max_y
    
    def check_self_collision(self) -> bool:
        """检查是否撞到自己"""
        head = self.body[0]
        return head in self.body[1:]
    
    def check_food_collision(self, food: Food) -> bool:
        """检查是否吃到食物"""
        return self.body[0] == food.position
    
    def draw(self, screen: pygame.Surface):
        """绘制蛇"""
        for i, segment in enumerate(self.body):
            pixel_x = segment.x * config.GRID_SIZE
            pixel_y = segment.y * config.GRID_SIZE
            
            if i == 0:  # 蛇头
                pygame.draw.rect(screen, config.DARK_GREEN, 
                               (pixel_x + 1, pixel_y + 1, 
                                config.GRID_SIZE - 2, config.GRID_SIZE - 2))
                # 绘制眼睛
                self._draw_eyes(screen, pixel_x, pixel_y)
            else:  # 蛇身
                pygame.draw.rect(screen, config.GREEN, 
                               (pixel_x + 1, pixel_y + 1, 
                                config.GRID_SIZE - 2, config.GRID_SIZE - 2))
    
    def _draw_eyes(self, screen: pygame.Surface, x: int, y: int):
        """绘制蛇的眼睛"""
        eye_size = 3
        eye_offset = 6
        
        # 根据移动方向调整眼睛位置
        if self.direction == Direction.RIGHT:
            left_eye = (x + config.GRID_SIZE - eye_offset, y + eye_offset)
            right_eye = (x + config.GRID_SIZE - eye_offset, y + config.GRID_SIZE - eye_offset)
        elif self.direction == Direction.LEFT:
            left_eye = (x + eye_offset, y + eye_offset)
            right_eye = (x + eye_offset, y + config.GRID_SIZE - eye_offset)
        elif self.direction == Direction.UP:
            left_eye = (x + eye_offset, y + eye_offset)
            right_eye = (x + config.GRID_SIZE - eye_offset, y + eye_offset)
        else:  # DOWN
            left_eye = (x + eye_offset, y + config.GRID_SIZE - eye_offset)
            right_eye = (x + config.GRID_SIZE - eye_offset, y + config.GRID_SIZE - eye_offset)
        
        pygame.draw.circle(screen, config.WHITE, left_eye, eye_size)
        pygame.draw.circle(screen, config.WHITE, right_eye, eye_size)
        pygame.draw.circle(screen, config.BLACK, left_eye, 1)
        pygame.draw.circle(screen, config.BLACK, right_eye, 1)

class GameData:
    """游戏数据管理"""
    def __init__(self):
        self.high_score = self.load_high_score()
        self.settings = self.load_settings()

    def load_high_score(self) -> int:
        """加载最高分"""
        try:
            if os.path.exists('high_score.txt'):
                with open('high_score.txt', 'r') as f:
                    return int(f.read().strip())
        except:
            pass
        return 0

    def save_high_score(self, score: int) -> bool:
        """保存最高分，返回是否创造新纪录"""
        if score > self.high_score:
            self.high_score = score
            try:
                with open('high_score.txt', 'w') as f:
                    f.write(str(score))
                return True
            except:
                pass
        return False

    def load_settings(self) -> dict:
        """加载游戏设置"""
        default_settings = {
            'speed': config.INITIAL_SPEED,
            'sound_enabled': True
        }
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r') as f:
                    saved_settings = json.load(f)
                    return {**default_settings, **saved_settings}
        except:
            pass
        return default_settings

    def save_settings(self, settings: dict):
        """保存游戏设置"""
        try:
            with open('settings.json', 'w') as f:
                json.dump(settings, f)
        except:
            pass

class SnakeGame:
    """贪吃蛇游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((config.WINDOW_WIDTH, config.WINDOW_HEIGHT))
        pygame.display.set_caption("🐍 贪吃蛇桌面版")
        self.clock = pygame.time.Clock()
        self.font_large = pygame.font.Font(None, 48)
        self.font_medium = pygame.font.Font(None, 32)
        self.font_small = pygame.font.Font(None, 24)

        # 游戏对象
        self.snake = Snake()
        self.food = Food()
        self.game_data = GameData()

        # 游戏状态
        self.state = GameState.MENU
        self.score = 0
        self.game_speed = self.game_data.settings['speed']

        # 初始化食物
        self.food.generate(self.snake.body)

        # 游戏区域偏移（居中显示）
        self.game_offset_x = (config.WINDOW_WIDTH - config.GAME_WIDTH) // 2
        self.game_offset_y = 50

    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False

            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False

                if self.state == GameState.MENU:
                    if event.key == pygame.K_SPACE:
                        self.start_game()
                    elif event.key == pygame.K_1:
                        self.game_speed = config.SPEED_VERY_SLOW
                    elif event.key == pygame.K_2:
                        self.game_speed = config.SPEED_SLOW
                    elif event.key == pygame.K_3:
                        self.game_speed = config.SPEED_NORMAL
                    elif event.key == pygame.K_4:
                        self.game_speed = config.SPEED_FAST
                    elif event.key == pygame.K_5:
                        self.game_speed = config.SPEED_VERY_FAST

                elif self.state == GameState.PLAYING:
                    if event.key == pygame.K_UP:
                        self.snake.set_direction(Direction.UP)
                    elif event.key == pygame.K_DOWN:
                        self.snake.set_direction(Direction.DOWN)
                    elif event.key == pygame.K_LEFT:
                        self.snake.set_direction(Direction.LEFT)
                    elif event.key == pygame.K_RIGHT:
                        self.snake.set_direction(Direction.RIGHT)
                    elif event.key == pygame.K_SPACE:
                        self.state = GameState.PAUSED
                    elif event.key == pygame.K_r:
                        self.restart_game()

                elif self.state == GameState.PAUSED:
                    if event.key == pygame.K_SPACE:
                        self.state = GameState.PLAYING
                    elif event.key == pygame.K_r:
                        self.restart_game()

                elif self.state == GameState.GAME_OVER:
                    if event.key == pygame.K_SPACE or event.key == pygame.K_r:
                        self.restart_game()
                    elif event.key == pygame.K_m:
                        self.state = GameState.MENU

        return True

    def start_game(self):
        """开始游戏"""
        self.state = GameState.PLAYING
        self.restart_game()

    def restart_game(self):
        """重新开始游戏"""
        self.snake.reset()
        self.food.generate(self.snake.body)
        self.score = 0
        self.state = GameState.PLAYING

    def update(self):
        """更新游戏状态"""
        if self.state != GameState.PLAYING:
            return

        # 移动蛇
        self.snake.move()

        # 检查食物碰撞
        if self.snake.check_food_collision(self.food):
            self.score += self.food.value
            self.snake.grow()
            self.food.generate(self.snake.body)

        # 检查碰撞
        if self.snake.check_wall_collision() or self.snake.check_self_collision():
            self.game_over()

    def game_over(self):
        """游戏结束"""
        self.state = GameState.GAME_OVER
        is_new_record = self.game_data.save_high_score(self.score)

        # 保存设置
        settings = {
            'speed': self.game_speed,
            'sound_enabled': self.game_data.settings.get('sound_enabled', True)
        }
        self.game_data.save_settings(settings)

    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(config.WHITE)

        if self.state == GameState.MENU:
            self.draw_menu()
        elif self.state == GameState.PLAYING:
            self.draw_game()
        elif self.state == GameState.PAUSED:
            self.draw_game()
            self.draw_pause_overlay()
        elif self.state == GameState.GAME_OVER:
            self.draw_game()
            self.draw_game_over_overlay()

        pygame.display.flip()

    def draw_menu(self):
        """绘制主菜单"""
        # 标题
        title_text = self.font_large.render("🐍 贪吃蛇桌面版", True, config.DARK_GREEN)
        title_rect = title_text.get_rect(center=(config.WINDOW_WIDTH // 2, 100))
        self.screen.blit(title_text, title_rect)

        # 最高分
        high_score_text = self.font_medium.render(f"最高分: {self.game_data.high_score}", True, config.BLUE)
        high_score_rect = high_score_text.get_rect(center=(config.WINDOW_WIDTH // 2, 150))
        self.screen.blit(high_score_text, high_score_rect)

        # 速度选择
        speed_title = self.font_medium.render("选择难度:", True, config.BLACK)
        speed_title_rect = speed_title.get_rect(center=(config.WINDOW_WIDTH // 2, 220))
        self.screen.blit(speed_title, speed_title_rect)

        speeds = [
            ("1 - 非常慢", config.SPEED_VERY_SLOW),
            ("2 - 慢", config.SPEED_SLOW),
            ("3 - 普通", config.SPEED_NORMAL),
            ("4 - 快", config.SPEED_FAST),
            ("5 - 非常快", config.SPEED_VERY_FAST)
        ]

        for i, (text, speed) in enumerate(speeds):
            color = config.GREEN if speed == self.game_speed else config.GRAY
            speed_text = self.font_small.render(text, True, color)
            speed_rect = speed_text.get_rect(center=(config.WINDOW_WIDTH // 2, 260 + i * 30))
            self.screen.blit(speed_text, speed_rect)

        # 开始提示
        start_text = self.font_medium.render("按空格键开始游戏", True, config.DARK_GREEN)
        start_rect = start_text.get_rect(center=(config.WINDOW_WIDTH // 2, 450))
        self.screen.blit(start_text, start_rect)

        # 控制说明
        controls = [
            "方向键: 控制移动",
            "空格键: 暂停/继续",
            "R键: 重新开始",
            "ESC键: 退出游戏"
        ]

        for i, control in enumerate(controls):
            control_text = self.font_small.render(control, True, config.GRAY)
            control_rect = control_text.get_rect(center=(config.WINDOW_WIDTH // 2, 500 + i * 25))
            self.screen.blit(control_text, control_rect)

    def draw_game(self):
        """绘制游戏画面"""
        # 绘制游戏区域背景
        game_rect = pygame.Rect(self.game_offset_x, self.game_offset_y,
                               config.GAME_WIDTH, config.GAME_HEIGHT)
        pygame.draw.rect(self.screen, config.LIGHT_GRAY, game_rect)
        pygame.draw.rect(self.screen, config.BLACK, game_rect, 2)

        # 绘制网格
        self.draw_grid()

        # 设置裁剪区域
        self.screen.set_clip(game_rect)

        # 保存当前变换
        old_clip = self.screen.get_clip()

        # 临时移动坐标系
        temp_surface = pygame.Surface((config.GAME_WIDTH, config.GAME_HEIGHT))
        temp_surface.fill(config.LIGHT_GRAY)

        # 在临时表面上绘制游戏对象
        self.snake.draw(temp_surface)
        self.food.draw(temp_surface)

        # 将临时表面绘制到主屏幕
        self.screen.blit(temp_surface, (self.game_offset_x, self.game_offset_y))

        # 恢复裁剪区域
        self.screen.set_clip(old_clip)

        # 绘制UI信息
        self.draw_ui()

    def draw_grid(self):
        """绘制网格"""
        for x in range(0, config.GAME_WIDTH + 1, config.GRID_SIZE):
            start_pos = (self.game_offset_x + x, self.game_offset_y)
            end_pos = (self.game_offset_x + x, self.game_offset_y + config.GAME_HEIGHT)
            pygame.draw.line(self.screen, config.GRAY, start_pos, end_pos, 1)

        for y in range(0, config.GAME_HEIGHT + 1, config.GRID_SIZE):
            start_pos = (self.game_offset_x, self.game_offset_y + y)
            end_pos = (self.game_offset_x + config.GAME_WIDTH, self.game_offset_y + y)
            pygame.draw.line(self.screen, config.GRAY, start_pos, end_pos, 1)

    def draw_ui(self):
        """绘制UI信息"""
        # 当前分数
        score_text = self.font_medium.render(f"分数: {self.score}", True, config.BLACK)
        self.screen.blit(score_text, (20, 20))

        # 最高分
        high_score_text = self.font_medium.render(f"最高分: {self.game_data.high_score}", True, config.BLUE)
        self.screen.blit(high_score_text, (20, 50))

        # 蛇长度
        length_text = self.font_small.render(f"长度: {len(self.snake.body)}", True, config.GRAY)
        self.screen.blit(length_text, (config.WINDOW_WIDTH - 120, 20))

        # 速度显示
        speed_names = {
            config.SPEED_VERY_SLOW: "非常慢",
            config.SPEED_SLOW: "慢",
            config.SPEED_NORMAL: "普通",
            config.SPEED_FAST: "快",
            config.SPEED_VERY_FAST: "非常快"
        }
        speed_text = self.font_small.render(f"速度: {speed_names.get(self.game_speed, '普通')}", True, config.GRAY)
        self.screen.blit(speed_text, (config.WINDOW_WIDTH - 120, 45))

    def draw_pause_overlay(self):
        """绘制暂停覆盖层"""
        overlay = pygame.Surface((config.WINDOW_WIDTH, config.WINDOW_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(config.BLACK)
        self.screen.blit(overlay, (0, 0))

        pause_text = self.font_large.render("游戏暂停", True, config.WHITE)
        pause_rect = pause_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 - 30))
        self.screen.blit(pause_text, pause_rect)

        continue_text = self.font_medium.render("按空格键继续", True, config.WHITE)
        continue_rect = continue_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 + 20))
        self.screen.blit(continue_text, continue_rect)

    def draw_game_over_overlay(self):
        """绘制游戏结束覆盖层"""
        overlay = pygame.Surface((config.WINDOW_WIDTH, config.WINDOW_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(config.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 检查是否创造新纪录
        is_new_record = self.score == self.game_data.high_score and self.score > 0

        if is_new_record:
            title_text = self.font_large.render("🎉 新纪录！", True, config.GOLD)
        else:
            title_text = self.font_large.render("游戏结束", True, config.WHITE)

        title_rect = title_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 - 60))
        self.screen.blit(title_text, title_rect)

        score_text = self.font_medium.render(f"最终分数: {self.score}", True, config.WHITE)
        score_rect = score_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 - 20))
        self.screen.blit(score_text, score_rect)

        restart_text = self.font_medium.render("按空格键重新开始", True, config.WHITE)
        restart_rect = restart_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 + 20))
        self.screen.blit(restart_text, restart_rect)

        menu_text = self.font_small.render("按M键返回主菜单", True, config.GRAY)
        menu_rect = menu_text.get_rect(center=(config.WINDOW_WIDTH // 2, config.WINDOW_HEIGHT // 2 + 50))
        self.screen.blit(menu_text, menu_rect)

    def run(self):
        """运行游戏主循环"""
        running = True

        while running:
            # 处理事件
            running = self.handle_events()

            # 更新游戏状态
            self.update()

            # 绘制画面
            self.draw()

            # 控制帧率
            self.clock.tick(self.game_speed)

        pygame.quit()
        sys.exit()

def main():
    """主函数"""
    try:
        game = SnakeGame()
        game.run()
    except Exception as e:
        print(f"游戏运行出错: {e}")
        pygame.quit()
        sys.exit(1)

if __name__ == "__main__":
    main()

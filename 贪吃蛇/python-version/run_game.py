#!/usr/bin/env python3
"""
贪吃蛇游戏启动器
自动检查依赖并启动游戏
"""

import sys
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("❌ 错误：需要Python 3.6或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✅ Python版本检查通过：{sys.version.split()[0]}")
    return True

def check_pygame():
    """检查PyGame是否已安装"""
    try:
        import pygame
        print(f"✅ PyGame已安装：{pygame.version.ver}")
        return True
    except ImportError:
        print("❌ PyGame未安装")
        return False

def install_pygame():
    """安装PyGame"""
    print("正在安装PyGame...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
        print("✅ PyGame安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyGame安装失败")
        print("请手动运行：pip install pygame")
        return False

def main():
    """主函数"""
    print("🐍 贪吃蛇游戏启动器")
    print("=" * 30)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查PyGame
    if not check_pygame():
        print("\n是否自动安装PyGame？(y/n): ", end="")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes', '是']:
            if not install_pygame():
                input("按回车键退出...")
                return
        else:
            print("请手动安装PyGame：pip install pygame")
            input("按回车键退出...")
            return
    
    # 启动游戏
    print("\n🎮 启动游戏...")
    try:
        from snake_game import main as game_main
        game_main()
    except Exception as e:
        print(f"❌ 游戏启动失败：{e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
